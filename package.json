{"name": "revalu", "version": "1.0.0", "description": "Real estate valuation and analysis platform", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test:supabase": "node scripts/test-supabase.js", "dev": "pnpm --parallel --recursive dev", "build": "pnpm --recursive build", "lint": "pnpm --recursive lint", "format": "prettier --write \"**/*.{js,ts,tsx,json,md,yaml,yml}\"", "clean": "pnpm --recursive clean", "db:seed": "cd packages/database && pnpm tsx src/seed.ts"}, "keywords": ["real-estate", "valuation", "monorepo", "typescript"], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "devDependencies": {"prettier": "^3.2.5"}, "dependencies": {"@supabase/supabase-js": "^2.39.3", "dotenv": "^16.4.1"}}