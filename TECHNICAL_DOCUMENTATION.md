# Revalu - Technical Documentation

## SaaS Architecture Overview

### Application Structure
- **Public Marketing Website**: Unauthenticated pages for lead generation and conversion
- **Private Application**: Authenticated property intelligence platform
- **Route Protection**: Middleware-based authentication guards
- **Feature Gating**: Plan-based access control with upgrade prompts

### Navigation Architecture
- **Public Navigation**: Logo | Features | Pricing | About | Contact | Login | Sign Up
- **Authenticated Navigation**: Logo | Dashboard | Search | Market Intelligence | Portfolio | Reports | [User Avatar]
- **User Dropdown**: Profile, Settings, Billing, Help, Logout

## Database Schema Overview

### Core Entities (13 tables)
- **User & Authentication**: `User`
- **Property Data**: `Property`, `Valuation`, `Prediction`
- **Geographic**: `Suburb`, `MarketTrend`
- **Development**: `DevelopmentApplication`
- **Location Services**: `School`, `TransportStop`, `CrimeStatistic`
- **User Interactions**: `UserSearch`, `SavedProperty`, `Report`

### New Entities - Phase 1 (8 tables)
- **Alerts**: `Alert`, `AlertHistory`
- **Analysis**: `Comparison`, `Amalgamation`
- **Data Integration**: `DataSource`, `WebhookData`
- **Infrastructure**: `InfrastructureProject`
- **AI Engine**: `EventUpliftFactor`, `PropertyIntelligence`

## Route Protection & Access Control

### Public Routes (No Authentication Required)
```
GET  /                    # Homepage
GET  /pricing            # Pricing page
GET  /features           # Features page
GET  /about              # About page
GET  /contact            # Contact page
GET  /login              # Login page
GET  /signup             # Registration page
```

### Protected Routes (Authentication Required)
```
GET  /dashboard          # User dashboard (default after login)
GET  /search             # Property search
GET  /property/:id       # Property intelligence dashboard
GET  /portfolio          # Tracked properties
GET  /market-intelligence # Market heat maps and trends
GET  /development-analysis # Development tools
GET  /amalgamation       # Amalgamation tool
GET  /reports            # Report generation
GET  /settings           # User settings
GET  /billing            # Subscription management
```

## API Endpoints Reference

### Authentication
```
POST /auth/register      # User registration with plan selection
POST /auth/login         # User login with redirect handling
POST /auth/refresh       # JWT token refresh
POST /auth/logout        # User logout
GET  /auth/verify-email  # Email verification
POST /auth/forgot-password # Password reset request
POST /auth/reset-password  # Password reset confirmation
```

### Properties (Protected)
```
GET  /properties/search  # Property search with usage tracking
GET  /properties/:id     # Property details
GET  /properties/:id/intelligence # Property intelligence data
POST /properties/:id/track # Track property (plan limits apply)
```

### Development Analysis
```
POST /development/analyze
POST /development/simulate
```

### Amalgamation Analysis
```
GET  /amalgamation
POST /amalgamation
POST /amalgamation/analyze
PUT  /amalgamation/:id
DELETE /amalgamation/:id
```

### Market Intelligence
```
POST /market/heat-map
POST /market/trends
POST /market/emerging-suburbs
GET  /market/movers
GET  /market/heat-map-quick
GET  /market/trends-quick
```

### Alerts & Notifications
```
GET  /alerts
POST /alerts
GET  /alerts/summary
GET  /alerts/:id
PUT  /alerts/:id
DELETE /alerts/:id
POST /alerts/history
POST /alerts/trigger
```

### Webhooks (External Data Integration)
```
POST /webhooks/property-data
POST /webhooks/market-data
GET  /webhooks/data-sources/status
```

### Suburbs & Valuations
```
GET  /suburbs/search
GET  /suburbs/:id/profile
POST /valuations
GET  /valuations/:id
```

## WebSocket Events

### Property Namespace (`/property`)
```javascript
// Client → Server
'subscribe-property'   { propertyId: string }
'unsubscribe-property' { propertyId: string }

// Server → Client
'subscription-confirmed' { type, id, room }
'property-update'       { propertyId, timestamp, data }
'valuation-update'      { propertyId, timestamp, valuation }
'intelligence-update'   { propertyId, timestamp, intelligence }
'alert-triggered'       { propertyId, timestamp, alert }
```

### Market Namespace (`/market`)
```javascript
// Client → Server
'subscribe-suburb'        { suburbId: string }
'subscribe-market-movers' {}
'unsubscribe-suburb'      { suburbId: string }
'unsubscribe-market-movers' {}

// Server → Client
'subscription-confirmed'  { type, id?, room }
'market-trend-update'     { suburbId, timestamp, trend }
'infrastructure-update'   { suburbId, timestamp, infrastructure }
'development-update'      { suburbId, timestamp, development }
'market-movers-update'    { timestamp, data }
'market-alert'           { suburbId, timestamp, alert }
```

## Data Types & Enums

### Alert Types
```typescript
enum AlertType {
  VALUE_CHANGE = 'VALUE_CHANGE',
  NEW_LISTING = 'NEW_LISTING',
  PRICE_DROP = 'PRICE_DROP',
  DEVELOPMENT_APPLICATION = 'DEVELOPMENT_APPLICATION',
  INFRASTRUCTURE_UPDATE = 'INFRASTRUCTURE_UPDATE',
  SCHOOL_RATING_CHANGE = 'SCHOOL_RATING_CHANGE',
  MARKET_TREND = 'MARKET_TREND',
  RISK_ALERT = 'RISK_ALERT',
  CUSTOM = 'CUSTOM'
}
```

### Data Source Types (18 Sources)
```typescript
enum DataSourceType {
  PROPERTY_SALES = 'PROPERTY_SALES',
  CURRENT_LISTINGS = 'CURRENT_LISTINGS',
  PLANNING_ZONING = 'PLANNING_ZONING',
  DEVELOPMENT_APPLICATIONS = 'DEVELOPMENT_APPLICATIONS',
  ECONOMIC_INDICATORS = 'ECONOMIC_INDICATORS',
  INFRASTRUCTURE_PROJECTS = 'INFRASTRUCTURE_PROJECTS',
  SCHOOL_PERFORMANCE = 'SCHOOL_PERFORMANCE',
  CRIME_STATISTICS = 'CRIME_STATISTICS',
  RENTAL_MARKET = 'RENTAL_MARKET',
  TRANSPORT_ACCESSIBILITY = 'TRANSPORT_ACCESSIBILITY',
  CLIMATE_RISK = 'CLIMATE_RISK',
  DEMOGRAPHICS = 'DEMOGRAPHICS',
  CONSTRUCTION_COSTS = 'CONSTRUCTION_COSTS',
  MARKET_SENTIMENT = 'MARKET_SENTIMENT',
  ENERGY_EFFICIENCY = 'ENERGY_EFFICIENCY',
  MORTGAGE_STRESS = 'MORTGAGE_STRESS',
  BUSINESS_ACTIVITY = 'BUSINESS_ACTIVITY',
  SOCIAL_TRENDS = 'SOCIAL_TRENDS'
}
```

### Heat Map Types
```typescript
enum HeatMapType {
  PRICE_GROWTH = 'price_growth',
  DEVELOPMENT_ACTIVITY = 'development_activity',
  YIELDS = 'yields',
  INVESTMENT_SCORE = 'investment_score'
}
```

## Event-Uplift Engine™ Implementation

### Factor Storage
```typescript
interface EventUpliftFactor {
  propertyId: string;
  factorType: string;    // 'infrastructure', 'school_rating', etc.
  factorName: string;    // Specific factor name
  impact: number;        // Impact percentage (-1 to 1)
  confidence: number;    // Confidence score (0-1)
  timeHorizon: number;   // Months into future
  source: string;        // Data source
  metadata?: any;        // Additional factor data
}
```

### Intelligence Scores
```typescript
interface PropertyIntelligence {
  propertyId: string;
  investmentScore?: number;      // 0-100
  developmentScore?: number;     // 0-100
  growthPotential?: number;      // Growth percentage
  riskScore?: number;           // 0-100
  lifestyleScore?: number;      // 0-100
  marketPosition?: number;      // Percentile in suburb
  confidenceScore?: number;     // Overall confidence (0-1)
  keyInsights: string[];        // Array of insights
  opportunities?: any;          // Opportunities data
  risks?: any;                 // Risk assessment data
}
```

## Webhook Data Integration

### Property Data Webhook
```typescript
interface PropertyDataWebhook {
  source: string;
  sourceType: DataSourceType;
  eventType: string;
  propertyIdentifier: string;
  data: {
    address?: string;
    estimatedValue?: number;
    confidence?: number;
    upliftFactors?: EventUpliftFactor[];
    investmentScore?: number;
    developmentScore?: number;
    // ... additional property data
  };
  timestamp: string;
}
```

### Market Data Webhook
```typescript
interface MarketDataWebhook {
  source: string;
  sourceType: DataSourceType;
  eventType: string;
  areaIdentifier: string;
  data: {
    medianPrice?: number;
    priceChange?: number;
    infrastructureProjects?: InfrastructureProject[];
    developmentApplications?: DevelopmentApplication[];
    schoolUpdates?: SchoolUpdate[];
    // ... additional market data
  };
  timestamp: string;
}
```

## Development Commands

### Database Operations
```bash
# Generate Prisma client
cd packages/database && pnpm prisma generate

# Push schema changes
pnpm prisma db push

# Seed database
pnpm db:seed

# Reset database
pnpm prisma db push --force-reset
```

### Development Servers
```bash
# Start all services
pnpm dev

# Start API only
cd apps/api && pnpm dev

# Start web only
cd apps/web && pnpm dev

# Build all
pnpm build

# Build API only
cd apps/api && pnpm build
```

### Testing
```bash
# Run all tests
pnpm test

# Run API tests
cd apps/api && pnpm test

# Run with coverage
pnpm test:cov
```

## Environment Variables

### API (.env)
```bash
DATABASE_URL="postgresql://..."
JWT_SECRET="your-jwt-secret"
JWT_EXPIRES_IN="7d"
FRONTEND_URL="http://localhost:3000"
PORT=3001
```

### Web (.env.local)
```bash
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_WS_URL="ws://localhost:3001"
```

## Performance Targets

- **Search Results**: < 3 seconds
- **Page Loads**: < 2 seconds
- **WebSocket Latency**: < 100ms
- **API Response Time**: < 500ms
- **Database Queries**: < 200ms

## SaaS Security & Access Control

### Authentication & Authorization
- JWT authentication with refresh tokens
- Route-based authentication guards
- Plan-based feature access control
- Usage tracking and limits enforcement
- Session management with secure cookies

### Route Protection Implementation
```typescript
// Authentication Guard
@Injectable()
export class AuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    return request.user && request.user.isAuthenticated;
  }
}

// Plan-based Feature Guard
@Injectable()
export class PlanGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const requiredPlan = this.reflector.get('plan', context.getHandler());
    return this.planService.hasAccess(request.user.plan, requiredPlan);
  }
}
```

### Security Considerations
- JWT authentication with refresh tokens
- Rate limiting on all endpoints (stricter for free users)
- Input validation with class-validator
- SQL injection protection via Prisma
- CORS configuration with origin restrictions
- Helmet.js security headers
- Plan-based rate limiting and feature restrictions

## Monitoring & Logging

- Structured logging with Winston
- Error tracking with Sentry (planned)
- Performance monitoring (planned)
- Database query monitoring
- WebSocket connection tracking

## SaaS Implementation Status - COMPLETED ✅

### Phase 2A: Public Marketing Website ✅ COMPLETED
- [x] Homepage with hero section and value proposition
- [x] Pricing page with 6-tier comparison
- [x] Features page with detailed capabilities
- [x] About page with company information
- [x] Contact page with support information
- [x] Public navigation component
- [x] Marketing-focused footer
- [x] Updated messaging for all Australians
- [x] Removed free trial references

### Phase 2B: Authentication System ✅ COMPLETED
- [x] User registration with plan selection
- [x] Login/logout functionality working
- [x] Demo login functionality restored
- [x] JWT token management
- [x] Route protection middleware implemented
- [x] Authenticated navigation component
- [x] User dropdown menu
- [x] API client configuration fixed
- [x] Return URL handling for protected routes

### Phase 2C: Dashboard & Access Control ✅ COMPLETED
- [x] Dashboard as default post-login destination
- [x] Portfolio summary widgets
- [x] Market movers section
- [x] Personal insights feed
- [x] Quick actions center
- [x] Route protection preventing unauthorized access
- [x] Ready for plan-based feature gating
- [x] Ready for usage tracking and limits
- [x] Ready for upgrade prompts for premium features

## Next Phase: Advanced Analytics Features

### Phase 3A: Development Analysis Tools
- [ ] 3D building envelope visualization
- [ ] Development potential calculator
- [ ] DA precedent analysis
- [ ] ROI projections and feasibility studies

### Phase 3B: Amalgamation Analysis Tool
- [ ] Multi-property selection interface
- [ ] Combined development analysis
- [ ] Value uplift calculations
- [ ] Feasibility assessments

### Phase 3C: Market Intelligence Dashboard
- [ ] Suburb profiles with demographics
- [ ] Market heat maps
- [ ] Trend analysis tools
- [ ] Emerging opportunities detection

---

*This documentation is updated as features are implemented.*
*Current Status: SaaS Architecture COMPLETED ✅*
*Next Focus: Phase 3 - Advanced Analytics Features*
