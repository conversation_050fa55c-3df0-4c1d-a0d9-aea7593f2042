#!/bin/bash

echo "🚀 Starting Revalu API Server"
echo "============================="

# Navigate to API directory
cd apps/api

# Build the application
echo "📦 Building API..."
pnpm build

if [ $? -eq 0 ]; then
    echo "✅ Build successful"
    
    # Start the server
    echo "🚀 Starting server on http://localhost:3001"
    echo "📚 Swagger docs will be available at http://localhost:3001/api/docs"
    echo "🔐 Test login: curl -X POST http://localhost:3001/api/v1/auth/login -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\",\"password\":\"demo123456\"}'"
    echo ""
    echo "Press Ctrl+C to stop the server"
    echo ""
    
    node dist/main.js
else
    echo "❌ Build failed"
    exit 1
fi
