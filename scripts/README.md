# Scripts

This directory contains utility scripts for the Revalu project.

## Available Scripts

### `test-supabase.js`

Tests the Supabase connection using environment variables from `.env`.

**Usage:**
```bash
pnpm test:supabase
```

**What it tests:**
- ✅ Supabase client creation with anon key
- ✅ Basic database connection
- ✅ Authentication status
- ✅ Service key connection (if available)
- ⚠️ Database schema access
- ✅ Storage access

**Expected Results:**
- All basic connection tests should pass
- "Table not found" errors are expected and indicate successful connection
- Authentication should show "no user" for anon key (this is normal)
- Storage access should work (may show 0 buckets initially)

**Environment Variables Required:**
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_ANON_KEY` - Your Supabase anon/public key
- `SUPABASE_SERVICE_KEY` - Your Supabase service key (optional)

**Troubleshooting:**
- If the script fails, check that your `.env` file has the correct Supabase credentials
- Ensure your Supabase project is active and accessible
- Verify that your API keys haven't expired

## Adding New Scripts

When adding new scripts to this directory:

1. Make them executable: `chmod +x scripts/your-script.js`
2. Add a shebang line: `#!/usr/bin/env node`
3. Add a corresponding npm script in `package.json`
4. Document the script in this README
