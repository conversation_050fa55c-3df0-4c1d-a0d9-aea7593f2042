#!/usr/bin/env node

/**
 * Supabase Connection Test Script
 * 
 * This script tests the Supabase connection using environment variables
 * and performs basic operations to verify everything is working correctly.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const envPath = join(__dirname, '..', '.env');
dotenv.config({ path: envPath });

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

async function testSupabaseConnection() {
  log(`${colors.bold}🧪 Testing Supabase Connection${colors.reset}\n`);

  // Check environment variables
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

  if (!supabaseUrl) {
    logError('SUPABASE_URL environment variable is not set');
    process.exit(1);
  }

  if (!supabaseAnonKey) {
    logError('SUPABASE_ANON_KEY environment variable is not set');
    process.exit(1);
  }

  logInfo(`Supabase URL: ${supabaseUrl}`);
  logInfo(`Anon Key: ${supabaseAnonKey.substring(0, 20)}...`);
  if (supabaseServiceKey) {
    logInfo(`Service Key: ${supabaseServiceKey.substring(0, 20)}...`);
  }
  console.log();

  // Test 1: Create Supabase client with anon key
  log(`${colors.bold}Test 1: Creating Supabase client (anon key)${colors.reset}`);
  let supabase;
  try {
    supabase = createClient(supabaseUrl, supabaseAnonKey);
    logSuccess('Supabase client created successfully');
  } catch (error) {
    logError(`Failed to create Supabase client: ${error.message}`);
    process.exit(1);
  }

  // Test 2: Test basic connection
  log(`\n${colors.bold}Test 2: Testing basic connection${colors.reset}`);
  try {
    const { data, error } = await supabase.from('_test_connection').select('*').limit(1);
    if (error) {
      if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
        logSuccess('Basic connection test passed (table not found is expected)');
      } else {
        throw error;
      }
    } else {
      logSuccess('Basic connection test passed');
    }
  } catch (error) {
    logError(`Connection test failed: ${error.message}`);
    process.exit(1);
  }

  // Test 3: Test authentication
  log(`\n${colors.bold}Test 3: Testing authentication status${colors.reset}`);
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      logInfo('No authenticated user (expected for anon key)');
    } else if (user) {
      logSuccess(`Authenticated user found: ${user.email}`);
    } else {
      logInfo('No authenticated user (expected for anon key)');
    }
  } catch (error) {
    logWarning(`Auth test warning: ${error.message}`);
  }

  // Test 4: Test with service key (if available)
  if (supabaseServiceKey) {
    log(`\n${colors.bold}Test 4: Testing service key connection${colors.reset}`);
    try {
      const serviceSupabase = createClient(supabaseUrl, supabaseServiceKey);
      const { data, error } = await serviceSupabase.from('_test_service_connection').select('*').limit(1);
      if (error) {
        if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
          logSuccess('Service key connection test passed (table not found is expected)');
        } else {
          throw error;
        }
      } else {
        logSuccess('Service key connection test passed');
      }
    } catch (error) {
      logError(`Service key test failed: ${error.message}`);
    }
  }

  // Test 5: Test database schema access
  log(`\n${colors.bold}Test 5: Testing database schema access${colors.reset}`);
  try {
    const { data, error } = await supabase.rpc('version');
    if (error) {
      logWarning(`Schema access test: ${error.message}`);
    } else {
      logSuccess('Database schema access working');
    }
  } catch (error) {
    logWarning(`Schema access test warning: ${error.message}`);
  }

  // Test 6: Test storage (if available)
  log(`\n${colors.bold}Test 6: Testing storage access${colors.reset}`);
  try {
    const { data, error } = await supabase.storage.listBuckets();
    if (error) {
      logWarning(`Storage test: ${error.message}`);
    } else {
      logSuccess(`Storage access working. Found ${data.length} buckets`);
      if (data.length > 0) {
        logInfo(`Buckets: ${data.map(b => b.name).join(', ')}`);
      }
    }
  } catch (error) {
    logWarning(`Storage test warning: ${error.message}`);
  }

  // Summary
  log(`\n${colors.bold}🎉 Supabase Connection Test Complete!${colors.reset}`);
  logSuccess('All basic tests passed. Your Supabase connection is working correctly.');
  
  log(`\n${colors.bold}Next Steps:${colors.reset}`);
  logInfo('1. Create your database tables and schemas');
  logInfo('2. Set up Row Level Security (RLS) policies');
  logInfo('3. Configure authentication providers if needed');
  logInfo('4. Set up storage buckets if using file uploads');
  
  console.log();
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  logError(`Unhandled error: ${error.message}`);
  process.exit(1);
});

// Run the test
testSupabaseConnection().catch((error) => {
  logError(`Test failed: ${error.message}`);
  process.exit(1);
});
