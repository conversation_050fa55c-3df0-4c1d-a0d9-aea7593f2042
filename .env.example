# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/revalu_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=revalu_db
DATABASE_USER=username
DATABASE_PASSWORD=password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# API Configuration
API_PORT=3001
API_HOST=localhost
API_BASE_URL=http://localhost:3001

# Web App Configuration
WEB_PORT=3000
WEB_HOST=localhost
WEB_BASE_URL=http://localhost:3000

# Authentication & Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
SESSION_SECRET=your-session-secret-change-this-in-production
BCRYPT_ROUNDS=12

# External APIs
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Environment
NODE_ENV=development

# ML Predictor Service
ML_SERVICE_URL=http://localhost:8000
ML_MODEL_PATH=./models
PREDICTION_CONFIDENCE_THRESHOLD=0.8

# Scraper Service
SCRAPER_SERVICE_URL=http://localhost:8001
SCRAPER_RATE_LIMIT=100
SCRAPER_DELAY_MS=1000

# Monitoring & Analytics
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id

# AWS Configuration (if using AWS services)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
S3_BUCKET_NAME=revalu-uploads

# Docker Configuration
DOCKER_REGISTRY=your-docker-registry
DOCKER_TAG=latest
