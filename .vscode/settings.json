{
  // TypeScript configuration
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.importModuleSpecifier": "relative",
  
  // Editor configuration
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  
  // File associations
  "files.associations": {
    "*.css": "tailwindcss",
    "*.json": "jsonc"
  },
  
  // Search configuration
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.next": true,
    "**/.turbo": true,
    "**/coverage": true,
    "**/*.log": true
  },
  
  // File watcher configuration
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/.next/**": true,
    "**/.turbo/**": true
  },
  
  // Prettier configuration
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,
  
  // ESLint configuration
  "eslint.workingDirectories": [
    "apps/web",
    "apps/api",
    "packages/database",
    "packages/shared",
    "packages/ui",
    "services/ml-predictor",
    "services/scraper"
  ],
  
  // Emmet configuration
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  
  // Terminal configuration
  "terminal.integrated.defaultProfile.osx": "bash",
  "terminal.integrated.cwd": "${workspaceFolder}",
  
  // Git configuration
  "git.ignoreLimitWarning": true,
  "git.autofetch": true,
  
  // Language-specific settings
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[yaml]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  
  // Tailwind CSS configuration
  "tailwindCSS.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  
  // Auto-save configuration
  "files.autoSave": "onFocusChange",
  
  // Breadcrumbs
  "breadcrumbs.enabled": true,
  
  // Explorer configuration
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "package.json": "package-lock.json,pnpm-lock.yaml,yarn.lock",
    "*.ts": "${capture}.js,${capture}.d.ts,${capture}.js.map",
    "*.tsx": "${capture}.js,${capture}.d.ts,${capture}.js.map",
    "tsconfig.json": "tsconfig.*.json",
    ".env": ".env.*",
    "docker-compose.yml": "docker-compose.*.yml,Dockerfile*"
  }
}
