# Revalu

A comprehensive monorepo for real estate valuation and analysis platform.

## Overview

Revalu is a modern real estate valuation platform that combines machine learning predictions, web scraping capabilities, and a user-friendly interface to provide accurate property valuations and market insights.

## Architecture

This monorepo contains the following components:

### Applications (`apps/`)
- **web** - Frontend web application (React/Next.js)
- **api** - Backend API server (Node.js/Express)

### Packages (`packages/`)
- **database** - Database schemas, migrations, and ORM configuration
- **shared** - Shared utilities, types, and constants
- **ui** - Reusable UI components and design system

### Services (`services/`)
- **ml-predictor** - Machine learning service for property valuation
- **scraper** - Web scraping service for market data collection

### Infrastructure (`infrastructure/`)
- **docker** - Docker configurations and compose files

## Prerequisites

- Node.js 18+ 
- pnpm 8+
- PostgreSQL 14+
- Redis 6+
- Python 3.9+ (for ML services)

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd revalu
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the development environment**
   ```bash
   # Start all services
   pnpm dev

   # Or start individual services
   pnpm dev:web     # Frontend only
   pnpm dev:api     # Backend API only
   pnpm dev:ml      # ML service only
   ```

## Development

### Workspace Commands

```bash
# Install dependencies for all packages
pnpm install

# Build all packages
pnpm build

# Run tests across all packages
pnpm test

# Lint all packages
pnpm lint

# Format code
pnpm format
```

### Package-specific Commands

```bash
# Run commands in specific packages
pnpm --filter web dev
pnpm --filter api start
pnpm --filter @revalu/database migrate
```

## Project Structure

```
revalu/
├── apps/
│   ├── web/                 # Frontend application
│   └── api/                 # Backend API
├── packages/
│   ├── database/            # Database layer
│   ├── shared/              # Shared utilities
│   └── ui/                  # UI components
├── services/
│   ├── ml-predictor/        # ML prediction service
│   └── scraper/             # Data scraping service
├── infrastructure/
│   └── docker/              # Docker configurations
├── .env.example             # Environment variables template
├── .gitignore               # Git ignore rules
├── package.json             # Root package configuration
├── pnpm-workspace.yaml      # Workspace configuration
└── README.md                # This file
```

## Technology Stack

- **Frontend**: React, Next.js, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript, Prisma
- **Database**: PostgreSQL, Redis
- **ML/AI**: Python, scikit-learn, TensorFlow/PyTorch
- **Infrastructure**: Docker, Docker Compose
- **Package Manager**: pnpm
- **Monorepo**: pnpm workspaces

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Add tests for new functionality
4. Run `pnpm test` and `pnpm lint`
5. Submit a pull request

## License

[Add your license here]

## Support

For questions and support, please [create an issue](link-to-issues) or contact the development team.
