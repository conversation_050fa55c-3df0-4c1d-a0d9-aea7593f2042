# @revalu/web

Next.js frontend for the Revalu property intelligence platform.

## 🎉 Setup Complete!

The frontend is now running with:
- ✅ Next.js 14 with App Router
- ✅ TypeScript Configuration
- ✅ Tailwind CSS Styling
- ✅ React Query for Server State
- ✅ Authentication System
- ✅ API Client Integration
- ✅ Responsive Design
- ✅ Modern UI Components

## Quick Commands

```bash
pnpm dev              # Start development server
pnpm build            # Build application
pnpm start            # Start production server
pnpm lint             # Run ESLint
pnpm type-check       # Run TypeScript checks
```

## Features

### 🏠 Core Pages
- **Homepage** - Landing page with search and features
- **Properties** - Browse and search properties with filters
- **Suburbs** - Explore suburbs with market data
- **Valuations** - Manage property valuations
- **Dashboard** - User dashboard with quick actions

### 🔐 Authentication
- **Login/Register** - User authentication with JWT
- **Protected Routes** - Automatic redirection for auth-required pages
- **User Context** - Global user state management

### 🎨 UI/UX
- **Responsive Design** - Mobile-first approach
- **Modern Components** - Reusable UI components
- **Loading States** - Skeleton loaders and spinners
- **Error Handling** - User-friendly error messages
- **Toast Notifications** - Success/error feedback

### 🔌 API Integration
- **Typed API Client** - Full TypeScript support
- **React Query** - Caching and synchronization
- **Error Handling** - Automatic retry and error states
- **Authentication** - JWT token management

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # User dashboard
│   ├── properties/        # Property pages
│   ├── suburbs/           # Suburb pages
│   ├── valuations/        # Valuation pages
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   └── ui/               # UI components
├── lib/                  # Utilities and configurations
│   ├── api.ts            # API client
│   ├── auth.tsx          # Authentication context
│   ├── query-client.tsx  # React Query setup
│   └── utils.ts          # Utility functions
└── types/                # TypeScript definitions
    └── api.ts            # API types
```

## Environment Variables

Create a `.env.local` file:

```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WEB_URL=http://localhost:3000
```

## API Integration

The frontend connects to the NestJS API running on `localhost:3001`. All API endpoints are typed and include:

- **Authentication** - Login, register, user management
- **Properties** - Search, filter, view property details
- **Valuations** - Create and manage property valuations
- **Suburbs** - Browse suburbs and market data

## Development

### Adding New Pages
1. Create page in `src/app/[route]/page.tsx`
2. Add navigation links in layout/header
3. Update API client if needed
4. Add TypeScript types

### Adding New Components
1. Create component in `src/components/`
2. Export from appropriate index file
3. Add Tailwind classes for styling
4. Include TypeScript props interface

### API Calls
Use React Query hooks for all API calls:

```tsx
const { data, isLoading, error } = useQuery({
  queryKey: ['properties'],
  queryFn: () => apiClient.getProperties(),
});
```

## Deployment

The application is ready for deployment to platforms like:
- Vercel (recommended for Next.js)
- Netlify
- AWS Amplify
- Docker containers

Make sure to set the correct environment variables for production.
