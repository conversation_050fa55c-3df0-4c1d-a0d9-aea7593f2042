{"name": "@revalu/web", "version": "1.0.0", "description": "Next.js frontend for Revalu property intelligence platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next dist"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.17.0", "@tanstack/react-query-devtools": "^5.17.0", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^3.6.0", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "lucide-react": "^0.303.0", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "recharts": "^2.8.0", "tailwind-merge": "^2.2.0", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.8", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}