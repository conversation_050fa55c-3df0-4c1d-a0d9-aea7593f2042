'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Property } from '@/types/api';

interface ComparisonContextType {
  selectedProperties: Property[];
  addProperty: (property: Property) => void;
  removeProperty: (propertyId: string) => void;
  clearComparison: () => void;
  isPropertySelected: (propertyId: string) => boolean;
  canAddMore: boolean;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

interface ComparisonProviderProps {
  children: ReactNode;
}

export function ComparisonProvider({ children }: ComparisonProviderProps) {
  const [selectedProperties, setSelectedProperties] = useState<Property[]>([]);

  const addProperty = (property: Property) => {
    setSelectedProperties(prev => {
      // Don't add if already selected or if we have 3 properties
      if (prev.length >= 3 || prev.some(p => p.id === property.id)) {
        return prev;
      }
      return [...prev, property];
    });
  };

  const removeProperty = (propertyId: string) => {
    setSelectedProperties(prev => prev.filter(p => p.id !== propertyId));
  };

  const clearComparison = () => {
    setSelectedProperties([]);
  };

  const isPropertySelected = (propertyId: string) => {
    return selectedProperties.some(p => p.id === propertyId);
  };

  const canAddMore = selectedProperties.length < 3;

  const value: ComparisonContextType = {
    selectedProperties,
    addProperty,
    removeProperty,
    clearComparison,
    isPropertySelected,
    canAddMore,
  };

  return (
    <ComparisonContext.Provider value={value}>
      {children}
    </ComparisonContext.Provider>
  );
}

export function useComparison() {
  const context = useContext(ComparisonContext);
  if (context === undefined) {
    throw new Error('useComparison must be used within a ComparisonProvider');
  }
  return context;
}
