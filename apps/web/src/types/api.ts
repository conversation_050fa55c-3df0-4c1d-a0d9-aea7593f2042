// Enums from your Prisma schema
export enum PropertyType {
  HOUSE = 'HOUSE',
  APARTMENT = 'APARTMENT',
  TOWNHOUSE = 'TOWNHOUSE',
  UNIT = 'UNIT',
  VILLA = 'VILLA',
  LAND = 'LAND',
  OTHER = 'OTHER',
}

export enum PropertyStatus {
  FOR_SALE = 'FOR_SALE',
  SOLD = 'SOLD',
  FOR_RENT = 'FOR_RENT',
  RENTED = 'RENTED',
  OFF_MARKET = 'OFF_MARKET',
}

export enum ValuationMethod {
  AUTOMATED = 'AUTOMATED',
  COMPARATIVE = 'COMPARATIVE',
  PROFESSIONAL = 'PROFESSIONAL',
  HYBRID = 'HYBRID',
}

export enum ValuationStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Auth Types
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  accessToken: string;
  tokenType: string;
  expiresIn: number;
  user: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

// Property Types
export interface Property {
  id: string;
  address: string;
  propertyType: PropertyType;
  status: PropertyStatus;
  bedrooms?: number;
  bathrooms?: number;
  carSpaces?: number;
  landSize?: number;
  buildingSize?: number;
  yearBuilt?: number;
  description?: string;
  features?: string[];
  images?: string[];
  latitude?: number;
  longitude?: number;
  currentPrice?: number;
  lastSalePrice?: number;
  lastSaleDate?: string;
  listingPrice?: number;
  soldPrice?: number;
  soldDate?: string;
  listingDate?: string;
  suburbId: string;
  suburb?: Suburb;
  valuations?: Valuation[];
  createdAt: string;
  updatedAt: string;
}

export interface PropertySearchParams {
  page?: number;
  limit?: number;
  propertyType?: PropertyType[];
  status?: PropertyStatus[];
  minPrice?: number;
  maxPrice?: number;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  minCarSpaces?: number;
  maxCarSpaces?: number;
  minLandSize?: number;
  maxLandSize?: number;
  suburbId?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Valuation Types
export interface Valuation {
  id: string;
  propertyId: string;
  estimatedValue: number;
  confidence?: number;
  method: ValuationMethod;
  status: ValuationStatus;
  valuationDate: string;
  expiryDate?: string;
  notes?: string;
  factors?: Record<string, any>;
  property?: Property;
  createdAt: string;
  updatedAt: string;
}

export interface CreateValuationRequest {
  propertyId: string;
  estimatedValue: number;
  confidence?: number;
  method: ValuationMethod;
  status?: ValuationStatus;
  expiryDate?: string;
  notes?: string;
  factors?: Record<string, any>;
}

// Suburb Types
export interface Suburb {
  id: string;
  name: string;
  state: string;
  postcode: string;
  country: string;
  latitude?: number;
  longitude?: number;
  population?: number;
  medianHousePrice?: number;
  medianUnitPrice?: number;
  medianRent?: number;
  averageIncome?: number;
  unemploymentRate?: number;
  crimeRate?: number;
  schoolRating?: number;
  transportScore?: number;
  amenityScore?: number;
  properties?: Property[];
  createdAt: string;
  updatedAt: string;
}

export interface SuburbSearchParams {
  page?: number;
  limit?: number;
  state?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
