'use client';

import { Info, Maximize2, RotateCcw, ZoomIn, ZoomOut } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface HeatMapData {
  type: 'price_growth' | 'median_price' | 'sales_volume' | 'investment_score';
  data: {
    suburbId: string;
    name: string;
    state: string;
    latitude: number;
    longitude: number;
    value: number;
    color: string;
    intensity: number;
  }[];
  legend: {
    min: number;
    max: number;
    unit: string;
    colorScale: string[];
  };
  lastUpdated: Date;
}

interface MarketHeatMapProps {
  data: HeatMapData;
}

export default function MarketHeatMap({ data }: MarketHeatMapProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [selectedSuburb, setSelectedSuburb] = useState<any>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });

  const drawHeatMap = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set up coordinate system
    ctx.save();
    ctx.translate(canvas.width / 2 + pan.x, canvas.height / 2 + pan.y);
    ctx.scale(zoom, zoom);

    // Draw Australia outline (simplified)
    drawAustraliaOutline(ctx);

    // Draw heat map points
    data.data.forEach(point => {
      drawHeatMapPoint(ctx, point);
    });

    ctx.restore();
  }, [data, zoom, pan]);

  useEffect(() => {
    drawHeatMap();
  }, [drawHeatMap]);

  const drawAustraliaOutline = (ctx: CanvasRenderingContext2D) => {
    // Simplified Australia outline
    ctx.strokeStyle = '#d1d5db';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    // Very simplified Australia shape
    const scale = 8;
    const offsetX = -150;
    const offsetY = -100;
    
    // Western Australia
    ctx.moveTo(offsetX + 10 * scale, offsetY + 20 * scale);
    ctx.lineTo(offsetX + 10 * scale, offsetY + 60 * scale);
    ctx.lineTo(offsetX + 30 * scale, offsetY + 80 * scale);
    ctx.lineTo(offsetX + 50 * scale, offsetY + 75 * scale);
    
    // Southern coast
    ctx.lineTo(offsetX + 80 * scale, offsetY + 75 * scale);
    ctx.lineTo(offsetX + 100 * scale, offsetY + 70 * scale);
    ctx.lineTo(offsetX + 120 * scale, offsetY + 65 * scale);
    
    // Eastern coast
    ctx.lineTo(offsetX + 130 * scale, offsetY + 50 * scale);
    ctx.lineTo(offsetX + 125 * scale, offsetY + 30 * scale);
    ctx.lineTo(offsetX + 120 * scale, offsetY + 10 * scale);
    
    // Northern coast
    ctx.lineTo(offsetX + 100 * scale, offsetY + 5 * scale);
    ctx.lineTo(offsetX + 80 * scale, offsetY + 8 * scale);
    ctx.lineTo(offsetX + 60 * scale, offsetY + 12 * scale);
    ctx.lineTo(offsetX + 40 * scale, offsetY + 15 * scale);
    ctx.lineTo(offsetX + 20 * scale, offsetY + 18 * scale);
    ctx.closePath();
    
    ctx.stroke();
  };

  const drawHeatMapPoint = (ctx: CanvasRenderingContext2D, point: any) => {
    // Convert lat/lng to canvas coordinates (simplified projection)
    const x = (point.longitude + 140) * 8 - 150;
    const y = (-point.latitude + 10) * 8 - 100;
    
    // Draw heat point
    const radius = 8 + (point.intensity * 12);
    
    // Create gradient
    const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
    gradient.addColorStop(0, point.color);
    gradient.addColorStop(1, point.color.replace(/[\d.]+\)$/g, '0)'));
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, 2 * Math.PI);
    ctx.fill();
    
    // Draw suburb marker
    ctx.fillStyle = '#ffffff';
    ctx.beginPath();
    ctx.arc(x, y, 3, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 1;
    ctx.stroke();
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      const deltaX = e.clientX - lastMousePos.x;
      const deltaY = e.clientY - lastMousePos.y;
      setPan(prev => ({ x: prev.x + deltaX, y: prev.y + deltaY }));
      setLastMousePos({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (isDragging) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Convert to map coordinates and find nearest suburb
    const mapX = (x - canvas.width / 2 - pan.x) / zoom;
    const mapY = (y - canvas.height / 2 - pan.y) / zoom;
    
    let nearestSuburb = null;
    let minDistance = Infinity;
    
    data.data.forEach(point => {
      const pointX = (point.longitude + 140) * 8 - 150;
      const pointY = (-point.latitude + 10) * 8 - 100;
      const distance = Math.sqrt((mapX - pointX) ** 2 + (mapY - pointY) ** 2);
      
      if (distance < 20 && distance < minDistance) {
        minDistance = distance;
        nearestSuburb = point;
      }
    });
    
    setSelectedSuburb(nearestSuburb);
  };

  const zoomIn = () => setZoom(prev => Math.min(prev * 1.2, 3));
  const zoomOut = () => setZoom(prev => Math.max(prev / 1.2, 0.5));
  const resetView = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === '$') {
      return new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    } else if (unit === '%') {
      return `${value.toFixed(1)}%`;
    } else {
      return `${Math.round(value)}${unit}`;
    }
  };

  const getDataTypeLabel = (type: string) => {
    switch (type) {
      case 'price_growth':
        return 'Price Growth';
      case 'median_price':
        return 'Median Price';
      case 'sales_volume':
        return 'Sales Volume';
      case 'investment_score':
        return 'Investment Score';
      default:
        return 'Market Data';
    }
  };

  return (
    <div className="relative">
      {/* Map Canvas */}
      <div className="relative bg-gray-50 rounded-lg overflow-hidden" style={{ height: '600px' }}>
        <canvas
          ref={canvasRef}
          width={800}
          height={600}
          className="w-full h-full cursor-move"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onClick={handleCanvasClick}
        />
        
        {/* Map Controls */}
        <div className="absolute top-4 right-4 flex flex-col gap-2">
          <button
            onClick={zoomIn}
            className="p-2 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 transition-colors"
            title="Zoom In"
          >
            <ZoomIn className="h-4 w-4" />
          </button>
          <button
            onClick={zoomOut}
            className="p-2 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 transition-colors"
            title="Zoom Out"
          >
            <ZoomOut className="h-4 w-4" />
          </button>
          <button
            onClick={resetView}
            className="p-2 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 transition-colors"
            title="Reset View"
          >
            <RotateCcw className="h-4 w-4" />
          </button>
          <button
            onClick={() => {/* Implement fullscreen */}}
            className="p-2 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 transition-colors"
            title="Fullscreen"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
        </div>

        {/* Instructions */}
        <div className="absolute bottom-4 left-4 bg-white bg-opacity-90 rounded-lg p-3 text-sm text-gray-600">
          <div className="flex items-center gap-2 mb-1">
            <Info className="h-4 w-4" />
            <span className="font-medium">Map Controls</span>
          </div>
          <div>• Click and drag to pan</div>
          <div>• Click on points for details</div>
          <div>• Use controls to zoom</div>
        </div>
      </div>

      {/* Selected Suburb Details */}
      {selectedSuburb && (
        <div className="mt-4 bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-semibold text-gray-900">{selectedSuburb.name}</h4>
            <button
              onClick={() => setSelectedSuburb(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <div className="text-sm text-gray-500">State</div>
              <div className="font-medium">{selectedSuburb.state}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">{getDataTypeLabel(data.type)}</div>
              <div className="font-medium text-blue-600">
                {formatValue(selectedSuburb.value, data.legend.unit)}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Intensity</div>
              <div className="font-medium">{(selectedSuburb.intensity * 100).toFixed(0)}%</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Coordinates</div>
              <div className="font-medium text-xs">
                {selectedSuburb.latitude.toFixed(3)}, {selectedSuburb.longitude.toFixed(3)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Heat Map Statistics */}
      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-sm text-gray-500">Highest Value</div>
          <div className="font-semibold text-green-600">
            {(() => {
              const maxPoint = data.data.reduce((max, point) => 
                point.value > max.value ? point : max
              );
              return `${maxPoint.name}, ${maxPoint.state}`;
            })()}
          </div>
          <div className="text-sm text-gray-600">
            {formatValue(Math.max(...data.data.map(p => p.value)), data.legend.unit)}
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-sm text-gray-500">Average Value</div>
          <div className="font-semibold text-blue-600">
            {formatValue(
              data.data.reduce((sum, point) => sum + point.value, 0) / data.data.length,
              data.legend.unit
            )}
          </div>
          <div className="text-sm text-gray-600">Across {data.data.length} suburbs</div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-sm text-gray-500">Data Coverage</div>
          <div className="font-semibold text-purple-600">{data.data.length} suburbs</div>
          <div className="text-sm text-gray-600">
            {new Set(data.data.map(p => p.state)).size} states
          </div>
        </div>
      </div>
    </div>
  );
}
