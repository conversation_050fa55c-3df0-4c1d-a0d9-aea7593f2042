'use client';

import { useState } from 'react';
import { TrendingUp, TrendingDown, BarChart3, Target, AlertTriangle, Calendar } from 'lucide-react';

interface TrendData {
  period: string;
  value: number;
  change: number;
  changePercent: number;
}

interface MarketTrend {
  suburb: string;
  state: string;
  currentValue: number;
  trends: TrendData[];
  summary: {
    currentValue: number;
    periodChange: number;
    periodChangePercent: number;
    volatility: number;
    trend: 'bullish' | 'bearish' | 'stable';
  };
  forecast: {
    period: string;
    predictedValue: number;
    confidence: number;
  }[];
}

interface Metric {
  id: string;
  label: string;
  unit: string;
}

interface TrendAnalyzerProps {
  data: MarketTrend;
  metric: Metric;
  period: string;
}

export default function TrendAnalyzer({ data, metric, period }: TrendAnalyzerProps) {
  const [activeTab, setActiveTab] = useState<'chart' | 'forecast' | 'analysis'>('chart');

  const formatValue = (value: number) => {
    if (metric.unit === '$') {
      return new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    } else {
      return `${Math.round(value)} ${metric.unit}`;
    }
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600 bg-green-50';
    if (confidence >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const generateInsights = () => {
    const insights = [];
    
    if (data.summary.periodChangePercent > 10) {
      insights.push({
        type: 'positive',
        title: 'Strong Growth Trend',
        description: `${metric.label} has increased by ${data.summary.periodChangePercent.toFixed(1)}% over the selected period, indicating strong market performance.`
      });
    } else if (data.summary.periodChangePercent < -10) {
      insights.push({
        type: 'negative',
        title: 'Declining Market',
        description: `${metric.label} has decreased by ${Math.abs(data.summary.periodChangePercent).toFixed(1)}% over the selected period, suggesting market challenges.`
      });
    }

    if (data.summary.volatility > 15) {
      insights.push({
        type: 'warning',
        title: 'High Volatility',
        description: `Market shows high volatility (${data.summary.volatility.toFixed(1)}%), indicating unpredictable price movements.`
      });
    } else if (data.summary.volatility < 5) {
      insights.push({
        type: 'positive',
        title: 'Stable Market',
        description: `Low volatility (${data.summary.volatility.toFixed(1)}%) suggests a stable and predictable market.`
      });
    }

    const recentTrend = data.trends.slice(-3);
    const recentGrowth = recentTrend.every(t => t.changePercent > 0);
    const recentDecline = recentTrend.every(t => t.changePercent < 0);

    if (recentGrowth) {
      insights.push({
        type: 'positive',
        title: 'Recent Momentum',
        description: 'The last 3 periods show consistent growth, indicating positive momentum.'
      });
    } else if (recentDecline) {
      insights.push({
        type: 'warning',
        title: 'Recent Decline',
        description: 'The last 3 periods show consistent decline, suggesting a potential downturn.'
      });
    }

    return insights;
  };

  const insights = generateInsights();

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'chart', label: 'Trend Chart', icon: BarChart3 },
            { id: 'forecast', label: 'Forecast', icon: Target },
            { id: 'analysis', label: 'Analysis', icon: TrendingUp }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      <div className="p-6">
        {/* Trend Chart */}
        {activeTab === 'chart' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {metric.label} Trend - {data.suburb}
              </h3>
              
              {/* Chart Visualization */}
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <div className="h-64 flex items-end justify-between gap-2">
                  {data.trends.map((trend, index) => {
                    const maxValue = Math.max(...data.trends.map(t => t.value));
                    const minValue = Math.min(...data.trends.map(t => t.value));
                    const height = ((trend.value - minValue) / (maxValue - minValue)) * 200 + 20;
                    
                    return (
                      <div key={index} className="flex flex-col items-center flex-1">
                        <div
                          className="bg-blue-500 rounded-t w-full min-w-8 relative group cursor-pointer"
                          style={{ height: `${height}px` }}
                        >
                          {/* Tooltip */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                            {formatValue(trend.value)}
                            {index > 0 && (
                              <div className={getChangeColor(trend.changePercent)}>
                                {trend.changePercent > 0 ? '+' : ''}{trend.changePercent.toFixed(1)}%
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-xs text-gray-600 mt-2 text-center">
                          {trend.period}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Period-over-Period Changes */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {data.trends.slice(-3).map((trend, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <div className="text-sm text-gray-600">{trend.period}</div>
                    <div className="text-lg font-semibold">{formatValue(trend.value)}</div>
                    {index > 0 && (
                      <div className={`text-sm flex items-center gap-1 ${getChangeColor(trend.changePercent)}`}>
                        {trend.changePercent > 0 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
                        {trend.changePercent > 0 ? '+' : ''}{trend.changePercent.toFixed(1)}%
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Forecast */}
        {activeTab === 'forecast' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-600" />
                Market Forecast
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {data.forecast.map((forecast, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">{forecast.period}</h4>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getConfidenceColor(forecast.confidence)}`}>
                        {forecast.confidence}% confidence
                      </span>
                    </div>
                    
                    <div className="text-2xl font-bold text-blue-600 mb-2">
                      {formatValue(forecast.predictedValue)}
                    </div>
                    
                    <div className={`text-sm flex items-center gap-1 ${
                      getChangeColor(forecast.predictedValue - data.summary.currentValue)
                    }`}>
                      {forecast.predictedValue > data.summary.currentValue ? 
                        <TrendingUp className="h-3 w-3" /> : 
                        <TrendingDown className="h-3 w-3" />
                      }
                      {((forecast.predictedValue - data.summary.currentValue) / data.summary.currentValue * 100).toFixed(1)}% 
                      from current
                    </div>
                  </div>
                ))}
              </div>

              <div className="bg-blue-50 rounded-lg p-4 mt-6">
                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900">Forecast Methodology</h4>
                    <p className="text-blue-800 text-sm mt-1">
                      Forecasts are generated using historical trend analysis, seasonal patterns, and market indicators. 
                      Confidence levels decrease over longer time horizons due to increased uncertainty.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Analysis */}
        {activeTab === 'analysis' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                Market Analysis & Insights
              </h3>
              
              {/* Key Insights */}
              <div className="space-y-4 mb-6">
                {insights.map((insight, index) => (
                  <div key={index} className={`p-4 rounded-lg border-l-4 ${
                    insight.type === 'positive' ? 'bg-green-50 border-green-400' :
                    insight.type === 'negative' ? 'bg-red-50 border-red-400' :
                    'bg-yellow-50 border-yellow-400'
                  }`}>
                    <div className="flex items-start gap-3">
                      {insight.type === 'positive' ? 
                        <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" /> :
                        insight.type === 'negative' ?
                        <TrendingDown className="h-5 w-5 text-red-600 mt-0.5" /> :
                        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                      }
                      <div>
                        <h4 className={`font-medium ${
                          insight.type === 'positive' ? 'text-green-900' :
                          insight.type === 'negative' ? 'text-red-900' :
                          'text-yellow-900'
                        }`}>
                          {insight.title}
                        </h4>
                        <p className={`text-sm mt-1 ${
                          insight.type === 'positive' ? 'text-green-800' :
                          insight.type === 'negative' ? 'text-red-800' :
                          'text-yellow-800'
                        }`}>
                          {insight.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Statistical Summary */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="font-medium text-gray-900 mb-4">Statistical Summary</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="text-sm text-gray-600">Average Monthly Change</div>
                    <div className="text-lg font-semibold">
                      {(data.trends.reduce((sum, t) => sum + t.changePercent, 0) / data.trends.length).toFixed(2)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Best Month</div>
                    <div className="text-lg font-semibold text-green-600">
                      +{Math.max(...data.trends.map(t => t.changePercent)).toFixed(1)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Worst Month</div>
                    <div className="text-lg font-semibold text-red-600">
                      {Math.min(...data.trends.map(t => t.changePercent)).toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
