'use client';

import { useComparison } from '@/contexts/ComparisonContext';
import { formatCurrency } from '@/lib/utils';
import { BarChart3, X, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export function ComparisonBar() {
  const { selectedProperties, removeProperty, clearComparison } = useComparison();
  const router = useRouter();

  if (selectedProperties.length === 0) {
    return null;
  }

  const handleCompare = () => {
    const propertyIds = selectedProperties.map(p => p.id).join(',');
    router.push(`/compare?properties=${propertyIds}`);
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-primary-600" />
              <span className="font-medium text-gray-900">
                Compare Properties ({selectedProperties.length}/3)
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {selectedProperties.map((property, index) => (
                <div key={property.id} className="flex items-center">
                  <div className="bg-gray-100 rounded-lg p-2 flex items-center space-x-2 max-w-xs">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {property.address}
                      </p>
                      <p className="text-xs text-gray-600">
                        {formatCurrency(property.currentPrice || property.lastSalePrice || 0)}
                      </p>
                    </div>
                    <button
                      onClick={() => removeProperty(property.id)}
                      className="text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  {index < selectedProperties.length - 1 && (
                    <ArrowRight className="w-4 h-4 text-gray-400 mx-2" />
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={clearComparison}
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              Clear All
            </button>
            <button
              onClick={handleCompare}
              disabled={selectedProperties.length < 2}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Compare {selectedProperties.length > 1 ? `${selectedProperties.length} Properties` : ''}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
