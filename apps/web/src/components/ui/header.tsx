'use client';

import { useAuth } from '@/lib/auth';
import { AuthenticatedHeader } from './authenticated-header';
import { PublicHeader } from './public-header';

export function Header() {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading state or nothing while checking auth
  if (isLoading) {
    return (
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <span className="text-2xl font-bold text-primary-600">Revalu</span>
            </div>
          </div>
        </div>
      </header>
    );
  }

  // Render appropriate header based on authentication status
  return isAuthenticated ? <AuthenticatedHeader /> : <PublicHeader />;
}


