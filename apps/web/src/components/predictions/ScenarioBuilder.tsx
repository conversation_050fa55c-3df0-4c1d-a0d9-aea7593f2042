'use client';

import { Play, Settings } from 'lucide-react';

interface ScenarioParameters {
  propertyType: 'house' | 'unit' | 'townhouse';
  location: string;
  currentPrice: number;
  timeframe: '6m' | '12m' | '24m' | '60m';
  marketConditions: 'bull' | 'bear' | 'stable';
  interestRates: 'rising' | 'falling' | 'stable';
  populationGrowth: 'high' | 'medium' | 'low';
  infrastructureInvestment: 'major' | 'minor' | 'none';
  economicFactors: {
    gdpGrowth: number;
    unemployment: number;
    inflation: number;
  };
}

interface ScenarioBuilderProps {
  parameters: ScenarioParameters;
  onParametersChange: (parameters: ScenarioParameters) => void;
  onRunScenario: () => void;
  isRunning: boolean;
}

export default function ScenarioBuilder({ 
  parameters, 
  onParametersChange, 
  onRunScenario, 
  isRunning 
}: ScenarioBuilderProps) {
  
  const updateParameter = (key: keyof ScenarioParameters, value: any) => {
    onParametersChange({
      ...parameters,
      [key]: value
    });
  };

  const updateEconomicFactor = (key: keyof ScenarioParameters['economicFactors'], value: number) => {
    onParametersChange({
      ...parameters,
      economicFactors: {
        ...parameters.economicFactors,
        [key]: value
      }
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const locations = [
    'Melbourne CBD', 'Sydney CBD', 'Brisbane CBD', 'Adelaide CBD', 'Perth CBD',
    'Toorak', 'Double Bay', 'New Farm', 'Unley', 'Cottesloe',
    'South Yarra', 'Paddington', 'Fortitude Valley', 'North Adelaide', 'Fremantle'
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Settings className="h-5 w-5 text-purple-600" />
          Scenario Parameters
        </h3>
      </div>
      
      <div className="p-6 space-y-6">
        {/* Property Details */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Property Details</h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Property Type</label>
              <select
                value={parameters.propertyType}
                onChange={(e) => updateParameter('propertyType', e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="house">House</option>
                <option value="unit">Unit/Apartment</option>
                <option value="townhouse">Townhouse</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
              <select
                value={parameters.location}
                onChange={(e) => updateParameter('location', e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                {locations.map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Current Price</label>
              <input
                type="range"
                min="300000"
                max="3000000"
                step="50000"
                value={parameters.currentPrice}
                onChange={(e) => updateParameter('currentPrice', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-sm text-gray-500 mt-1">
                <span>$300k</span>
                <span className="font-medium text-purple-600">{formatCurrency(parameters.currentPrice)}</span>
                <span>$3M</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Analysis Timeframe</label>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { id: '6m', label: '6 Months' },
                  { id: '12m', label: '12 Months' },
                  { id: '24m', label: '2 Years' },
                  { id: '60m', label: '5 Years' }
                ].map((timeframe) => (
                  <button
                    key={timeframe.id}
                    onClick={() => updateParameter('timeframe', timeframe.id)}
                    className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      parameters.timeframe === timeframe.id
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {timeframe.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Market Conditions */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Market Conditions</h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Overall Market</label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { id: 'bull', label: 'Bull Market', color: 'green' },
                  { id: 'stable', label: 'Stable', color: 'blue' },
                  { id: 'bear', label: 'Bear Market', color: 'red' }
                ].map((condition) => (
                  <button
                    key={condition.id}
                    onClick={() => updateParameter('marketConditions', condition.id)}
                    className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      parameters.marketConditions === condition.id
                        ? `bg-${condition.color}-600 text-white`
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {condition.label}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Interest Rates</label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { id: 'rising', label: 'Rising' },
                  { id: 'stable', label: 'Stable' },
                  { id: 'falling', label: 'Falling' }
                ].map((rate) => (
                  <button
                    key={rate.id}
                    onClick={() => updateParameter('interestRates', rate.id)}
                    className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      parameters.interestRates === rate.id
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {rate.label}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Population Growth</label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { id: 'high', label: 'High' },
                  { id: 'medium', label: 'Medium' },
                  { id: 'low', label: 'Low' }
                ].map((growth) => (
                  <button
                    key={growth.id}
                    onClick={() => updateParameter('populationGrowth', growth.id)}
                    className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      parameters.populationGrowth === growth.id
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {growth.label}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Infrastructure Investment</label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { id: 'major', label: 'Major' },
                  { id: 'minor', label: 'Minor' },
                  { id: 'none', label: 'None' }
                ].map((investment) => (
                  <button
                    key={investment.id}
                    onClick={() => updateParameter('infrastructureInvestment', investment.id)}
                    className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      parameters.infrastructureInvestment === investment.id
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {investment.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Economic Factors */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Economic Factors</h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                GDP Growth: {parameters.economicFactors.gdpGrowth}%
              </label>
              <input
                type="range"
                min="-2"
                max="6"
                step="0.1"
                value={parameters.economicFactors.gdpGrowth}
                onChange={(e) => updateEconomicFactor('gdpGrowth', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>-2%</span>
                <span>6%</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Unemployment: {parameters.economicFactors.unemployment}%
              </label>
              <input
                type="range"
                min="2"
                max="10"
                step="0.1"
                value={parameters.economicFactors.unemployment}
                onChange={(e) => updateEconomicFactor('unemployment', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>2%</span>
                <span>10%</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Inflation: {parameters.economicFactors.inflation}%
              </label>
              <input
                type="range"
                min="0"
                max="8"
                step="0.1"
                value={parameters.economicFactors.inflation}
                onChange={(e) => updateEconomicFactor('inflation', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0%</span>
                <span>8%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Run Analysis Button */}
        <button
          onClick={onRunScenario}
          disabled={isRunning}
          className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
        >
          <Play className="h-5 w-5" />
          {isRunning ? 'Running Analysis...' : 'Run Scenario Analysis'}
        </button>
      </div>
    </div>
  );
}
