'use client';

import { useState } from 'react';
import { TrendingUp, TrendingDown, Target, AlertTriangle, CheckCircle, BarChart3 } from 'lucide-react';

interface ScenarioParameters {
  propertyType: 'house' | 'unit' | 'townhouse';
  location: string;
  currentPrice: number;
  timeframe: '6m' | '12m' | '24m' | '60m';
  marketConditions: 'bull' | 'bear' | 'stable';
  interestRates: 'rising' | 'falling' | 'stable';
  populationGrowth: 'high' | 'medium' | 'low';
  infrastructureInvestment: 'major' | 'minor' | 'none';
  economicFactors: {
    gdpGrowth: number;
    unemployment: number;
    inflation: number;
  };
}

interface ScenarioResult {
  scenario: string;
  probability: number;
  priceChange: number;
  priceChangePercent: number;
  finalPrice: number;
  confidence: number;
  keyFactors: string[];
  risks: string[];
  opportunities: string[];
}

interface ScenarioResultsProps {
  results: ScenarioResult[];
  parameters: ScenarioParameters;
  selectedScenario: string;
  onScenarioSelect: (scenario: string) => void;
}

export default function ScenarioResults({ 
  results, 
  parameters, 
  selectedScenario, 
  onScenarioSelect 
}: ScenarioResultsProps) {
  
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'comparison'>('overview');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getScenarioColor = (scenario: string) => {
    switch (scenario.toLowerCase()) {
      case 'optimistic':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'pessimistic':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <BarChart3 className="h-4 w-4 text-gray-500" />;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600 bg-green-50';
    if (confidence >= 75) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getTimeframeLabel = (timeframe: string) => {
    switch (timeframe) {
      case '6m': return '6 months';
      case '12m': return '12 months';
      case '24m': return '2 years';
      case '60m': return '5 years';
      default: return timeframe;
    }
  };

  const selectedResult = results.find(r => r.scenario.toLowerCase() === selectedScenario) || results[1];

  return (
    <div className="space-y-6">
      {/* Scenario Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {results.map((result) => (
          <div
            key={result.scenario}
            onClick={() => onScenarioSelect(result.scenario.toLowerCase())}
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              selectedScenario === result.scenario.toLowerCase()
                ? getScenarioColor(result.scenario)
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-gray-900">{result.scenario}</h3>
              <span className="text-sm font-medium text-gray-600">{result.probability}%</span>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                {getChangeIcon(result.priceChangePercent)}
                <span className={`text-lg font-bold ${getChangeColor(result.priceChangePercent)}`}>
                  {result.priceChangePercent > 0 ? '+' : ''}{result.priceChangePercent.toFixed(1)}%
                </span>
              </div>
              
              <div className="text-sm text-gray-600">
                {formatCurrency(parameters.currentPrice)} → {formatCurrency(result.finalPrice)}
              </div>
              
              <div className="text-sm text-gray-600">
                Change: {formatCurrency(Math.abs(result.priceChange))}
              </div>
              
              <div className={`text-xs px-2 py-1 rounded-full inline-block ${getConfidenceColor(result.confidence)}`}>
                {result.confidence}% confidence
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Detailed Analysis */}
      <div className="bg-white rounded-lg shadow-sm">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: Target },
              { id: 'details', label: 'Details', icon: BarChart3 },
              { id: 'comparison', label: 'Comparison', icon: TrendingUp }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {selectedResult.scenario} Scenario Analysis
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Property Details</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div>Type: {parameters.propertyType}</div>
                        <div>Location: {parameters.location}</div>
                        <div>Current Price: {formatCurrency(parameters.currentPrice)}</div>
                        <div>Timeframe: {getTimeframeLabel(parameters.timeframe)}</div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Market Conditions</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div>Market: {parameters.marketConditions}</div>
                        <div>Interest Rates: {parameters.interestRates}</div>
                        <div>Population Growth: {parameters.populationGrowth}</div>
                        <div>Infrastructure: {parameters.infrastructureInvestment}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h4 className="font-medium text-blue-900 mb-2">Predicted Outcome</h4>
                      <div className="space-y-2">
                        <div className="text-2xl font-bold text-blue-600">
                          {formatCurrency(selectedResult.finalPrice)}
                        </div>
                        <div className={`text-lg font-semibold ${getChangeColor(selectedResult.priceChangePercent)}`}>
                          {selectedResult.priceChangePercent > 0 ? '+' : ''}{selectedResult.priceChangePercent.toFixed(1)}% 
                          ({formatCurrency(Math.abs(selectedResult.priceChange))})
                        </div>
                        <div className="text-sm text-blue-800">
                          Probability: {selectedResult.probability}% • Confidence: {selectedResult.confidence}%
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Economic Factors</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div>GDP Growth: {parameters.economicFactors.gdpGrowth}%</div>
                        <div>Unemployment: {parameters.economicFactors.unemployment}%</div>
                        <div>Inflation: {parameters.economicFactors.inflation}%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Details Tab */}
          {activeTab === 'details' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {selectedResult.scenario} Scenario Details
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Key Factors */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Key Factors
                    </h4>
                    <ul className="space-y-2">
                      {selectedResult.keyFactors.map((factor, index) => (
                        <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                          <CheckCircle className="h-3 w-3 text-green-500 mt-1 flex-shrink-0" />
                          {factor}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {/* Risks */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      Risks
                    </h4>
                    <ul className="space-y-2">
                      {selectedResult.risks.map((risk, index) => (
                        <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                          <AlertTriangle className="h-3 w-3 text-red-500 mt-1 flex-shrink-0" />
                          {risk}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {/* Opportunities */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-500" />
                      Opportunities
                    </h4>
                    <ul className="space-y-2">
                      {selectedResult.opportunities.map((opportunity, index) => (
                        <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                          <Target className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                          {opportunity}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Comparison Tab */}
          {activeTab === 'comparison' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Scenario Comparison</h3>
                
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Scenario</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Probability</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Price Change</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Final Price</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Confidence</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.map((result, index) => (
                        <tr key={index} className="border-b border-gray-100">
                          <td className="py-3 px-4 font-medium">{result.scenario}</td>
                          <td className="py-3 px-4">{result.probability}%</td>
                          <td className={`py-3 px-4 font-medium ${getChangeColor(result.priceChangePercent)}`}>
                            {result.priceChangePercent > 0 ? '+' : ''}{result.priceChangePercent.toFixed(1)}%
                          </td>
                          <td className="py-3 px-4 font-medium">{formatCurrency(result.finalPrice)}</td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getConfidenceColor(result.confidence)}`}>
                              {result.confidence}%
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {/* Summary Insights */}
                <div className="mt-6 bg-purple-50 rounded-lg p-4">
                  <h4 className="font-medium text-purple-900 mb-2">Analysis Summary</h4>
                  <div className="text-sm text-purple-800 space-y-1">
                    <div>• Expected value range: {formatCurrency(Math.min(...results.map(r => r.finalPrice)))} - {formatCurrency(Math.max(...results.map(r => r.finalPrice)))}</div>
                    <div>• Most likely outcome: {results.find(r => r.probability === Math.max(...results.map(r => r.probability)))?.scenario} scenario</div>
                    <div>• Average confidence: {Math.round(results.reduce((sum, r) => sum + r.confidence, 0) / results.length)}%</div>
                    <div>• Timeframe: {getTimeframeLabel(parameters.timeframe)} analysis period</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
