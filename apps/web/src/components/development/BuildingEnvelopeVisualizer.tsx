'use client';

import { <PERSON>, Info, Maximize, RotateCcw, Setting<PERSON>, ZoomIn, ZoomOut } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface PropertyDetails {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  zoning: string;
  developmentScore: number;
  coordinates: {
    lat: number;
    lng: number;
  };
  buildingEnvelope: {
    maxHeight: number;
    setbacks: {
      front: number;
      rear: number;
      side: number;
    };
    floorSpaceRatio: number;
    siteCoverage: number;
  };
  developmentPotential: {
    maxDwellings: number;
    estimatedValue: number;
    roi: number;
    developmentCost: number;
    profit: number;
  };
}

interface BuildingEnvelopeVisualizerProps {
  property: PropertyDetails;
}

export default function BuildingEnvelopeVisualizer({ property }: BuildingEnvelopeVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [viewMode, setViewMode] = useState<'3d' | 'top' | 'side'>('3d');
  const [showSetbacks, setShowSetbacks] = useState(true);
  const [showDimensions, setShowDimensions] = useState(true);
  const [rotation, setRotation] = useState({ x: 20, y: 45 });
  const [zoom, setZoom] = useState(1);

  // Mock 3D visualization data
  const envelopeData = {
    landDimensions: {
      width: Math.sqrt(property.landSize * 0.8), // Approximate square lot
      depth: Math.sqrt(property.landSize * 1.2)
    },
    buildableArea: {
      width: Math.sqrt(property.landSize * 0.8) - (property.buildingEnvelope.setbacks.side * 2),
      depth: Math.sqrt(property.landSize * 1.2) - property.buildingEnvelope.setbacks.front - property.buildingEnvelope.setbacks.rear
    },
    maxHeight: property.buildingEnvelope.maxHeight,
    floors: Math.floor(property.buildingEnvelope.maxHeight / 3.2), // Assuming 3.2m floor height
    totalFloorArea: property.landSize * property.buildingEnvelope.floorSpaceRatio
  };

  const drawVisualization = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Set up coordinate system
    ctx.save();
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.scale(zoom, zoom);

    if (viewMode === '3d') {
      draw3DView(ctx);
    } else if (viewMode === 'top') {
      drawTopView(ctx);
    } else {
      drawSideView(ctx);
    }

    ctx.restore();
  }, [viewMode, showSetbacks, showDimensions, rotation, zoom, envelopeData]);

  useEffect(() => {
    drawVisualization();
  }, [drawVisualization]);

  const draw3DView = (ctx: CanvasRenderingContext2D) => {
    const { landDimensions, buildableArea, maxHeight } = envelopeData;
    
    // Apply rotation
    const radX = (rotation.x * Math.PI) / 180;
    const radY = (rotation.y * Math.PI) / 180;
    
    // Draw land boundary
    ctx.strokeStyle = '#10b981';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    drawIsometricRectangle(ctx, -landDimensions.width/2, -landDimensions.depth/2, landDimensions.width, landDimensions.depth, 0);
    
    // Draw setbacks if enabled
    if (showSetbacks) {
      ctx.strokeStyle = '#f59e0b';
      ctx.lineWidth = 1;
      ctx.setLineDash([3, 3]);
      const setbackX = -buildableArea.width/2;
      const setbackY = -buildableArea.depth/2;
      drawIsometricRectangle(ctx, setbackX, setbackY, buildableArea.width, buildableArea.depth, 0);
    }
    
    // Draw building envelope
    ctx.strokeStyle = '#3b82f6';
    ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';
    ctx.lineWidth = 2;
    ctx.setLineDash([]);
    drawIsometricBox(ctx, -buildableArea.width/2, -buildableArea.depth/2, buildableArea.width, buildableArea.depth, maxHeight);
    
    // Draw dimensions if enabled
    if (showDimensions) {
      drawDimensions(ctx);
    }
  };

  const drawTopView = (ctx: CanvasRenderingContext2D) => {
    const { landDimensions, buildableArea } = envelopeData;
    const scale = 3;
    
    // Draw land boundary
    ctx.strokeStyle = '#10b981';
    ctx.fillStyle = 'rgba(16, 185, 129, 0.1)';
    ctx.lineWidth = 2;
    ctx.fillRect(-landDimensions.width * scale / 2, -landDimensions.depth * scale / 2, landDimensions.width * scale, landDimensions.depth * scale);
    ctx.strokeRect(-landDimensions.width * scale / 2, -landDimensions.depth * scale / 2, landDimensions.width * scale, landDimensions.depth * scale);
    
    // Draw setbacks
    if (showSetbacks) {
      ctx.strokeStyle = '#f59e0b';
      ctx.fillStyle = 'rgba(245, 158, 11, 0.1)';
      ctx.lineWidth = 1;
      ctx.setLineDash([5, 5]);
      ctx.fillRect(-buildableArea.width * scale / 2, -buildableArea.depth * scale / 2, buildableArea.width * scale, buildableArea.depth * scale);
      ctx.strokeRect(-buildableArea.width * scale / 2, -buildableArea.depth * scale / 2, buildableArea.width * scale, buildableArea.depth * scale);
    }
    
    // Draw building footprint
    ctx.strokeStyle = '#3b82f6';
    ctx.fillStyle = 'rgba(59, 130, 246, 0.3)';
    ctx.lineWidth = 2;
    ctx.setLineDash([]);
    ctx.fillRect(-buildableArea.width * scale / 2, -buildableArea.depth * scale / 2, buildableArea.width * scale, buildableArea.depth * scale);
    ctx.strokeRect(-buildableArea.width * scale / 2, -buildableArea.depth * scale / 2, buildableArea.width * scale, buildableArea.depth * scale);
  };

  const drawSideView = (ctx: CanvasRenderingContext2D) => {
    const { buildableArea, maxHeight } = envelopeData;
    const scale = 3;
    
    // Draw ground line
    ctx.strokeStyle = '#10b981';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(-buildableArea.width * scale / 2 - 20, 0);
    ctx.lineTo(buildableArea.width * scale / 2 + 20, 0);
    ctx.stroke();
    
    // Draw building envelope
    ctx.strokeStyle = '#3b82f6';
    ctx.fillStyle = 'rgba(59, 130, 246, 0.2)';
    ctx.lineWidth = 2;
    ctx.fillRect(-buildableArea.width * scale / 2, -maxHeight * scale, buildableArea.width * scale, maxHeight * scale);
    ctx.strokeRect(-buildableArea.width * scale / 2, -maxHeight * scale, buildableArea.width * scale, maxHeight * scale);
    
    // Draw floor lines
    const floorHeight = maxHeight / envelopeData.floors;
    ctx.strokeStyle = '#94a3b8';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);
    for (let i = 1; i < envelopeData.floors; i++) {
      const y = -i * floorHeight * scale;
      ctx.beginPath();
      ctx.moveTo(-buildableArea.width * scale / 2, y);
      ctx.lineTo(buildableArea.width * scale / 2, y);
      ctx.stroke();
    }
  };

  const drawIsometricRectangle = (ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, z: number) => {
    const scale = 2;
    ctx.beginPath();
    ctx.moveTo(x * scale, y * scale - z * scale);
    ctx.lineTo((x + width) * scale, y * scale - z * scale);
    ctx.lineTo((x + width) * scale, (y + height) * scale - z * scale);
    ctx.lineTo(x * scale, (y + height) * scale - z * scale);
    ctx.closePath();
    ctx.stroke();
  };

  const drawIsometricBox = (ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, depth: number) => {
    const scale = 2;
    
    // Bottom face
    ctx.beginPath();
    ctx.moveTo(x * scale, y * scale);
    ctx.lineTo((x + width) * scale, y * scale);
    ctx.lineTo((x + width) * scale, (y + height) * scale);
    ctx.lineTo(x * scale, (y + height) * scale);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // Top face
    ctx.beginPath();
    ctx.moveTo(x * scale, y * scale - depth * scale);
    ctx.lineTo((x + width) * scale, y * scale - depth * scale);
    ctx.lineTo((x + width) * scale, (y + height) * scale - depth * scale);
    ctx.lineTo(x * scale, (y + height) * scale - depth * scale);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // Side faces
    ctx.beginPath();
    ctx.moveTo(x * scale, y * scale);
    ctx.lineTo(x * scale, y * scale - depth * scale);
    ctx.lineTo(x * scale, (y + height) * scale - depth * scale);
    ctx.lineTo(x * scale, (y + height) * scale);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo((x + width) * scale, y * scale);
    ctx.lineTo((x + width) * scale, y * scale - depth * scale);
    ctx.lineTo((x + width) * scale, (y + height) * scale - depth * scale);
    ctx.lineTo((x + width) * scale, (y + height) * scale);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
  };

  const drawDimensions = (ctx: CanvasRenderingContext2D) => {
    ctx.fillStyle = '#374151';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    
    // Add dimension labels
    ctx.fillText(`${envelopeData.maxHeight}m height`, 0, -100);
    ctx.fillText(`${envelopeData.buildableArea.width.toFixed(1)}m width`, 0, 120);
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">View:</span>
          <div className="flex bg-white rounded-lg border border-gray-200">
            {[
              { id: '3d', label: '3D', icon: Eye },
              { id: 'top', label: 'Top', icon: Maximize },
              { id: 'side', label: 'Side', icon: Settings }
            ].map((view) => {
              const Icon = view.icon;
              return (
                <button
                  key={view.id}
                  onClick={() => setViewMode(view.id as any)}
                  className={`px-3 py-2 text-sm font-medium flex items-center gap-1 ${
                    viewMode === view.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {view.label}
                </button>
              );
            })}
          </div>
        </div>

        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={showSetbacks}
              onChange={(e) => setShowSetbacks(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            Show Setbacks
          </label>
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={showDimensions}
              onChange={(e) => setShowDimensions(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            Show Dimensions
          </label>
        </div>

        {viewMode === '3d' && (
          <div className="flex items-center gap-2">
            <button
              onClick={() => setZoom(Math.max(0.5, zoom - 0.1))}
              className="p-2 bg-white border border-gray-200 rounded hover:bg-gray-50"
            >
              <ZoomOut className="h-4 w-4" />
            </button>
            <button
              onClick={() => setZoom(Math.min(2, zoom + 0.1))}
              className="p-2 bg-white border border-gray-200 rounded hover:bg-gray-50"
            >
              <ZoomIn className="h-4 w-4" />
            </button>
            <button
              onClick={() => setRotation({ x: 20, y: 45 })}
              className="p-2 bg-white border border-gray-200 rounded hover:bg-gray-50"
            >
              <RotateCcw className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Visualization Canvas */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <canvas
          ref={canvasRef}
          width={800}
          height={600}
          className="w-full h-auto border border-gray-100 rounded"
          style={{ maxHeight: '600px' }}
        />
      </div>

      {/* Building Envelope Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-2 mb-2">
            <Info className="h-4 w-4 text-blue-500" />
            <h4 className="font-medium text-gray-900">Maximum Height</h4>
          </div>
          <div className="text-2xl font-bold text-blue-600">{envelopeData.maxHeight}m</div>
          <div className="text-sm text-gray-500">{envelopeData.floors} floors</div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-2 mb-2">
            <Info className="h-4 w-4 text-green-500" />
            <h4 className="font-medium text-gray-900">Buildable Area</h4>
          </div>
          <div className="text-2xl font-bold text-green-600">
            {(envelopeData.buildableArea.width * envelopeData.buildableArea.depth).toFixed(0)}m²
          </div>
          <div className="text-sm text-gray-500">
            {envelopeData.buildableArea.width.toFixed(1)}m × {envelopeData.buildableArea.depth.toFixed(1)}m
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-2 mb-2">
            <Info className="h-4 w-4 text-purple-500" />
            <h4 className="font-medium text-gray-900">Floor Space Ratio</h4>
          </div>
          <div className="text-2xl font-bold text-purple-600">{property.buildingEnvelope.floorSpaceRatio}:1</div>
          <div className="text-sm text-gray-500">{envelopeData.totalFloorArea.toFixed(0)}m² total</div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-2 mb-2">
            <Info className="h-4 w-4 text-orange-500" />
            <h4 className="font-medium text-gray-900">Site Coverage</h4>
          </div>
          <div className="text-2xl font-bold text-orange-600">{property.buildingEnvelope.siteCoverage}%</div>
          <div className="text-sm text-gray-500">Maximum coverage allowed</div>
        </div>
      </div>
    </div>
  );
}
