'use client';

import { useState, useEffect } from 'react';
import { MapPin, Calendar, CheckCircle, XCircle, Clock, FileText, Filter, Search } from 'lucide-react';

interface PropertyDetails {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  zoning: string;
  developmentScore: number;
  coordinates: {
    lat: number;
    lng: number;
  };
  buildingEnvelope: {
    maxHeight: number;
    setbacks: {
      front: number;
      rear: number;
      side: number;
    };
    floorSpaceRatio: number;
    siteCoverage: number;
  };
  developmentPotential: {
    maxDwellings: number;
    estimatedValue: number;
    roi: number;
    developmentCost: number;
    profit: number;
  };
}

interface DAPrecedentAnalysisProps {
  property: PropertyDetails;
}

interface DevelopmentApplication {
  id: string;
  address: string;
  suburb: string;
  distance: number; // km from subject property
  status: 'approved' | 'refused' | 'pending' | 'withdrawn';
  applicationDate: string;
  decisionDate?: string;
  dwellings: number;
  landSize: number;
  height: number;
  developmentType: string;
  applicant: string;
  description: string;
  conditions?: string[];
  refusalReasons?: string[];
  processingTime?: number; // days
  relevanceScore: number; // 0-100
}

export default function DAPrecedentAnalysis({ property }: DAPrecedentAnalysisProps) {
  const [applications, setApplications] = useState<DevelopmentApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<DevelopmentApplication[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch DA precedents
    setTimeout(() => {
      const mockApplications: DevelopmentApplication[] = [
        {
          id: 'DA-2023-001',
          address: '47 Collins Street',
          suburb: property.suburb,
          distance: 0.2,
          status: 'approved',
          applicationDate: '2023-03-15',
          decisionDate: '2023-06-20',
          dwellings: 14,
          landSize: 850,
          height: 42,
          developmentType: 'Mixed Use',
          applicant: 'Collins Development Pty Ltd',
          description: '14 apartment development with ground floor retail',
          conditions: [
            'Maximum building height of 42 metres',
            'Minimum 3m setback from side boundaries',
            'Provision of 20 car parking spaces',
            'Landscaping plan to be submitted'
          ],
          processingTime: 97,
          relevanceScore: 95
        },
        {
          id: 'DA-2023-002',
          address: '123 Queen Street',
          suburb: property.suburb,
          distance: 0.5,
          status: 'approved',
          applicationDate: '2023-01-10',
          decisionDate: '2023-04-15',
          dwellings: 8,
          landSize: 600,
          height: 28,
          developmentType: 'Residential',
          applicant: 'Urban Living Group',
          description: '8 townhouse development with basement parking',
          conditions: [
            'Maximum building height of 28 metres',
            'Minimum 6m rear setback',
            'Deep soil landscaping requirement',
            'Acoustic treatment for traffic noise'
          ],
          processingTime: 95,
          relevanceScore: 82
        },
        {
          id: 'DA-2023-003',
          address: '89 King Street',
          suburb: property.suburb,
          distance: 0.8,
          status: 'refused',
          applicationDate: '2023-02-20',
          decisionDate: '2023-05-10',
          dwellings: 20,
          landSize: 750,
          height: 55,
          developmentType: 'Residential',
          applicant: 'Metro Developments',
          description: '20 apartment development with rooftop amenities',
          refusalReasons: [
            'Excessive building height for the zone',
            'Inadequate setbacks from boundaries',
            'Insufficient car parking provision',
            'Overdevelopment of the site'
          ],
          processingTime: 79,
          relevanceScore: 78
        },
        {
          id: 'DA-2023-004',
          address: '156 Smith Street',
          suburb: property.suburb,
          distance: 1.2,
          status: 'pending',
          applicationDate: '2023-08-05',
          dwellings: 12,
          landSize: 720,
          height: 38,
          developmentType: 'Mixed Use',
          applicant: 'Smith Street Properties',
          description: '12 apartment development with commercial ground floor',
          processingTime: 45, // days so far
          relevanceScore: 88
        }
      ];

      setApplications(mockApplications);
      setFilteredApplications(mockApplications);
      setLoading(false);
    }, 1000);
  }, [property]);

  useEffect(() => {
    let filtered = applications;

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(app => app.developmentType.toLowerCase().includes(typeFilter.toLowerCase()));
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(app => 
        app.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.applicant.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort by relevance score
    filtered.sort((a, b) => b.relevanceScore - a.relevanceScore);

    setFilteredApplications(filtered);
  }, [applications, statusFilter, typeFilter, searchTerm]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'refused':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'refused':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading development applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            Development Application Precedents
          </h3>
          <div className="text-sm text-gray-600">
            {filteredApplications.length} of {applications.length} applications
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by address, description, or applicant..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="approved">Approved</option>
                <option value="refused">Refused</option>
                <option value="pending">Pending</option>
              </select>
            </div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="residential">Residential</option>
              <option value="mixed">Mixed Use</option>
              <option value="commercial">Commercial</option>
            </select>
          </div>
        </div>
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {filteredApplications.map((app) => (
          <div key={app.id} className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-4">
                {getStatusIcon(app.status)}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">{app.address}</h4>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      {app.distance}km away
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      Applied {formatDate(app.applicationDate)}
                    </div>
                    {app.decisionDate && (
                      <div>
                        Decided {formatDate(app.decisionDate)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <div className="text-sm text-gray-500">Relevance</div>
                  <div className="text-lg font-bold text-blue-600">{app.relevanceScore}%</div>
                </div>
                <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(app.status)}`}>
                  {app.status}
                </span>
              </div>
            </div>

            <p className="text-gray-700 mb-4">{app.description}</p>

            {/* Key Details */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <div className="text-sm text-gray-500">Dwellings</div>
                <div className="font-semibold">{app.dwellings}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Land Size</div>
                <div className="font-semibold">{app.landSize}m²</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Height</div>
                <div className="font-semibold">{app.height}m</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Processing Time</div>
                <div className="font-semibold">
                  {app.processingTime} days
                  {app.status === 'pending' && ' (ongoing)'}
                </div>
              </div>
            </div>

            {/* Conditions or Refusal Reasons */}
            {app.status === 'approved' && app.conditions && (
              <div className="border-t pt-4">
                <h5 className="font-medium text-gray-900 mb-2">Approval Conditions</h5>
                <ul className="space-y-1">
                  {app.conditions.map((condition, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      {condition}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {app.status === 'refused' && app.refusalReasons && (
              <div className="border-t pt-4">
                <h5 className="font-medium text-gray-900 mb-2">Refusal Reasons</h5>
                <ul className="space-y-1">
                  {app.refusalReasons.map((reason, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                      <XCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                      {reason}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div className="border-t pt-4 mt-4">
              <div className="text-sm text-gray-600">
                <strong>Applicant:</strong> {app.applicant} • <strong>Application ID:</strong> {app.id}
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredApplications.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
          <p className="text-gray-600">Try adjusting your search criteria or filters</p>
        </div>
      )}

      {/* Summary Insights */}
      {filteredApplications.length > 0 && (
        <div className="bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">Key Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="text-blue-700 font-medium">Approval Rate</div>
              <div className="text-blue-800 mt-1">
                {Math.round((applications.filter(app => app.status === 'approved').length / applications.length) * 100)}% 
                of similar applications approved
              </div>
            </div>
            <div>
              <div className="text-blue-700 font-medium">Average Processing Time</div>
              <div className="text-blue-800 mt-1">
                {Math.round(applications.filter(app => app.processingTime).reduce((sum, app) => sum + (app.processingTime || 0), 0) / applications.filter(app => app.processingTime).length)} days
                for decision
              </div>
            </div>
            <div>
              <div className="text-blue-700 font-medium">Common Conditions</div>
              <div className="text-blue-800 mt-1">
                Height limits, setback requirements,<br/>
                parking provisions most common
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
