'use client';

import { Al<PERSON><PERSON>riangle, BarChart3, Calendar, DollarSign, Target, TrendingUp } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

interface PropertyDetails {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  zoning: string;
  developmentScore: number;
  coordinates: {
    lat: number;
    lng: number;
  };
  buildingEnvelope: {
    maxHeight: number;
    setbacks: {
      front: number;
      rear: number;
      side: number;
    };
    floorSpaceRatio: number;
    siteCoverage: number;
  };
  developmentPotential: {
    maxDwellings: number;
    estimatedValue: number;
    roi: number;
    developmentCost: number;
    profit: number;
  };
}

interface ROIProjectionsProps {
  property: PropertyDetails;
}

interface Scenario {
  name: string;
  dwellings: number;
  totalCost: number;
  totalRevenue: number;
  profit: number;
  roi: number;
  timeline: number;
  riskLevel: 'low' | 'medium' | 'high';
}

interface CashFlowItem {
  month: number;
  cashIn: number;
  cashOut: number;
  netCashFlow: number;
  cumulativeCashFlow: number;
}

export default function ROIProjections({ property }: ROIProjectionsProps) {
  const [selectedScenario, setSelectedScenario] = useState<string>('conservative');
  const [scenarios, setScenarios] = useState<{ [key: string]: Scenario }>({});
  const [cashFlow, setCashFlow] = useState<CashFlowItem[]>([]);
  const [sensitivityData, setSensitivityData] = useState<any[]>([]);

  const generateScenarios = useCallback(() => {
    const baseUnitPrice = property.suburb === 'Melbourne' ? 650000 : 
                         property.suburb === 'Sydney' ? 800000 : 550000;
    const baseCostPerSqm = 2800;

    const scenarioData = {
      conservative: {
        name: 'Conservative',
        dwellings: Math.floor(property.developmentPotential.maxDwellings * 0.7),
        unitPrice: baseUnitPrice * 0.9,
        costMultiplier: 1.1,
        timeline: 30,
        riskLevel: 'low' as const
      },
      moderate: {
        name: 'Moderate',
        dwellings: Math.floor(property.developmentPotential.maxDwellings * 0.85),
        unitPrice: baseUnitPrice,
        costMultiplier: 1.0,
        timeline: 24,
        riskLevel: 'medium' as const
      },
      aggressive: {
        name: 'Aggressive',
        dwellings: property.developmentPotential.maxDwellings,
        unitPrice: baseUnitPrice * 1.15,
        costMultiplier: 0.95,
        timeline: 18,
        riskLevel: 'high' as const
      }
    };

    const generatedScenarios: { [key: string]: Scenario } = {};

    Object.entries(scenarioData).forEach(([key, data]) => {
      const landCost = property.currentPrice;
      const constructionCost = data.dwellings * 75 * baseCostPerSqm * data.costMultiplier;
      const softCosts = (landCost + constructionCost) * 0.15;
      const totalCost = landCost + constructionCost + softCosts;
      const totalRevenue = data.dwellings * data.unitPrice;
      const profit = totalRevenue - totalCost;
      const roi = (profit / totalCost) * 100;

      generatedScenarios[key] = {
        name: data.name,
        dwellings: data.dwellings,
        totalCost,
        totalRevenue,
        profit,
        roi,
        timeline: data.timeline,
        riskLevel: data.riskLevel
      };
    });

    setScenarios(generatedScenarios);
  }, [property]);

  useEffect(() => {
    generateScenarios();
  }, [generateScenarios]);

  useEffect(() => {
    if (scenarios[selectedScenario]) {
      generateCashFlow(scenarios[selectedScenario]);
      generateSensitivityAnalysis(scenarios[selectedScenario]);
    }
  }, [selectedScenario, scenarios]);

  const generateCashFlow = (scenario: Scenario) => {
    const cashFlowData: CashFlowItem[] = [];
    let cumulativeCashFlow = 0;

    // Development phase (first 80% of timeline)
    const developmentMonths = Math.floor(scenario.timeline * 0.8);
    const monthlyCost = scenario.totalCost / developmentMonths;

    for (let month = 1; month <= developmentMonths; month++) {
      const cashOut = monthlyCost;
      const cashIn = 0;
      const netCashFlow = cashIn - cashOut;
      cumulativeCashFlow += netCashFlow;

      cashFlowData.push({
        month,
        cashIn,
        cashOut,
        netCashFlow,
        cumulativeCashFlow
      });
    }

    // Sales phase (last 20% of timeline)
    const salesMonths = scenario.timeline - developmentMonths;
    const monthlyRevenue = scenario.totalRevenue / salesMonths;

    for (let month = developmentMonths + 1; month <= scenario.timeline; month++) {
      const cashIn = monthlyRevenue;
      const cashOut = 0;
      const netCashFlow = cashIn - cashOut;
      cumulativeCashFlow += netCashFlow;

      cashFlowData.push({
        month,
        cashIn,
        cashOut,
        netCashFlow,
        cumulativeCashFlow
      });
    }

    setCashFlow(cashFlowData);
  };

  const generateSensitivityAnalysis = (scenario: Scenario) => {
    const variables = [
      { name: 'Construction Cost', baseValue: scenario.totalCost * 0.7, variations: [-20, -10, 0, 10, 20] },
      { name: 'Sale Price', baseValue: scenario.totalRevenue / scenario.dwellings, variations: [-15, -7.5, 0, 7.5, 15] },
      { name: 'Timeline', baseValue: scenario.timeline, variations: [-25, -12.5, 0, 12.5, 25] }
    ];

    const sensitivityResults = variables.map(variable => {
      const impacts = variable.variations.map(variation => {
        let adjustedCost = scenario.totalCost;
        let adjustedRevenue = scenario.totalRevenue;
        let adjustedTimeline = scenario.timeline;

        if (variable.name === 'Construction Cost') {
          adjustedCost = scenario.totalCost * (1 + variation / 100);
        } else if (variable.name === 'Sale Price') {
          adjustedRevenue = scenario.totalRevenue * (1 + variation / 100);
        } else if (variable.name === 'Timeline') {
          adjustedTimeline = scenario.timeline * (1 + variation / 100);
        }

        const adjustedProfit = adjustedRevenue - adjustedCost;
        const adjustedROI = (adjustedProfit / adjustedCost) * 100;
        const roiChange = adjustedROI - scenario.roi;

        return {
          variation,
          roi: adjustedROI,
          roiChange
        };
      });

      return {
        variable: variable.name,
        impacts
      };
    });

    setSensitivityData(sensitivityResults);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Scenario Selection */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Target className="h-5 w-5 text-blue-600" />
          ROI Projection Scenarios
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(scenarios).map(([key, scenario]) => (
            <div
              key={key}
              onClick={() => setSelectedScenario(key)}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedScenario === key
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{scenario.name}</h4>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskColor(scenario.riskLevel)}`}>
                  {scenario.riskLevel} risk
                </span>
              </div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Units:</span>
                  <span className="font-medium">{scenario.dwellings}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">ROI:</span>
                  <span className="font-medium text-green-600">{scenario.roi.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Timeline:</span>
                  <span className="font-medium">{scenario.timeline} months</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Profit:</span>
                  <span className="font-medium">{formatCurrency(scenario.profit)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {scenarios[selectedScenario] && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-5 w-5 text-green-500" />
                <h4 className="font-medium text-gray-900">Total ROI</h4>
              </div>
              <div className="text-3xl font-bold text-green-600">
                {scenarios[selectedScenario].roi.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">Return on investment</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="h-5 w-5 text-blue-500" />
                <h4 className="font-medium text-gray-900">Net Profit</h4>
              </div>
              <div className="text-3xl font-bold text-blue-600">
                {formatCurrency(scenarios[selectedScenario].profit)}
              </div>
              <div className="text-sm text-gray-500">After all costs</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-5 w-5 text-purple-500" />
                <h4 className="font-medium text-gray-900">Timeline</h4>
              </div>
              <div className="text-3xl font-bold text-purple-600">
                {scenarios[selectedScenario].timeline}
              </div>
              <div className="text-sm text-gray-500">Months to completion</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="h-5 w-5 text-orange-500" />
                <h4 className="font-medium text-gray-900">Profit Margin</h4>
              </div>
              <div className="text-3xl font-bold text-orange-600">
                {((scenarios[selectedScenario].profit / scenarios[selectedScenario].totalRevenue) * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">Profit as % of revenue</div>
            </div>
          </div>

          {/* Cash Flow Chart */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Cash Flow Projection
            </h3>
            
            <div className="h-64 flex items-end justify-between gap-1 border-b border-gray-200 pb-4">
              {cashFlow.slice(0, 24).map((item, index) => {
                const maxValue = Math.max(...cashFlow.map(cf => Math.abs(cf.cumulativeCashFlow)));
                const height = Math.abs(item.cumulativeCashFlow) / maxValue * 200;
                const isNegative = item.cumulativeCashFlow < 0;
                
                return (
                  <div key={index} className="flex flex-col items-center">
                    <div
                      className={`w-3 ${isNegative ? 'bg-red-400' : 'bg-green-400'} rounded-t`}
                      style={{ height: `${height}px` }}
                      title={`Month ${item.month}: ${formatCurrency(item.cumulativeCashFlow)}`}
                    />
                    {index % 3 === 0 && (
                      <div className="text-xs text-gray-500 mt-1">{item.month}</div>
                    )}
                  </div>
                );
              })}
            </div>
            
            <div className="flex items-center justify-center gap-6 mt-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-400 rounded"></div>
                <span className="text-gray-600">Negative Cash Flow</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-400 rounded"></div>
                <span className="text-gray-600">Positive Cash Flow</span>
              </div>
            </div>
          </div>

          {/* Sensitivity Analysis */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Sensitivity Analysis
            </h3>
            
            <div className="space-y-6">
              {sensitivityData.map((data, index) => (
                <div key={index}>
                  <h4 className="font-medium text-gray-900 mb-3">{data.variable} Impact on ROI</h4>
                  <div className="grid grid-cols-5 gap-2">
                    {data.impacts.map((impact: any, impactIndex: number) => (
                      <div key={impactIndex} className="text-center">
                        <div className={`p-3 rounded-lg ${
                          impact.roiChange < -5 ? 'bg-red-100 text-red-800' :
                          impact.roiChange < 0 ? 'bg-yellow-100 text-yellow-800' :
                          impact.roiChange === 0 ? 'bg-gray-100 text-gray-800' :
                          impact.roiChange < 5 ? 'bg-green-100 text-green-800' :
                          'bg-green-200 text-green-900'
                        }`}>
                          <div className="text-xs font-medium">{impact.variation > 0 ? '+' : ''}{impact.variation}%</div>
                          <div className="text-sm font-bold">{impact.roi.toFixed(1)}%</div>
                          <div className="text-xs">
                            {impact.roiChange > 0 ? '+' : ''}{impact.roiChange.toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
