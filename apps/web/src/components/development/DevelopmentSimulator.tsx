'use client';

import { Calculator, DollarSign, Home, Play, RotateCcw, TrendingUp } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

interface PropertyDetails {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  zoning: string;
  developmentScore: number;
  coordinates: {
    lat: number;
    lng: number;
  };
  buildingEnvelope: {
    maxHeight: number;
    setbacks: {
      front: number;
      rear: number;
      side: number;
    };
    floorSpaceRatio: number;
    siteCoverage: number;
  };
  developmentPotential: {
    maxDwellings: number;
    estimatedValue: number;
    roi: number;
    developmentCost: number;
    profit: number;
  };
}

interface DevelopmentSimulatorProps {
  property: PropertyDetails;
}

interface SimulationParams {
  dwellings: number;
  unitSize: number;
  developmentType: 'apartments' | 'townhouses' | 'mixed';
  constructionQuality: 'standard' | 'premium' | 'luxury';
  timeline: number; // months
}

interface SimulationResults {
  totalCost: number;
  totalRevenue: number;
  profit: number;
  roi: number;
  irr: number;
  paybackPeriod: number;
  riskScore: number;
}

export default function DevelopmentSimulator({ property }: DevelopmentSimulatorProps) {
  const [params, setParams] = useState<SimulationParams>({
    dwellings: Math.floor(property.developmentPotential.maxDwellings * 0.8),
    unitSize: 75,
    developmentType: 'apartments',
    constructionQuality: 'standard',
    timeline: 24
  });

  const [results, setResults] = useState<SimulationResults | null>(null);
  const [isSimulating, setIsSimulating] = useState(false);

  // Calculate simulation results
  const runSimulation = useCallback(() => {
    setIsSimulating(true);
    
    // Simulate processing delay
    setTimeout(() => {
      const landCost = property.currentPrice;
      const constructionCostPerSqm = {
        standard: 2500,
        premium: 3500,
        luxury: 5000
      }[params.constructionQuality];

      const totalConstructionArea = params.dwellings * params.unitSize;
      const constructionCost = totalConstructionArea * constructionCostPerSqm;
      
      // Additional costs
      const softCosts = (landCost + constructionCost) * 0.15; // 15% for professional fees, permits, etc.
      const marketingCosts = constructionCost * 0.03; // 3% marketing
      const contingency = constructionCost * 0.1; // 10% contingency
      
      const totalCost = landCost + constructionCost + softCosts + marketingCosts + contingency;
      
      // Revenue calculation
      const avgUnitPrice = {
        apartments: property.suburb === 'Melbourne' ? 650000 : property.suburb === 'Sydney' ? 800000 : 550000,
        townhouses: property.suburb === 'Melbourne' ? 750000 : property.suburb === 'Sydney' ? 950000 : 650000,
        mixed: property.suburb === 'Melbourne' ? 700000 : property.suburb === 'Sydney' ? 875000 : 600000
      }[params.developmentType];

      const qualityMultiplier = {
        standard: 1.0,
        premium: 1.2,
        luxury: 1.5
      }[params.constructionQuality];

      const totalRevenue = params.dwellings * avgUnitPrice * qualityMultiplier;
      const profit = totalRevenue - totalCost;
      const roi = (profit / totalCost) * 100;
      
      // IRR calculation (simplified)
      const annualReturn = profit / (params.timeline / 12);
      const irr = (annualReturn / totalCost) * 100;
      
      const paybackPeriod = totalCost / (profit / (params.timeline / 12));
      
      // Risk score (0-100, lower is better)
      const marketRisk = params.timeline > 24 ? 30 : 20;
      const constructionRisk = params.dwellings > 10 ? 25 : 15;
      const qualityRisk = params.constructionQuality === 'luxury' ? 20 : 10;
      const riskScore = Math.min(100, marketRisk + constructionRisk + qualityRisk);

      setResults({
        totalCost,
        totalRevenue,
        profit,
        roi,
        irr,
        paybackPeriod,
        riskScore
      });
      
      setIsSimulating(false);
    }, 2000);
  }, [params, property]);

  useEffect(() => {
    runSimulation();
  }, [runSimulation]);

  const resetToDefaults = () => {
    setParams({
      dwellings: Math.floor(property.developmentPotential.maxDwellings * 0.8),
      unitSize: 75,
      developmentType: 'apartments',
      constructionQuality: 'standard',
      timeline: 24
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Simulation Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Calculator className="h-5 w-5 text-blue-600" />
            Development Simulator
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={resetToDefaults}
              className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </button>
            <button
              onClick={runSimulation}
              disabled={isSimulating}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {isSimulating ? 'Simulating...' : 'Run Simulation'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Number of Dwellings */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Number of Dwellings
            </label>
            <input
              type="range"
              min="1"
              max={property.developmentPotential.maxDwellings}
              value={params.dwellings}
              onChange={(e) => setParams({ ...params, dwellings: parseInt(e.target.value) })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>1</span>
              <span className="font-medium text-blue-600">{params.dwellings}</span>
              <span>{property.developmentPotential.maxDwellings}</span>
            </div>
          </div>

          {/* Average Unit Size */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Average Unit Size (m²)
            </label>
            <input
              type="range"
              min="45"
              max="150"
              value={params.unitSize}
              onChange={(e) => setParams({ ...params, unitSize: parseInt(e.target.value) })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>45m²</span>
              <span className="font-medium text-blue-600">{params.unitSize}m²</span>
              <span>150m²</span>
            </div>
          </div>

          {/* Development Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Development Type
            </label>
            <select
              value={params.developmentType}
              onChange={(e) => setParams({ ...params, developmentType: e.target.value as any })}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="apartments">Apartments</option>
              <option value="townhouses">Townhouses</option>
              <option value="mixed">Mixed Development</option>
            </select>
          </div>

          {/* Construction Quality */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Construction Quality
            </label>
            <select
              value={params.constructionQuality}
              onChange={(e) => setParams({ ...params, constructionQuality: e.target.value as any })}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="standard">Standard</option>
              <option value="premium">Premium</option>
              <option value="luxury">Luxury</option>
            </select>
          </div>
        </div>

        {/* Timeline */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Development Timeline (months)
          </label>
          <input
            type="range"
            min="12"
            max="48"
            value={params.timeline}
            onChange={(e) => setParams({ ...params, timeline: parseInt(e.target.value) })}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-sm text-gray-500 mt-1">
            <span>12 months</span>
            <span className="font-medium text-blue-600">{params.timeline} months</span>
            <span>48 months</span>
          </div>
        </div>
      </div>

      {/* Simulation Results */}
      {isSimulating ? (
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Running Simulation</h3>
          <p className="text-gray-600">Analyzing development scenarios and calculating projections...</p>
        </div>
      ) : results ? (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="h-5 w-5 text-green-500" />
                <h4 className="font-medium text-gray-900">Total Profit</h4>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(results.profit)}
              </div>
              <div className="text-sm text-gray-500">After all costs</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-5 w-5 text-blue-500" />
                <h4 className="font-medium text-gray-900">ROI</h4>
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {results.roi.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">Return on investment</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <Calculator className="h-5 w-5 text-purple-500" />
                <h4 className="font-medium text-gray-900">IRR</h4>
              </div>
              <div className="text-2xl font-bold text-purple-600">
                {results.irr.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">Internal rate of return</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <Home className="h-5 w-5 text-orange-500" />
                <h4 className="font-medium text-gray-900">Risk Score</h4>
              </div>
              <div className={`text-2xl font-bold ${
                results.riskScore < 30 ? 'text-green-600' : 
                results.riskScore < 60 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {results.riskScore}/100
              </div>
              <div className="text-sm text-gray-500">
                {results.riskScore < 30 ? 'Low risk' : 
                 results.riskScore < 60 ? 'Medium risk' : 'High risk'}
              </div>
            </div>
          </div>

          {/* Financial Breakdown */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Breakdown</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Costs</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Land Acquisition</span>
                    <span className="font-medium">{formatCurrency(property.currentPrice)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Construction</span>
                    <span className="font-medium">{formatCurrency(results.totalCost - property.currentPrice)}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold">
                      <span>Total Costs</span>
                      <span>{formatCurrency(results.totalCost)}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Revenue</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Sales</span>
                    <span className="font-medium">{formatCurrency(results.totalRevenue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average per Unit</span>
                    <span className="font-medium">{formatCurrency(results.totalRevenue / params.dwellings)}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold text-green-600">
                      <span>Net Profit</span>
                      <span>{formatCurrency(results.profit)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Development Summary */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">Development Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-blue-700 font-medium">Development Details</div>
                <div className="text-blue-800 mt-1">
                  {params.dwellings} {params.developmentType} units<br/>
                  Average {params.unitSize}m² each<br/>
                  {params.constructionQuality} construction quality
                </div>
              </div>
              <div>
                <div className="text-blue-700 font-medium">Timeline & Returns</div>
                <div className="text-blue-800 mt-1">
                  {params.timeline} month development<br/>
                  {results.paybackPeriod.toFixed(1)} year payback period<br/>
                  {results.roi.toFixed(1)}% total return
                </div>
              </div>
              <div>
                <div className="text-blue-700 font-medium">Risk Assessment</div>
                <div className="text-blue-800 mt-1">
                  {results.riskScore < 30 ? 'Low' : results.riskScore < 60 ? 'Medium' : 'High'} risk profile<br/>
                  Market conditions: Favorable<br/>
                  Construction risk: Manageable
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
}
