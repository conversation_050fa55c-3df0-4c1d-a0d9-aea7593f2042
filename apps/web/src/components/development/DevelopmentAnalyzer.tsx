'use client';

import { useState } from 'react';
import { Building, TrendingUp, AlertCircle, CheckCircle, Info, Calculator } from 'lucide-react';

interface PropertyDetails {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  zoning: string;
  developmentScore: number;
  coordinates: {
    lat: number;
    lng: number;
  };
  buildingEnvelope: {
    maxHeight: number;
    setbacks: {
      front: number;
      rear: number;
      side: number;
    };
    floorSpaceRatio: number;
    siteCoverage: number;
  };
  developmentPotential: {
    maxDwellings: number;
    estimatedValue: number;
    roi: number;
    developmentCost: number;
    profit: number;
  };
}

interface DevelopmentAnalyzerProps {
  property: PropertyDetails;
}

// Mock analysis data
const generateAnalysisData = (property: PropertyDetails) => ({
  feasibilityScore: property.developmentScore,
  constraints: [
    {
      type: 'Planning',
      status: 'compliant',
      description: 'Property meets current zoning requirements',
      impact: 'positive'
    },
    {
      type: 'Heritage',
      status: 'clear',
      description: 'No heritage overlays affecting development',
      impact: 'neutral'
    },
    {
      type: 'Environmental',
      status: 'minor',
      description: 'Minor environmental considerations for stormwater',
      impact: 'manageable'
    },
    {
      type: 'Infrastructure',
      status: 'adequate',
      description: 'Existing infrastructure can support development',
      impact: 'positive'
    }
  ],
  opportunities: [
    {
      title: 'High Density Zoning',
      description: 'Mixed-use zoning allows for maximum dwelling density',
      impact: 'High',
      value: '+$800k'
    },
    {
      title: 'Transport Proximity',
      description: 'Within 400m of major transport hub',
      impact: 'Medium',
      value: '+$400k'
    },
    {
      title: 'Market Demand',
      description: 'Strong rental demand in area (98% occupancy)',
      impact: 'High',
      value: '+$600k'
    }
  ],
  risks: [
    {
      title: 'Construction Costs',
      description: 'Rising material costs may impact margins',
      probability: 'Medium',
      impact: 'Medium',
      mitigation: 'Lock in fixed-price contracts early'
    },
    {
      title: 'Market Saturation',
      description: '3 similar developments planned nearby',
      probability: 'Low',
      impact: 'Medium',
      mitigation: 'Differentiate with premium finishes'
    }
  ],
  recommendations: [
    'Proceed with detailed feasibility study',
    'Engage town planner for pre-application advice',
    'Consider staged development approach',
    'Investigate government incentives for affordable housing'
  ]
});

export default function DevelopmentAnalyzer({ property }: DevelopmentAnalyzerProps) {
  const [analysisData] = useState(() => generateAnalysisData(property));

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
      case 'clear':
      case 'adequate':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'minor':
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact.toLowerCase()) {
      case 'high':
        return 'text-green-600 bg-green-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Feasibility Overview */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Calculator className="h-5 w-5 text-blue-600" />
            Development Feasibility Analysis
          </h3>
          <div className="text-right">
            <div className="text-sm text-gray-600">Feasibility Score</div>
            <div className="text-3xl font-bold text-blue-600">{analysisData.feasibilityScore}/100</div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Development Potential</div>
            <div className="text-xl font-bold text-green-600">
              {property.developmentPotential.maxDwellings} Units
            </div>
            <div className="text-sm text-gray-500">Maximum density achievable</div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Estimated Profit</div>
            <div className="text-xl font-bold text-green-600">
              ${(property.developmentPotential.profit / 1000000).toFixed(1)}M
            </div>
            <div className="text-sm text-gray-500">After all costs</div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">ROI Projection</div>
            <div className="text-xl font-bold text-green-600">
              {property.developmentPotential.roi}%
            </div>
            <div className="text-sm text-gray-500">Return on investment</div>
          </div>
        </div>
      </div>

      {/* Constraints Analysis */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-orange-500" />
            Development Constraints
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {analysisData.constraints.map((constraint, index) => (
              <div key={index} className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                {getStatusIcon(constraint.status)}
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-900">{constraint.type}</h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      constraint.status === 'compliant' || constraint.status === 'clear' || constraint.status === 'adequate'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {constraint.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{constraint.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Opportunities */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-500" />
            Development Opportunities
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {analysisData.opportunities.map((opportunity, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{opportunity.title}</h4>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getImpactColor(opportunity.impact)}`}>
                      {opportunity.impact} Impact
                    </span>
                    <span className="text-sm font-semibold text-green-600">{opportunity.value}</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600">{opportunity.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Risk Assessment */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            Risk Assessment
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {analysisData.risks.map((risk, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{risk.title}</h4>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskColor(risk.probability)}`}>
                      {risk.probability}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskColor(risk.impact)}`}>
                      {risk.impact} Impact
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">{risk.description}</p>
                <div className="bg-blue-50 p-3 rounded">
                  <div className="text-xs font-medium text-blue-900 mb-1">Mitigation Strategy</div>
                  <div className="text-sm text-blue-800">{risk.mitigation}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Building className="h-5 w-5 text-blue-500" />
            Next Steps & Recommendations
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-3">
            {analysisData.recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </div>
                <p className="text-gray-700">{recommendation}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
