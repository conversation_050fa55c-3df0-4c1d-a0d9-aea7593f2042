'use client';

import {
    <PERSON><PERSON><PERSON><PERSON>gle,
    Building,
    Calculator,
    Car,
    CheckCircle,
    Home,
    Map,
    MapPin,
    School,
    Train,
    TrendingUp,
    Users,
    Zap
} from 'lucide-react';
import { useState } from 'react';

interface PropertyIntelligenceDashboardProps {
  property: {
    id: string;
    address: string;
    propertyType: string;
    bedrooms?: number;
    bathrooms?: number;
    carSpaces?: number;
    landSize?: number;
    buildingSize?: number;
    yearBuilt?: number;
    lastSalePrice?: number;
    lastSaleDate?: string;
    currentPrice?: number;
    images?: string[];
  };
}

export function PropertyIntelligenceDashboard({ property }: PropertyIntelligenceDashboardProps) {
  const [forecastPeriod, setForecastPeriod] = useState(12);

  // Mock intelligence data - in real app this would come from API
  const intelligence = {
    currentValue: 1275000,
    confidenceRange: { min: 1225000, max: 1325000 },
    monthlyChange: { amount: 25000, percentage: 2.0 },
    confidence: 87,
    dataSourcesActive: 16,
    totalDataSources: 18,
    
    scores: {
      investment: 85,
      development: 78,
      growth: 12.5,
      risk: 23,
      lifestyle: 88,
      marketPosition: 89
    },
    
    predictions: {
      12: { value: 1387500, confidence: 75 },
      24: { value: 1512500, confidence: 65 }
    },
    
    opportunities: [
      {
        type: 'Infrastructure',
        title: 'Light Rail Extension',
        description: 'New light rail station 800m away',
        impact: '+15-20%',
        timeline: 'Opening 2025',
        status: 'approved'
      },
      {
        type: 'Education',
        title: 'School Rating Improvement',
        description: 'Local school rating improved from 72 to 89',
        impact: 'High family demand',
        timeline: 'Current',
        status: 'active'
      },
      {
        type: 'Development',
        title: 'Duplex Potential',
        description: 'Similar approvals in area: 6 recent',
        impact: 'Value add: $400k+',
        timeline: '12-18 months',
        status: 'potential'
      }
    ],
    
    risks: [
      {
        type: 'Flood Risk',
        level: 'low',
        description: 'Property is 15m above flood zone'
      },
      {
        type: 'Market Risk',
        level: 'medium',
        description: 'Moderate volatility in local market'
      },
      {
        type: 'Climate Risk',
        level: 'low',
        description: 'Low bushfire and coastal erosion risk'
      }
    ],
    
    keyInsights: [
      'Property value has increased 8.5% in the last 12 months',
      'Located in top 10% of suburb for growth potential',
      'Strong rental demand with 1.2% vacancy rate',
      'Excellent transport connectivity to CBD'
    ],

    // Market Context Data
    marketContext: {
      suburbMedian: 1150000,
      suburbGrowth12m: 8.2,
      suburbGrowth3y: 24.5,
      daysOnMarket: 28,
      stockLevels: 'low',
      marketTemperature: 'hot'
    },

    // Location Intelligence Data
    locationIntelligence: {
      schools: [
        { name: 'Brisbane Grammar School', rating: 95, distance: 1.2, type: 'Private' },
        { name: 'New Farm State School', rating: 87, distance: 0.8, type: 'Public' },
        { name: 'Holy Spirit School', rating: 89, distance: 1.5, type: 'Catholic' }
      ],
      transport: [
        { type: 'Train', name: 'Fortitude Valley Station', distance: 1.8, walkTime: 22 },
        { type: 'Bus', name: 'Brunswick St', distance: 0.3, walkTime: 4 },
        { type: 'Ferry', name: 'New Farm Park', distance: 0.9, walkTime: 11 }
      ],
      amenities: [
        { type: 'Shopping', name: 'James Street Precinct', distance: 0.7 },
        { type: 'Hospital', name: 'Royal Brisbane Hospital', distance: 2.1 },
        { type: 'Park', name: 'New Farm Park', distance: 0.5 }
      ]
    },

    // Investment Metrics Data
    investmentMetrics: {
      rentalYield: 4.2,
      vacancyRate: 1.2,
      medianRent: 680,
      rentGrowth12m: 8.5,
      comparableRentals: [
        { address: '15 Bowen Terrace', rent: 650, bedrooms: 3, bathrooms: 2 },
        { address: '42 Merthyr Road', rent: 720, bedrooms: 3, bathrooms: 2 },
        { address: '8 Annie Street', rent: 675, bedrooms: 3, bathrooms: 2 }
      ],
      cashFlow: {
        weeklyRent: 680,
        annualRent: 35360,
        expenses: {
          rates: 3200,
          insurance: 1800,
          maintenance: 2500,
          management: 2830
        }
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBarColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-8">
      {/* Hero Section - Real-Time Valuation */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{property.address}</h1>
            <p className="text-gray-600 mt-1">
              {property.propertyType} • {property.bedrooms} bed, {property.bathrooms} bath, {property.carSpaces} car
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600">Live</span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="text-center lg:text-left">
              <div className="text-5xl font-bold text-gray-900 mb-2">
                {formatCurrency(intelligence.currentValue)}
              </div>
              <div className="text-lg text-gray-600 mb-4">
                Range: {formatCurrency(intelligence.confidenceRange.min)} - {formatCurrency(intelligence.confidenceRange.max)}
              </div>
              <div className="flex items-center justify-center lg:justify-start space-x-4 mb-4">
                <div className="flex items-center text-green-600">
                  <TrendingUp className="w-5 h-5 mr-1" />
                  <span className="font-semibold">
                    +{formatCurrency(intelligence.monthlyChange.amount)} (+{intelligence.monthlyChange.percentage}%)
                  </span>
                </div>
                <span className="text-gray-500">since last month</span>
              </div>
              <div className="text-sm text-gray-600">
                Confidence: {intelligence.confidence}% | {intelligence.dataSourcesActive} of {intelligence.totalDataSources} data sources active
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">Quick Stats</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Land Size:</span>
                  <span className="font-medium">{property.landSize}m²</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Building Size:</span>
                  <span className="font-medium">{property.buildingSize}m²</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Year Built:</span>
                  <span className="font-medium">{property.yearBuilt}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Sold:</span>
                  <span className="font-medium">{formatCurrency(property.lastSalePrice || 0)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Intelligence Scores Dashboard */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Intelligence Scores</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="40" stroke="#e5e7eb" strokeWidth="8" fill="none" />
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  stroke="#10b981" 
                  strokeWidth="8" 
                  fill="none"
                  strokeDasharray={`${intelligence.scores.investment * 2.51} 251`}
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xl font-bold text-gray-900">{intelligence.scores.investment}</span>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900">Investment Score</h3>
            <p className="text-sm text-gray-600">Market trends, infrastructure, quality</p>
          </div>

          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="40" stroke="#e5e7eb" strokeWidth="8" fill="none" />
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  stroke="#3b82f6" 
                  strokeWidth="8" 
                  fill="none"
                  strokeDasharray={`${intelligence.scores.development * 2.51} 251`}
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xl font-bold text-gray-900">{intelligence.scores.development}</span>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900">Development Score</h3>
            <p className="text-sm text-gray-600">Zoning, land size, DA activity</p>
          </div>

          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="40" stroke="#e5e7eb" strokeWidth="8" fill="none" />
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  stroke="#8b5cf6" 
                  strokeWidth="8" 
                  fill="none"
                  strokeDasharray={`${intelligence.scores.lifestyle * 2.51} 251`}
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xl font-bold text-gray-900">{intelligence.scores.lifestyle}</span>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900">Lifestyle Score</h3>
            <p className="text-sm text-gray-600">Schools, transport, amenities</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Growth Potential</span>
              <span className="text-lg font-bold text-green-600">+{intelligence.scores.growth}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{width: `${intelligence.scores.growth * 5}%`}}></div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Risk Score</span>
              <span className="text-lg font-bold text-green-600">{intelligence.scores.risk}/100</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{width: `${100 - intelligence.scores.risk}%`}}></div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Market Position</span>
              <span className="text-lg font-bold text-blue-600">{intelligence.scores.marketPosition}th %ile</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{width: `${intelligence.scores.marketPosition}%`}}></div>
            </div>
          </div>
        </div>
      </div>

      {/* AI Predictions & Insights */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">AI Predictions & Insights</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Value Forecast</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Forecast Period:</label>
                <select
                  value={forecastPeriod}
                  onChange={(e) => setForecastPeriod(Number(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value={12}>12 months</option>
                  <option value={24}>24 months</option>
                </select>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {formatCurrency(intelligence.predictions[forecastPeriod as keyof typeof intelligence.predictions].value)}
                  </div>
                  <div className="text-sm text-gray-600 mb-4">
                    Predicted value in {forecastPeriod} months
                  </div>
                  <div className="flex items-center justify-center space-x-4">
                    <div className="flex items-center text-green-600">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      <span className="font-semibold">
                        +{((intelligence.predictions[forecastPeriod as keyof typeof intelligence.predictions].value / intelligence.currentValue - 1) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      Confidence: {intelligence.predictions[forecastPeriod as keyof typeof intelligence.predictions].confidence}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Insights</h3>
            <div className="space-y-3">
              {intelligence.keyInsights.map((insight, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <p className="text-gray-700">{insight}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Opportunities Panel */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Opportunities</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {intelligence.opportunities.map((opportunity, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  {opportunity.type === 'Infrastructure' && <Zap className="w-5 h-5 text-blue-500" />}
                  {opportunity.type === 'Education' && <Users className="w-5 h-5 text-green-500" />}
                  {opportunity.type === 'Development' && <Building className="w-5 h-5 text-purple-500" />}
                  <span className="text-sm font-medium text-gray-600">{opportunity.type}</span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  opportunity.status === 'approved' ? 'bg-green-100 text-green-800' :
                  opportunity.status === 'active' ? 'bg-blue-100 text-blue-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {opportunity.status}
                </span>
              </div>

              <h3 className="font-semibold text-gray-900 mb-2">{opportunity.title}</h3>
              <p className="text-gray-600 text-sm mb-4">{opportunity.description}</p>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Impact:</span>
                  <span className="font-medium text-green-600">{opportunity.impact}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Timeline:</span>
                  <span className="font-medium">{opportunity.timeline}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Assessment Panel */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Risk Assessment</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {intelligence.risks.map((risk, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">{risk.type}</h3>
                <div className="flex items-center space-x-2">
                  {risk.level === 'low' && <CheckCircle className="w-5 h-5 text-green-500" />}
                  {risk.level === 'medium' && <AlertTriangle className="w-5 h-5 text-yellow-500" />}
                  {risk.level === 'high' && <AlertTriangle className="w-5 h-5 text-red-500" />}
                  <span className={`text-sm font-medium capitalize ${getRiskColor(risk.level)}`}>
                    {risk.level}
                  </span>
                </div>
              </div>
              <p className="text-gray-600 text-sm">{risk.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Interactive Maps Section */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Interactive Maps & Development Activity</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Map Placeholder */}
          <div className="space-y-4">
            <div className="bg-gray-100 rounded-lg h-80 flex items-center justify-center border-2 border-dashed border-gray-300">
              <div className="text-center">
                <Map className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 font-medium">Interactive Development Map</p>
                <p className="text-sm text-gray-500">Coming Soon - Full map integration</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <button className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                Development Activity
              </button>
              <button className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                Infrastructure Projects
              </button>
              <button className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                School Catchments
              </button>
              <button className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                Risk Zones
              </button>
            </div>
          </div>

          {/* Development Activity Summary */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Nearby Development Activity</h3>
              <div className="space-y-4">
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">Mixed-Use Development</span>
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Approved</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">350m away • 45 apartments + retail</p>
                  <div className="text-sm text-green-600 font-medium">Impact: +8-12% property value</div>
                </div>

                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">Townhouse Complex</span>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">Under Construction</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">180m away • 12 townhouses</p>
                  <div className="text-sm text-blue-600 font-medium">Completion: Q2 2025</div>
                </div>

                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">Duplex Development</span>
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">Proposed</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">220m away • 2 x 3BR duplexes</p>
                  <div className="text-sm text-yellow-600 font-medium">DA submitted Nov 2024</div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Infrastructure Projects</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Zap className="w-5 h-5 text-blue-500" />
                  <div>
                    <p className="font-medium text-gray-900">Light Rail Extension</p>
                    <p className="text-sm text-gray-600">New station 800m away • Opening 2025</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Building className="w-5 h-5 text-green-500" />
                  <div>
                    <p className="font-medium text-gray-900">Community Centre Upgrade</p>
                    <p className="text-sm text-gray-600">New facilities 600m away • Completed 2024</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Car className="w-5 h-5 text-purple-500" />
                  <div>
                    <p className="font-medium text-gray-900">Road Improvements</p>
                    <p className="text-sm text-gray-600">Brunswick St upgrade • Planned 2025</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Market Context Section */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Market Context</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Suburb Comparison</h3>
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Suburb Median</span>
                  <span className="text-lg font-bold text-gray-900">{formatCurrency(intelligence.marketContext.suburbMedian)}</span>
                </div>
                <div className="text-sm text-gray-600">
                  This property: {((intelligence.currentValue / intelligence.marketContext.suburbMedian - 1) * 100).toFixed(1)}% above median
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">12-Month Growth</span>
                  <span className="text-lg font-bold text-green-600">+{intelligence.marketContext.suburbGrowth12m}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{width: `${intelligence.marketContext.suburbGrowth12m * 8}%`}}></div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">3-Year Growth</span>
                  <span className="text-lg font-bold text-blue-600">+{intelligence.marketContext.suburbGrowth3y}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{width: `${intelligence.marketContext.suburbGrowth3y * 2}%`}}></div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Market Temperature</h3>
            <div className="space-y-4">
              <div className="text-center">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-red-100 text-red-800 font-semibold text-lg mb-2">
                  🔥 {intelligence.marketContext.marketTemperature.toUpperCase()} MARKET
                </div>
                <p className="text-sm text-gray-600">High demand, low supply</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{intelligence.marketContext.daysOnMarket}</div>
                  <div className="text-sm text-gray-600">Days on Market</div>
                  <div className="text-xs text-green-600 mt-1">15% below average</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 capitalize">{intelligence.marketContext.stockLevels}</div>
                  <div className="text-sm text-gray-600">Stock Levels</div>
                  <div className="text-xs text-red-600 mt-1">High competition</div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2">Market Insights</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• Properties selling 8% above asking price</li>
                  <li>• 73% of sales within first 2 weeks</li>
                  <li>• Auction clearance rate: 89%</li>
                  <li>• Strong buyer competition in this price range</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Location Intelligence Section */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Location Intelligence</h2>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Schools */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <School className="w-5 h-5 mr-2 text-blue-500" />
              Schools
            </h3>
            <div className="space-y-3">
              {intelligence.locationIntelligence.schools.map((school, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 text-sm">{school.name}</h4>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">{school.type}</span>
                      <span className="text-sm font-bold text-green-600">{school.rating}</span>
                    </div>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="w-4 h-4 mr-1" />
                    {school.distance}km away
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Transport */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Train className="w-5 h-5 mr-2 text-green-500" />
              Transport
            </h3>
            <div className="space-y-3">
              {intelligence.locationIntelligence.transport.map((transport, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 text-sm">{transport.name}</h4>
                    <span className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">{transport.type}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      {transport.distance}km
                    </div>
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      {transport.walkTime} min walk
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Amenities */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Building className="w-5 h-5 mr-2 text-purple-500" />
              Amenities
            </h3>
            <div className="space-y-3">
              {intelligence.locationIntelligence.amenities.map((amenity, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 text-sm">{amenity.name}</h4>
                    <span className="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full">{amenity.type}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="w-4 h-4 mr-1" />
                    {amenity.distance}km away
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Commute Times */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Car className="w-5 h-5 mr-2 text-gray-600" />
            Commute Times
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">12 min</div>
              <div className="text-sm text-gray-600">CBD</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">8 min</div>
              <div className="text-sm text-gray-600">Valley</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">25 min</div>
              <div className="text-sm text-gray-600">Airport</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">18 min</div>
              <div className="text-sm text-gray-600">South Bank</div>
            </div>
          </div>
        </div>
      </div>

      {/* Investment Metrics Section */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Investment Metrics</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Rental Analysis */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Home className="w-5 h-5 mr-2 text-green-500" />
              Rental Analysis
            </h3>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{intelligence.investmentMetrics.rentalYield}%</div>
                  <div className="text-sm text-gray-600">Rental Yield</div>
                  <div className="text-xs text-green-600 mt-1">Above average</div>
                </div>
                <div className="bg-blue-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{intelligence.investmentMetrics.vacancyRate}%</div>
                  <div className="text-sm text-gray-600">Vacancy Rate</div>
                  <div className="text-xs text-blue-600 mt-1">Very low</div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Estimated Weekly Rent</span>
                  <span className="text-lg font-bold text-gray-900">${intelligence.investmentMetrics.medianRent}</span>
                </div>
                <div className="text-sm text-gray-600">
                  12-month growth: +{intelligence.investmentMetrics.rentGrowth12m}%
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{width: `${intelligence.investmentMetrics.rentGrowth12m * 8}%`}}></div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">Comparable Rentals</h4>
                <div className="space-y-2">
                  {intelligence.investmentMetrics.comparableRentals.map((rental, index) => (
                    <div key={index} className="flex items-center justify-between text-sm border border-gray-200 rounded p-3">
                      <div>
                        <div className="font-medium text-gray-900">{rental.address}</div>
                        <div className="text-gray-600">{rental.bedrooms} bed, {rental.bathrooms} bath</div>
                      </div>
                      <div className="font-bold text-gray-900">${rental.rent}/week</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Cash Flow Analysis */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Calculator className="w-5 h-5 mr-2 text-blue-500" />
              Cash Flow Analysis
            </h3>

            <div className="space-y-4">
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Annual Income</h4>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Weekly Rent × 52</span>
                  <span className="text-xl font-bold text-green-600">
                    {formatCurrency(intelligence.investmentMetrics.cashFlow.annualRent)}
                  </span>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Annual Expenses</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Council Rates</span>
                    <span className="font-medium">{formatCurrency(intelligence.investmentMetrics.cashFlow.expenses.rates)}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Insurance</span>
                    <span className="font-medium">{formatCurrency(intelligence.investmentMetrics.cashFlow.expenses.insurance)}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Maintenance</span>
                    <span className="font-medium">{formatCurrency(intelligence.investmentMetrics.cashFlow.expenses.maintenance)}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Property Management</span>
                    <span className="font-medium">{formatCurrency(intelligence.investmentMetrics.cashFlow.expenses.management)}</span>
                  </div>
                  <div className="border-t pt-2 mt-2">
                    <div className="flex items-center justify-between font-semibold">
                      <span className="text-gray-900">Total Expenses</span>
                      <span className="text-red-600">
                        {formatCurrency(
                          intelligence.investmentMetrics.cashFlow.expenses.rates +
                          intelligence.investmentMetrics.cashFlow.expenses.insurance +
                          intelligence.investmentMetrics.cashFlow.expenses.maintenance +
                          intelligence.investmentMetrics.cashFlow.expenses.management
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="font-semibold text-gray-900">Net Annual Cash Flow</span>
                  <span className="text-xl font-bold text-green-600">
                    {formatCurrency(
                      intelligence.investmentMetrics.cashFlow.annualRent -
                      (intelligence.investmentMetrics.cashFlow.expenses.rates +
                       intelligence.investmentMetrics.cashFlow.expenses.insurance +
                       intelligence.investmentMetrics.cashFlow.expenses.maintenance +
                       intelligence.investmentMetrics.cashFlow.expenses.management)
                    )}
                  </span>
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {formatCurrency(
                    (intelligence.investmentMetrics.cashFlow.annualRent -
                     (intelligence.investmentMetrics.cashFlow.expenses.rates +
                      intelligence.investmentMetrics.cashFlow.expenses.insurance +
                      intelligence.investmentMetrics.cashFlow.expenses.maintenance +
                      intelligence.investmentMetrics.cashFlow.expenses.management)) / 52
                  )} per week
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
