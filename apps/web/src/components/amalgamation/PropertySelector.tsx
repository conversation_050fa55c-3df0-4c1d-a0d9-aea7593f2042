'use client';

import { Building, Filter, MapPin, Plus, Search } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Property {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  zoning: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  developmentScore?: number;
}

interface PropertySelectorProps {
  onPropertySelect: (property: Property) => void;
  selectedPropertyIds: string[];
}

// Mock properties for selection
const mockProperties: Property[] = [
  {
    id: '1',
    address: '45 Collins Street',
    suburb: 'Melbourne',
    state: 'VIC',
    postcode: '3000',
    landSize: 800,
    currentPrice: 2500000,
    zoning: 'Mixed Use',
    coordinates: { lat: -37.8136, lng: 144.9631 },
    developmentScore: 95
  },
  {
    id: '2',
    address: '47 Collins Street',
    suburb: 'Melbourne',
    state: 'VIC',
    postcode: '3000',
    landSize: 850,
    currentPrice: 2700000,
    zoning: 'Mixed Use',
    coordinates: { lat: -37.8136, lng: 144.9632 },
    developmentScore: 92
  },
  {
    id: '3',
    address: '49 Collins Street',
    suburb: 'Melbourne',
    state: 'VIC',
    postcode: '3000',
    landSize: 750,
    currentPrice: 2300000,
    zoning: 'Mixed Use',
    coordinates: { lat: -37.8136, lng: 144.9633 },
    developmentScore: 88
  },
  {
    id: '4',
    address: '78 Queen Street',
    suburb: 'Brisbane',
    state: 'QLD',
    postcode: '4000',
    landSize: 920,
    currentPrice: 1800000,
    zoning: 'High Density Residential',
    coordinates: { lat: -27.4698, lng: 153.0251 },
    developmentScore: 82
  },
  {
    id: '5',
    address: '80 Queen Street',
    suburb: 'Brisbane',
    state: 'QLD',
    postcode: '4000',
    landSize: 680,
    currentPrice: 3000000,
    zoning: 'High Density Residential',
    coordinates: { lat: -27.4698, lng: 153.0252 },
    developmentScore: 85
  },
  {
    id: '6',
    address: '123 George Street',
    suburb: 'Sydney',
    state: 'NSW',
    postcode: '2000',
    landSize: 650,
    currentPrice: 3200000,
    zoning: 'Commercial',
    coordinates: { lat: -33.8688, lng: 151.2093 },
    developmentScore: 90
  },
  {
    id: '7',
    address: '125 George Street',
    suburb: 'Sydney',
    state: 'NSW',
    postcode: '2000',
    landSize: 800,
    currentPrice: 3600000,
    zoning: 'Commercial',
    coordinates: { lat: -33.8688, lng: 151.2094 },
    developmentScore: 87
  },
  {
    id: '8',
    address: '127 George Street',
    suburb: 'Sydney',
    state: 'NSW',
    postcode: '2000',
    landSize: 900,
    currentPrice: 3200000,
    zoning: 'Commercial',
    coordinates: { lat: -33.8688, lng: 151.2095 },
    developmentScore: 89
  }
];

export default function PropertySelector({ onPropertySelect, selectedPropertyIds }: PropertySelectorProps) {
  const [properties, setProperties] = useState<Property[]>([]);
  const [filteredProperties, setFilteredProperties] = useState<Property[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [stateFilter, setStateFilter] = useState<string>('all');
  const [zoningFilter, setZoningFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'price' | 'landSize' | 'score'>('score');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setProperties(mockProperties);
      setFilteredProperties(mockProperties);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    let filtered = properties;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(property =>
        property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.suburb.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // State filter
    if (stateFilter !== 'all') {
      filtered = filtered.filter(property => property.state === stateFilter);
    }

    // Zoning filter
    if (zoningFilter !== 'all') {
      filtered = filtered.filter(property => 
        property.zoning.toLowerCase().includes(zoningFilter.toLowerCase())
      );
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return b.currentPrice - a.currentPrice;
        case 'landSize':
          return b.landSize - a.landSize;
        case 'score':
        default:
          return (b.developmentScore || 0) - (a.developmentScore || 0);
      }
    });

    setFilteredProperties(filtered);
  }, [properties, searchTerm, stateFilter, zoningFilter, sortBy]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getUniqueStates = () => {
    return Array.from(new Set(properties.map(p => p.state)));
  };

  const getUniqueZonings = () => {
    return Array.from(new Set(properties.map(p => p.zoning)));
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading available properties...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Properties</h3>
        
        {/* Search and Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by address or suburb..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={stateFilter}
                onChange={(e) => setStateFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All States</option>
                {getUniqueStates().map(state => (
                  <option key={state} value={state}>{state}</option>
                ))}
              </select>
            </div>
            
            <select
              value={zoningFilter}
              onChange={(e) => setZoningFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Zoning</option>
              {getUniqueZonings().map(zoning => (
                <option key={zoning} value={zoning}>{zoning}</option>
              ))}
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="score">Development Score</option>
              <option value="price">Price</option>
              <option value="landSize">Land Size</option>
            </select>
          </div>
        </div>
      </div>

      {/* Properties List */}
      <div className="p-6">
        {filteredProperties.length === 0 ? (
          <div className="text-center py-8">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No properties found</h4>
            <p className="text-gray-600">Try adjusting your search criteria or filters</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredProperties.map((property) => {
              const isSelected = selectedPropertyIds.includes(property.id);
              
              return (
                <div
                  key={property.id}
                  className={`border rounded-lg p-4 transition-all cursor-pointer ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                  }`}
                  onClick={() => !isSelected && onPropertySelect(property)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">{property.address}</h4>
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-4 w-4 mr-1" />
                        {property.suburb}, {property.state} {property.postcode}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {property.developmentScore && (
                        <div className="text-right">
                          <div className="text-xs text-gray-500">Score</div>
                          <div className="text-sm font-bold text-green-600">{property.developmentScore}</div>
                        </div>
                      )}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isSelected) {
                            onPropertySelect(property);
                          }
                        }}
                        disabled={isSelected}
                        className={`p-2 rounded-full transition-colors ${
                          isSelected
                            ? 'bg-blue-600 text-white cursor-not-allowed'
                            : 'bg-gray-100 text-gray-600 hover:bg-blue-100 hover:text-blue-600'
                        }`}
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-gray-500">Land Size:</span>
                      <div className="font-medium">{property.landSize}m²</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Price:</span>
                      <div className="font-medium">{formatCurrency(property.currentPrice)}</div>
                    </div>
                    <div className="col-span-2">
                      <span className="text-gray-500">Zoning:</span>
                      <div className="font-medium">{property.zoning}</div>
                    </div>
                  </div>

                  {isSelected && (
                    <div className="mt-3 pt-3 border-t border-blue-200">
                      <div className="flex items-center text-sm text-blue-700">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                        Selected for analysis
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {filteredProperties.length > 0 && (
          <div className="mt-6 text-center text-sm text-gray-600">
            Showing {filteredProperties.length} of {properties.length} properties
          </div>
        )}
      </div>
    </div>
  );
}
