'use client';

import { useState } from 'react';
import { Building2, MapPin, Clock, DollarSign, Target, AlertTriangle, CheckCircle, Users } from 'lucide-react';

interface AmalgamationProject {
  id: string;
  name: string;
  propertyCount: number;
  totalLandSize: number;
  totalValue: number;
  estimatedUplift: number;
  roi: number;
  status: 'draft' | 'analyzing' | 'complete';
  createdAt: string;
  lastUpdated: string;
  developmentType: string;
  targetDwellings: number;
  properties: {
    id: string;
    address: string;
    suburb: string;
    state: string;
    postcode: string;
    landSize: number;
    currentPrice: number;
    zoning: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  }[];
}

interface AssemblyStrategyProps {
  project: AmalgamationProject;
}

interface PropertyStrategy {
  id: string;
  address: string;
  priority: 'high' | 'medium' | 'low';
  acquisitionOrder: number;
  negotiationComplexity: 'low' | 'medium' | 'high';
  estimatedTimeframe: string;
  keyConsiderations: string[];
  recommendedApproach: string;
  fallbackOptions: string[];
}

interface AssemblyPhase {
  phase: number;
  name: string;
  duration: string;
  properties: string[];
  keyActivities: string[];
  risks: string[];
  successCriteria: string[];
}

// Generate assembly strategy data
const generateAssemblyStrategy = (project: AmalgamationProject) => {
  const propertyStrategies: PropertyStrategy[] = project.properties.map((property, index) => ({
    id: property.id,
    address: property.address,
    priority: index === 0 ? 'high' : index === 1 ? 'medium' : 'low',
    acquisitionOrder: index + 1,
    negotiationComplexity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
    estimatedTimeframe: `${3 + index * 2}-${6 + index * 2} months`,
    keyConsiderations: [
      'Owner occupier vs investor status',
      'Recent market activity in area',
      'Property condition and maintenance',
      'Existing tenancy arrangements'
    ],
    recommendedApproach: index === 0 
      ? 'Direct approach with premium offer to secure anchor property'
      : 'Conditional offer subject to other acquisitions',
    fallbackOptions: [
      'Alternative property in same block',
      'Phased development approach',
      'Joint venture with current owner'
    ]
  }));

  const assemblyPhases: AssemblyPhase[] = [
    {
      phase: 1,
      name: 'Anchor Property Acquisition',
      duration: '3-6 months',
      properties: [project.properties[0]?.address || ''],
      keyActivities: [
        'Market research and valuation',
        'Initial owner contact and negotiation',
        'Due diligence and contract execution',
        'Settlement and possession'
      ],
      risks: [
        'Owner unwilling to sell',
        'Price expectations too high',
        'Hidden property issues'
      ],
      successCriteria: [
        'Anchor property secured at target price',
        'Clear title obtained',
        'Development potential confirmed'
      ]
    },
    {
      phase: 2,
      name: 'Adjacent Properties',
      duration: '6-12 months',
      properties: project.properties.slice(1, 3).map(p => p.address),
      keyActivities: [
        'Leverage anchor property success',
        'Negotiate conditional contracts',
        'Coordinate settlement timing',
        'Manage holding costs'
      ],
      risks: [
        'Increased market awareness',
        'Competing developers',
        'Extended negotiation periods'
      ],
      successCriteria: [
        'All target properties under contract',
        'Synchronized settlement dates',
        'Development approvals progressing'
      ]
    },
    {
      phase: 3,
      name: 'Final Assembly & Development',
      duration: '3-6 months',
      properties: ['All remaining properties'],
      keyActivities: [
        'Complete final acquisitions',
        'Finalize development approvals',
        'Secure construction financing',
        'Commence development'
      ],
      risks: [
        'Last-minute acquisition issues',
        'Planning approval delays',
        'Construction cost escalation'
      ],
      successCriteria: [
        'Complete site control achieved',
        'All approvals in place',
        'Construction commenced'
      ]
    }
  ];

  return { propertyStrategies, assemblyPhases };
};

export default function AssemblyStrategy({ project }: AssemblyStrategyProps) {
  const [strategyData] = useState(() => generateAssemblyStrategy(project));
  const [selectedProperty, setSelectedProperty] = useState<string | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Strategy Overview */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Target className="h-5 w-5 text-green-600" />
            Assembly Strategy Overview
          </h3>
          <div className="text-right">
            <div className="text-sm text-gray-600">Total Timeline</div>
            <div className="text-2xl font-bold text-blue-600">12-24 months</div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Properties</div>
            <div className="text-xl font-bold text-blue-600">{project.propertyCount}</div>
            <div className="text-sm text-gray-500">To acquire</div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Total Investment</div>
            <div className="text-xl font-bold text-green-600">{formatCurrency(project.totalValue)}</div>
            <div className="text-sm text-gray-500">Acquisition cost</div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Success Rate</div>
            <div className="text-xl font-bold text-orange-600">85%</div>
            <div className="text-sm text-gray-500">Estimated probability</div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Risk Level</div>
            <div className="text-xl font-bold text-yellow-600">Medium</div>
            <div className="text-sm text-gray-500">Overall assessment</div>
          </div>
        </div>
      </div>

      {/* Property Acquisition Strategy */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Building2 className="h-5 w-5 text-blue-500" />
            Property Acquisition Strategy
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {strategyData.propertyStrategies.map((strategy) => {
              const property = project.properties.find(p => p.id === strategy.id);
              if (!property) return null;

              return (
                <div
                  key={strategy.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    selectedProperty === strategy.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedProperty(selectedProperty === strategy.id ? null : strategy.id)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">{strategy.address}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {property.suburb}
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {formatCurrency(property.currentPrice)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {strategy.estimatedTimeframe}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-600">#{strategy.acquisitionOrder}</span>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(strategy.priority)}`}>
                        {strategy.priority} priority
                      </span>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getComplexityColor(strategy.negotiationComplexity)}`}>
                        {strategy.negotiationComplexity} complexity
                      </span>
                    </div>
                  </div>

                  {selectedProperty === strategy.id && (
                    <div className="border-t border-blue-200 pt-4 mt-4 space-y-4">
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">Recommended Approach</h5>
                        <p className="text-sm text-gray-700">{strategy.recommendedApproach}</p>
                      </div>

                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">Key Considerations</h5>
                        <ul className="space-y-1">
                          {strategy.keyConsiderations.map((consideration, index) => (
                            <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                              {consideration}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">Fallback Options</h5>
                        <ul className="space-y-1">
                          {strategy.fallbackOptions.map((option, index) => (
                            <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                              <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                              {option}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Assembly Phases */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Clock className="h-5 w-5 text-purple-500" />
            Assembly Timeline & Phases
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-6">
            {strategyData.assemblyPhases.map((phase, index) => (
              <div key={phase.phase} className="relative">
                {index < strategyData.assemblyPhases.length - 1 && (
                  <div className="absolute left-6 top-12 w-0.5 h-full bg-gray-200"></div>
                )}
                
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-bold">{phase.phase}</span>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{phase.name}</h4>
                      <span className="text-sm font-medium text-blue-600">{phase.duration}</span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">Properties</h5>
                        <ul className="text-sm text-gray-700 space-y-1">
                          {phase.properties.map((property, propIndex) => (
                            <li key={propIndex} className="flex items-center gap-2">
                              <Building2 className="h-4 w-4 text-gray-400" />
                              {property}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">Key Activities</h5>
                        <ul className="text-sm text-gray-700 space-y-1">
                          {phase.keyActivities.map((activity, actIndex) => (
                            <li key={actIndex} className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                              {activity}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h5 className="font-medium text-gray-900 mb-2">Key Risks</h5>
                        <ul className="text-sm text-gray-700 space-y-1">
                          {phase.risks.map((risk, riskIndex) => (
                            <li key={riskIndex} className="flex items-start gap-2">
                              <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                              {risk}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-3">
                      <h5 className="font-medium text-gray-900 mb-2">Success Criteria</h5>
                      <div className="flex flex-wrap gap-2">
                        {phase.successCriteria.map((criteria, critIndex) => (
                          <span key={critIndex} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            {criteria}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Strategic Recommendations */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center gap-2">
          <Users className="h-5 w-5" />
          Strategic Recommendations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
          <div>
            <div className="text-blue-700 font-medium mb-2">Negotiation Strategy</div>
            <ul className="text-blue-800 space-y-1">
              <li>• Engage professional acquisition specialist</li>
              <li>• Maintain confidentiality during early phases</li>
              <li>• Offer premium for anchor property</li>
              <li>• Use conditional contracts for subsequent properties</li>
            </ul>
          </div>
          <div>
            <div className="text-blue-700 font-medium mb-2">Risk Mitigation</div>
            <ul className="text-blue-800 space-y-1">
              <li>• Secure backup properties in same area</li>
              <li>• Maintain flexible development plans</li>
              <li>• Consider joint venture opportunities</li>
              <li>• Plan for extended negotiation periods</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
