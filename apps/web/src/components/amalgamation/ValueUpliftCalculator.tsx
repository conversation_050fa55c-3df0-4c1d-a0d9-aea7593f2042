'use client';

import { Bar<PERSON><PERSON>3, Calculator, DollarSign, RefreshCw, Sliders, TrendingUp } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

interface AmalgamationProject {
  id: string;
  name: string;
  propertyCount: number;
  totalLandSize: number;
  totalValue: number;
  estimatedUplift: number;
  roi: number;
  status: 'draft' | 'analyzing' | 'complete';
  createdAt: string;
  lastUpdated: string;
  developmentType: string;
  targetDwellings: number;
  properties: {
    id: string;
    address: string;
    suburb: string;
    state: string;
    postcode: string;
    landSize: number;
    currentPrice: number;
    zoning: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  }[];
}

interface ValueUpliftCalculatorProps {
  project: AmalgamationProject;
}

interface CalculationParams {
  dwellings: number;
  averageUnitSize: number;
  averageUnitPrice: number;
  constructionCostPerSqm: number;
  softCostsPercentage: number;
  marketingCostsPercentage: number;
  contingencyPercentage: number;
  profitMargin: number;
}

interface UpliftBreakdown {
  landAssemblyPremium: number;
  densityBonus: number;
  designEfficiency: number;
  marketPositioning: number;
  infrastructureSynergies: number;
  total: number;
}

export default function ValueUpliftCalculator({ project }: ValueUpliftCalculatorProps) {
  const [params, setParams] = useState<CalculationParams>({
    dwellings: project.targetDwellings,
    averageUnitSize: 85,
    averageUnitPrice: project.properties[0]?.suburb === 'Melbourne' ? 750000 : 
                     project.properties[0]?.suburb === 'Sydney' ? 900000 : 650000,
    constructionCostPerSqm: 3200,
    softCostsPercentage: 15,
    marketingCostsPercentage: 3,
    contingencyPercentage: 10,
    profitMargin: 20
  });

  const [results, setResults] = useState<any>(null);
  const [upliftBreakdown, setUpliftBreakdown] = useState<UpliftBreakdown | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const calculateValueUplift = useCallback(() => {
    setIsCalculating(true);
    
    // Simulate calculation delay
    setTimeout(() => {
      // Individual development scenario
      const individualDwellings = Math.floor(project.targetDwellings * 0.65);
      const individualRevenue = individualDwellings * params.averageUnitPrice * 0.9; // Lower price for smaller developments
      
      // Amalgamated development scenario
      const totalConstructionArea = params.dwellings * params.averageUnitSize;
      const constructionCost = totalConstructionArea * params.constructionCostPerSqm;
      const softCosts = (project.totalValue + constructionCost) * (params.softCostsPercentage / 100);
      const marketingCosts = constructionCost * (params.marketingCostsPercentage / 100);
      const contingency = constructionCost * (params.contingencyPercentage / 100);
      
      const totalCosts = project.totalValue + constructionCost + softCosts + marketingCosts + contingency;
      const totalRevenue = params.dwellings * params.averageUnitPrice;
      const grossProfit = totalRevenue - totalCosts;
      const netProfit = grossProfit * (1 - params.profitMargin / 100);
      
      // Value uplift breakdown
      const baseValue = project.totalValue;
      const upliftBreakdown: UpliftBreakdown = {
        landAssemblyPremium: baseValue * 0.15, // 15% premium for larger site
        densityBonus: (params.dwellings - individualDwellings) * params.averageUnitPrice * 0.3,
        designEfficiency: totalRevenue * 0.08, // 8% efficiency gain
        marketPositioning: totalRevenue * 0.12, // 12% premium positioning
        infrastructureSynergies: constructionCost * 0.05, // 5% cost savings
        total: 0
      };
      
      upliftBreakdown.total = Object.values(upliftBreakdown).reduce((sum, value) => 
        typeof value === 'number' ? sum + value : sum, 0
      );
      
      const calculationResults = {
        // Individual scenario
        individualDwellings,
        individualRevenue,
        individualProfit: individualRevenue - (project.totalValue + individualDwellings * params.averageUnitSize * params.constructionCostPerSqm * 1.3),
        
        // Amalgamated scenario
        totalCosts,
        totalRevenue,
        grossProfit,
        netProfit,
        roi: (netProfit / totalCosts) * 100,
        
        // Uplift analysis
        valueUplift: upliftBreakdown.total,
        upliftPercentage: (upliftBreakdown.total / baseValue) * 100,
        
        // Per unit metrics
        revenuePerUnit: params.averageUnitPrice,
        costPerUnit: totalCosts / params.dwellings,
        profitPerUnit: netProfit / params.dwellings,
        
        // Efficiency metrics
        landEfficiency: params.dwellings / (project.totalLandSize / 10000), // units per hectare
        costEfficiency: constructionCost / totalConstructionArea, // cost per sqm
        revenueEfficiency: totalRevenue / project.totalLandSize // revenue per sqm of land
      };
      
      setResults(calculationResults);
      setUpliftBreakdown(upliftBreakdown);
      setIsCalculating(false);
    }, 1500);
  }, [params, project]);

  useEffect(() => {
    calculateValueUplift();
  }, [calculateValueUplift]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const resetToDefaults = () => {
    setParams({
      dwellings: project.targetDwellings,
      averageUnitSize: 85,
      averageUnitPrice: project.properties[0]?.suburb === 'Melbourne' ? 750000 : 
                       project.properties[0]?.suburb === 'Sydney' ? 900000 : 650000,
      constructionCostPerSqm: 3200,
      softCostsPercentage: 15,
      marketingCostsPercentage: 3,
      contingencyPercentage: 10,
      profitMargin: 20
    });
  };

  return (
    <div className="space-y-6">
      {/* Calculator Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Calculator className="h-5 w-5 text-blue-600" />
            Value Uplift Calculator
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={resetToDefaults}
              className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Reset
            </button>
            <button
              onClick={calculateValueUplift}
              disabled={isCalculating}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Calculator className="h-4 w-4" />
              {isCalculating ? 'Calculating...' : 'Recalculate'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Development Parameters */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Number of Dwellings
            </label>
            <input
              type="range"
              min="10"
              max="50"
              value={params.dwellings}
              onChange={(e) => setParams({ ...params, dwellings: parseInt(e.target.value) })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>10</span>
              <span className="font-medium text-blue-600">{params.dwellings}</span>
              <span>50</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Average Unit Size (m²)
            </label>
            <input
              type="range"
              min="50"
              max="150"
              value={params.averageUnitSize}
              onChange={(e) => setParams({ ...params, averageUnitSize: parseInt(e.target.value) })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>50m²</span>
              <span className="font-medium text-blue-600">{params.averageUnitSize}m²</span>
              <span>150m²</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Average Unit Price
            </label>
            <input
              type="range"
              min="400000"
              max="1200000"
              step="25000"
              value={params.averageUnitPrice}
              onChange={(e) => setParams({ ...params, averageUnitPrice: parseInt(e.target.value) })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>$400k</span>
              <span className="font-medium text-blue-600">{formatCurrency(params.averageUnitPrice)}</span>
              <span>$1.2M</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Construction Cost (per m²)
            </label>
            <input
              type="range"
              min="2000"
              max="5000"
              step="100"
              value={params.constructionCostPerSqm}
              onChange={(e) => setParams({ ...params, constructionCostPerSqm: parseInt(e.target.value) })}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>$2k</span>
              <span className="font-medium text-blue-600">${params.constructionCostPerSqm}</span>
              <span>$5k</span>
            </div>
          </div>
        </div>

        {/* Additional Parameters */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Soft Costs (%)</label>
            <input
              type="number"
              min="10"
              max="25"
              value={params.softCostsPercentage}
              onChange={(e) => setParams({ ...params, softCostsPercentage: parseFloat(e.target.value) })}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Marketing (%)</label>
            <input
              type="number"
              min="1"
              max="8"
              step="0.5"
              value={params.marketingCostsPercentage}
              onChange={(e) => setParams({ ...params, marketingCostsPercentage: parseFloat(e.target.value) })}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Contingency (%)</label>
            <input
              type="number"
              min="5"
              max="20"
              value={params.contingencyPercentage}
              onChange={(e) => setParams({ ...params, contingencyPercentage: parseFloat(e.target.value) })}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Profit Margin (%)</label>
            <input
              type="number"
              min="10"
              max="35"
              value={params.profitMargin}
              onChange={(e) => setParams({ ...params, profitMargin: parseFloat(e.target.value) })}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
        </div>
      </div>

      {/* Results */}
      {isCalculating ? (
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Calculating Value Uplift</h3>
          <p className="text-gray-600">Analyzing development scenarios and calculating potential returns...</p>
        </div>
      ) : results ? (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-5 w-5 text-green-500" />
                <h4 className="font-medium text-gray-900">Value Uplift</h4>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(results.valueUplift)}
              </div>
              <div className="text-sm text-gray-500">{results.upliftPercentage.toFixed(1)}% increase</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="h-5 w-5 text-blue-500" />
                <h4 className="font-medium text-gray-900">Net Profit</h4>
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(results.netProfit)}
              </div>
              <div className="text-sm text-gray-500">After all costs</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="h-5 w-5 text-purple-500" />
                <h4 className="font-medium text-gray-900">ROI</h4>
              </div>
              <div className="text-2xl font-bold text-purple-600">
                {results.roi.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">Return on investment</div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-2">
                <Sliders className="h-5 w-5 text-orange-500" />
                <h4 className="font-medium text-gray-900">Profit per Unit</h4>
              </div>
              <div className="text-2xl font-bold text-orange-600">
                {formatCurrency(results.profitPerUnit)}
              </div>
              <div className="text-sm text-gray-500">Average per dwelling</div>
            </div>
          </div>

          {/* Value Uplift Breakdown */}
          {upliftBreakdown && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Value Uplift Breakdown</h3>
              <div className="space-y-3">
                {Object.entries(upliftBreakdown).map(([key, value]) => {
                  if (key === 'total') return null;
                  const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                  const percentage = (value / upliftBreakdown.total) * 100;
                  
                  return (
                    <div key={key} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 bg-blue-500 rounded" style={{ 
                          backgroundColor: `hsl(${Object.keys(upliftBreakdown).indexOf(key) * 60}, 70%, 50%)` 
                        }}></div>
                        <span className="text-gray-700">{label}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatCurrency(value)}</div>
                        <div className="text-sm text-gray-500">{percentage.toFixed(1)}%</div>
                      </div>
                    </div>
                  );
                })}
                <div className="border-t pt-3">
                  <div className="flex items-center justify-between font-semibold">
                    <span>Total Value Uplift</span>
                    <span className="text-green-600">{formatCurrency(upliftBreakdown.total)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Comparison Analysis */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Amalgamation vs Individual Development</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Individual Development</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Units:</span>
                    <span className="font-medium">{results.individualDwellings}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Revenue:</span>
                    <span className="font-medium">{formatCurrency(results.individualRevenue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Profit:</span>
                    <span className="font-medium">{formatCurrency(results.individualProfit)}</span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Amalgamated Development</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Units:</span>
                    <span className="font-medium">{params.dwellings}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Revenue:</span>
                    <span className="font-medium">{formatCurrency(results.totalRevenue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Profit:</span>
                    <span className="font-medium text-green-600">{formatCurrency(results.netProfit)}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-4 bg-green-50 rounded-lg">
              <div className="text-green-800 font-medium">Amalgamation Advantage</div>
              <div className="text-green-700 text-sm mt-1">
                +{params.dwellings - results.individualDwellings} additional units • 
                +{formatCurrency(results.netProfit - results.individualProfit)} additional profit • 
                {((results.netProfit / results.individualProfit - 1) * 100).toFixed(1)}% profit increase
              </div>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
}
