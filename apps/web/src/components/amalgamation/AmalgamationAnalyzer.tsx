'use client';

import { useState } from 'react';
import { Building2, TrendingUp, AlertCircle, CheckCircle, Info, Calculator, Target, Zap } from 'lucide-react';

interface AmalgamationProject {
  id: string;
  name: string;
  propertyCount: number;
  totalLandSize: number;
  totalValue: number;
  estimatedUplift: number;
  roi: number;
  status: 'draft' | 'analyzing' | 'complete';
  createdAt: string;
  lastUpdated: string;
  developmentType: string;
  targetDwellings: number;
  properties: {
    id: string;
    address: string;
    suburb: string;
    state: string;
    postcode: string;
    landSize: number;
    currentPrice: number;
    zoning: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  }[];
}

interface AmalgamationAnalyzerProps {
  project: AmalgamationProject;
}

// Generate comprehensive analysis data
const generateAnalysisData = (project: AmalgamationProject) => ({
  feasibilityScore: Math.min(95, 60 + (project.roi * 0.5)),
  
  landAnalysis: {
    totalArea: project.totalLandSize,
    averageSize: project.totalLandSize / project.propertyCount,
    efficiency: 85, // How well properties fit together
    accessibility: 92, // Street access and connectivity
    topography: 'Relatively flat with good development potential',
    soilConditions: 'Standard urban soil, suitable for construction'
  },

  developmentPotential: {
    maxDwellings: project.targetDwellings,
    density: (project.targetDwellings / project.totalLandSize) * 10000, // per hectare
    floorSpaceRatio: 6.5,
    siteCoverage: 75,
    buildingHeight: 45,
    parkingSpaces: project.targetDwellings * 1.2
  },

  financialAnalysis: {
    acquisitionCost: project.totalValue,
    developmentCost: project.targetDwellings * 350000,
    totalInvestment: project.totalValue + (project.targetDwellings * 350000),
    estimatedGDV: project.totalValue + project.estimatedUplift,
    profit: project.estimatedUplift - (project.targetDwellings * 350000 * 0.2),
    roi: project.roi,
    irr: project.roi * 0.75,
    paybackPeriod: 4.2
  },

  advantages: [
    {
      title: 'Consolidated Land Assembly',
      description: 'Larger development site enables better design and economies of scale',
      impact: 'High',
      value: '+$1.2M'
    },
    {
      title: 'Zoning Compatibility',
      description: 'All properties have compatible zoning for intended development',
      impact: 'High',
      value: '+$800k'
    },
    {
      title: 'Infrastructure Synergies',
      description: 'Shared infrastructure reduces per-unit development costs',
      impact: 'Medium',
      value: '+$600k'
    },
    {
      title: 'Market Position',
      description: 'Combined site creates premium development opportunity',
      impact: 'High',
      value: '+$1.0M'
    }
  ],

  challenges: [
    {
      title: 'Acquisition Complexity',
      description: 'Multiple property negotiations increase transaction risk',
      probability: 'Medium',
      impact: 'Medium',
      mitigation: 'Engage experienced acquisition specialist'
    },
    {
      title: 'Planning Approval',
      description: 'Larger development may face increased scrutiny',
      probability: 'Low',
      impact: 'High',
      mitigation: 'Early engagement with planning authorities'
    },
    {
      title: 'Construction Staging',
      description: 'Complex site may require phased construction',
      probability: 'High',
      impact: 'Low',
      mitigation: 'Detailed construction management plan'
    }
  ],

  comparisonWithIndividual: {
    individualDwellings: Math.floor(project.targetDwellings * 0.6),
    individualValue: project.totalValue * 1.15,
    amalgamatedAdvantage: project.estimatedUplift * 0.4,
    efficiencyGain: '35%'
  }
});

export default function AmalgamationAnalyzer({ project }: AmalgamationAnalyzerProps) {
  const [analysisData] = useState(() => generateAnalysisData(project));

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getImpactColor = (impact: string) => {
    switch (impact.toLowerCase()) {
      case 'high':
        return 'text-green-600 bg-green-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Feasibility Overview */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Calculator className="h-5 w-5 text-blue-600" />
            Amalgamation Feasibility Analysis
          </h3>
          <div className="text-right">
            <div className="text-sm text-gray-600">Feasibility Score</div>
            <div className="text-3xl font-bold text-blue-600">{analysisData.feasibilityScore.toFixed(0)}/100</div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Combined Potential</div>
            <div className="text-xl font-bold text-green-600">
              {analysisData.developmentPotential.maxDwellings} Units
            </div>
            <div className="text-sm text-gray-500">vs {analysisData.comparisonWithIndividual.individualDwellings} individual</div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Value Uplift</div>
            <div className="text-xl font-bold text-green-600">
              {formatCurrency(project.estimatedUplift)}
            </div>
            <div className="text-sm text-gray-500">Through amalgamation</div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm text-gray-600">Efficiency Gain</div>
            <div className="text-xl font-bold text-green-600">
              {analysisData.comparisonWithIndividual.efficiencyGain}
            </div>
            <div className="text-sm text-gray-500">vs individual development</div>
          </div>
        </div>
      </div>

      {/* Land Analysis */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Target className="h-5 w-5 text-green-500" />
            Land Assembly Analysis
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Site Characteristics</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Land Area</span>
                  <span className="font-medium">{analysisData.landAnalysis.totalArea.toLocaleString()}m²</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Average Property Size</span>
                  <span className="font-medium">{analysisData.landAnalysis.averageSize.toFixed(0)}m²</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Assembly Efficiency</span>
                  <span className="font-medium text-green-600">{analysisData.landAnalysis.efficiency}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Site Accessibility</span>
                  <span className="font-medium text-green-600">{analysisData.landAnalysis.accessibility}%</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Development Metrics</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Density</span>
                  <span className="font-medium">{analysisData.developmentPotential.density.toFixed(0)} units/ha</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Floor Space Ratio</span>
                  <span className="font-medium">{analysisData.developmentPotential.floorSpaceRatio}:1</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Site Coverage</span>
                  <span className="font-medium">{analysisData.developmentPotential.siteCoverage}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Max Building Height</span>
                  <span className="font-medium">{analysisData.developmentPotential.buildingHeight}m</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Financial Analysis */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-500" />
            Financial Analysis
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Investment Summary</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Acquisition Cost</span>
                  <span className="font-medium">{formatCurrency(analysisData.financialAnalysis.acquisitionCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Development Cost</span>
                  <span className="font-medium">{formatCurrency(analysisData.financialAnalysis.developmentCost)}</span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between font-semibold">
                    <span>Total Investment</span>
                    <span>{formatCurrency(analysisData.financialAnalysis.totalInvestment)}</span>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Returns</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Estimated GDV</span>
                  <span className="font-medium">{formatCurrency(analysisData.financialAnalysis.estimatedGDV)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Net Profit</span>
                  <span className="font-medium text-green-600">{formatCurrency(analysisData.financialAnalysis.profit)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">ROI</span>
                  <span className="font-medium text-green-600">{analysisData.financialAnalysis.roi}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">IRR</span>
                  <span className="font-medium text-blue-600">{analysisData.financialAnalysis.irr.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Advantages */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Zap className="h-5 w-5 text-green-500" />
            Amalgamation Advantages
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {analysisData.advantages.map((advantage, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{advantage.title}</h4>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getImpactColor(advantage.impact)}`}>
                      {advantage.impact} Impact
                    </span>
                    <span className="text-sm font-semibold text-green-600">{advantage.value}</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600">{advantage.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Challenges */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-orange-500" />
            Implementation Challenges
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {analysisData.challenges.map((challenge, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{challenge.title}</h4>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskColor(challenge.probability)}`}>
                      {challenge.probability}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskColor(challenge.impact)}`}>
                      {challenge.impact} Impact
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">{challenge.description}</p>
                <div className="bg-blue-50 p-3 rounded">
                  <div className="text-xs font-medium text-blue-900 mb-1">Mitigation Strategy</div>
                  <div className="text-sm text-blue-800">{challenge.mitigation}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Comparison Summary */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">Amalgamation vs Individual Development</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <div className="text-blue-700 font-medium">Development Scale</div>
            <div className="text-blue-800 mt-1">
              Combined: {analysisData.developmentPotential.maxDwellings} units<br/>
              Individual: {analysisData.comparisonWithIndividual.individualDwellings} units<br/>
              Advantage: +{analysisData.developmentPotential.maxDwellings - analysisData.comparisonWithIndividual.individualDwellings} units
            </div>
          </div>
          <div>
            <div className="text-blue-700 font-medium">Value Creation</div>
            <div className="text-blue-800 mt-1">
              Amalgamation uplift: {formatCurrency(project.estimatedUplift)}<br/>
              Efficiency gain: {analysisData.comparisonWithIndividual.efficiencyGain}<br/>
              ROI improvement: +{(project.roi - 25).toFixed(1)}%
            </div>
          </div>
          <div>
            <div className="text-blue-700 font-medium">Strategic Benefits</div>
            <div className="text-blue-800 mt-1">
              Better site utilization<br/>
              Economies of scale<br/>
              Premium market positioning
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
