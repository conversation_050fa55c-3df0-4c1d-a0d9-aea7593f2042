'use client';

import { useAuth } from '@/lib/auth';
import { AlertCircle, Crown, FileText, Home, Search, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';

interface UsageStats {
  userId: string;
  monthlySearchCount: number;
  yearlySearchCount: number;
  trackedPropertiesCount: number;
  monthlySearchLimit?: number;
  yearlySearchLimit?: number;
  propertyTrackLimit?: number;
  monthlySearchesRemaining?: number;
  yearlySearchesRemaining?: number;
  propertyTrackingSlotsRemaining?: number;
  canSearchThisMonth: boolean;
  canSearchThisYear: boolean;
  canTrackMoreProperties: boolean;
  lastSearchReset: string;
  lastYearlyReset: string;
}

interface Subscription {
  id: string;
  planType: string;
  status: string;
  currentPeriodEnd: string;
  trialEnd?: string;
  canExportReports: boolean;
  canAccessCMA: boolean;
  hasPrioritySupport: boolean;
}

export default function UsageDashboard() {
  const { user } = useAuth();
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchUsageData();
    }
  }, [user]);

  const fetchUsageData = async () => {
    try {
      // Mock data for now - replace with actual API calls
      const mockUsageStats: UsageStats = {
        userId: user?.id || '',
        monthlySearchCount: 7,
        yearlySearchCount: 15,
        trackedPropertiesCount: 2,
        monthlySearchLimit: 10,
        yearlySearchLimit: undefined,
        propertyTrackLimit: 3,
        monthlySearchesRemaining: 3,
        yearlySearchesRemaining: -1,
        propertyTrackingSlotsRemaining: 1,
        canSearchThisMonth: true,
        canSearchThisYear: true,
        canTrackMoreProperties: true,
        lastSearchReset: new Date().toISOString(),
        lastYearlyReset: new Date().toISOString(),
      };

      const mockSubscription: Subscription = {
        id: 'sub_123',
        planType: 'HOME_PLUS',
        status: 'ACTIVE',
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        canExportReports: false,
        canAccessCMA: false,
        hasPrioritySupport: false,
      };

      setUsageStats(mockUsageStats);
      setSubscription(mockSubscription);
    } catch (error) {
      console.error('Failed to fetch usage data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatPlanName = (planType: string) => {
    switch (planType) {
      case 'REVALU_ACCESS': return 'Revalu Access';
      case 'HOME_PLUS': return 'Home+';
      case 'INVESTOR_PLUS': return 'Investor+';
      case 'AGENT_PRO': return 'Agent Pro';
      case 'TEAM_PLAN': return 'Team Plan';
      case 'ENTERPRISE': return 'Enterprise';
      default: return planType;
    }
  };

  const getUsageColor = (used: number, limit?: number) => {
    if (!limit || limit === -1) return 'text-green-600';
    const percentage = (used / limit) * 100;
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressColor = (used: number, limit?: number) => {
    if (!limit || limit === -1) return 'bg-green-500';
    const percentage = (used / limit) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatLimit = (limit?: number) => {
    if (!limit) return 'None';
    if (limit === -1) return 'Unlimited';
    return limit.toString();
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!usageStats || !subscription) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center text-gray-500">
          <AlertCircle className="w-8 h-8 mx-auto mb-2" />
          <p>Unable to load usage information</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Plan Overview */}
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg shadow-sm p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center mb-2">
              <Crown className="w-5 h-5 mr-2" />
              <h2 className="text-xl font-semibold">{formatPlanName(subscription.planType)}</h2>
            </div>
            <p className="text-primary-100">
              Status: {subscription.status}
              {subscription.trialEnd && new Date(subscription.trialEnd) > new Date() && (
                <span className="ml-2 bg-white/20 px-2 py-1 rounded text-xs">
                  Trial until {new Date(subscription.trialEnd).toLocaleDateString()}
                </span>
              )}
            </p>
            <p className="text-primary-100 text-sm">
              Next billing: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
            </p>
          </div>
          <div className="text-right">
            <button className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              Manage Plan
            </button>
          </div>
        </div>
      </div>

      {/* Usage Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Monthly Searches */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Search className="w-5 h-5 text-blue-500 mr-2" />
              <h3 className="font-medium text-gray-900">Monthly Searches</h3>
            </div>
            <span className={`text-sm font-medium ${getUsageColor(usageStats.monthlySearchCount, usageStats.monthlySearchLimit)}`}>
              {usageStats.monthlySearchCount}/{formatLimit(usageStats.monthlySearchLimit)}
            </span>
          </div>
          
          {usageStats.monthlySearchLimit && usageStats.monthlySearchLimit !== -1 && (
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                className={`h-2 rounded-full ${getProgressColor(usageStats.monthlySearchCount, usageStats.monthlySearchLimit)}`}
                style={{
                  width: `${Math.min((usageStats.monthlySearchCount / usageStats.monthlySearchLimit) * 100, 100)}%`
                }}
              ></div>
            </div>
          )}
          
          <p className="text-sm text-gray-600">
            {usageStats.monthlySearchesRemaining === -1 
              ? 'Unlimited searches remaining'
              : `${usageStats.monthlySearchesRemaining} searches remaining this month`
            }
          </p>
        </div>

        {/* Tracked Properties */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Home className="w-5 h-5 text-green-500 mr-2" />
              <h3 className="font-medium text-gray-900">Tracked Properties</h3>
            </div>
            <span className={`text-sm font-medium ${getUsageColor(usageStats.trackedPropertiesCount, usageStats.propertyTrackLimit)}`}>
              {usageStats.trackedPropertiesCount}/{formatLimit(usageStats.propertyTrackLimit)}
            </span>
          </div>
          
          {usageStats.propertyTrackLimit && usageStats.propertyTrackLimit !== -1 && (
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                className={`h-2 rounded-full ${getProgressColor(usageStats.trackedPropertiesCount, usageStats.propertyTrackLimit)}`}
                style={{
                  width: `${Math.min((usageStats.trackedPropertiesCount / usageStats.propertyTrackLimit) * 100, 100)}%`
                }}
              ></div>
            </div>
          )}
          
          <p className="text-sm text-gray-600">
            {usageStats.propertyTrackingSlotsRemaining === -1 
              ? 'Unlimited tracking available'
              : `${usageStats.propertyTrackingSlotsRemaining} slots remaining`
            }
          </p>
        </div>

        {/* Yearly Searches (for Freemium) */}
        {usageStats.yearlySearchLimit && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <TrendingUp className="w-5 h-5 text-purple-500 mr-2" />
                <h3 className="font-medium text-gray-900">Yearly Searches</h3>
              </div>
              <span className={`text-sm font-medium ${getUsageColor(usageStats.yearlySearchCount, usageStats.yearlySearchLimit)}`}>
                {usageStats.yearlySearchCount}/{formatLimit(usageStats.yearlySearchLimit)}
              </span>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                className={`h-2 rounded-full ${getProgressColor(usageStats.yearlySearchCount, usageStats.yearlySearchLimit)}`}
                style={{
                  width: `${Math.min((usageStats.yearlySearchCount / usageStats.yearlySearchLimit) * 100, 100)}%`
                }}
              ></div>
            </div>
            
            <p className="text-sm text-gray-600">
              {usageStats.yearlySearchesRemaining === -1
                ? 'Unlimited searches remaining'
                : `${Math.max(0, usageStats.yearlySearchesRemaining || 0)} searches remaining this year`
              }
            </p>
          </div>
        )}
      </div>

      {/* Feature Access */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="font-medium text-gray-900 mb-4">Plan Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <FileText className="w-4 h-4 mr-2 text-gray-400" />
            <span className="text-sm text-gray-600">Export Reports</span>
            <span className={`ml-auto text-xs px-2 py-1 rounded ${
              subscription.canExportReports 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {subscription.canExportReports ? 'Available' : 'Upgrade Required'}
            </span>
          </div>
          <div className="flex items-center">
            <TrendingUp className="w-4 h-4 mr-2 text-gray-400" />
            <span className="text-sm text-gray-600">CMA Tools</span>
            <span className={`ml-auto text-xs px-2 py-1 rounded ${
              subscription.canAccessCMA 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {subscription.canAccessCMA ? 'Available' : 'Upgrade Required'}
            </span>
          </div>
          <div className="flex items-center">
            <Crown className="w-4 h-4 mr-2 text-gray-400" />
            <span className="text-sm text-gray-600">Priority Support</span>
            <span className={`ml-auto text-xs px-2 py-1 rounded ${
              subscription.hasPrioritySupport 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {subscription.hasPrioritySupport ? 'Available' : 'Upgrade Required'}
            </span>
          </div>
        </div>
      </div>

      {/* Upgrade CTA */}
      {subscription.planType === 'REVALU_ACCESS' && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-900 mb-1">Ready for more?</h3>
              <p className="text-sm text-gray-600">
                Upgrade to Home+ for unlimited monthly searches and property tracking.
              </p>
            </div>
            <button className="btn-primary">
              Upgrade Now
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
