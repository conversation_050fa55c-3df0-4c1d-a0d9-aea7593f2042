'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, MapPin, Plus, X, Building2, Calculator } from 'lucide-react';
import PropertySelector from '@/components/amalgamation/PropertySelector';

interface SelectedProperty {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  zoning: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export default function CreateAmalgamationPage() {
  const router = useRouter();
  const [step, setStep] = useState<'properties' | 'details' | 'analysis'>('properties');
  const [selectedProperties, setSelectedProperties] = useState<SelectedProperty[]>([]);
  const [projectName, setProjectName] = useState('');
  const [developmentType, setDevelopmentType] = useState<'residential' | 'commercial' | 'mixed'>('mixed');
  const [targetDwellings, setTargetDwellings] = useState(20);
  const [isCreating, setIsCreating] = useState(false);

  const handlePropertySelect = (property: SelectedProperty) => {
    if (!selectedProperties.find(p => p.id === property.id)) {
      setSelectedProperties([...selectedProperties, property]);
    }
  };

  const handlePropertyRemove = (propertyId: string) => {
    setSelectedProperties(selectedProperties.filter(p => p.id !== propertyId));
  };

  const handleNext = () => {
    if (step === 'properties' && selectedProperties.length >= 2) {
      setStep('details');
    } else if (step === 'details' && projectName.trim()) {
      setStep('analysis');
      createAmalgamation();
    }
  };

  const createAmalgamation = async () => {
    setIsCreating(true);
    
    // Simulate API call
    setTimeout(() => {
      // Generate a mock ID and redirect to the analysis page
      const mockId = Math.random().toString(36).substr(2, 9);
      router.push(`/amalgamation/${mockId}`);
    }, 2000);
  };

  const canProceed = () => {
    if (step === 'properties') return selectedProperties.length >= 2;
    if (step === 'details') return projectName.trim().length > 0;
    return false;
  };

  const totalLandSize = selectedProperties.reduce((sum, p) => sum + p.landSize, 0);
  const totalValue = selectedProperties.reduce((sum, p) => sum + p.currentPrice, 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push('/amalgamation')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <Building2 className="h-7 w-7 text-blue-600" />
                Create Amalgamation Analysis
              </h1>
              <p className="text-gray-600 mt-1">
                Select properties and analyze combined development potential
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
              step === 'properties' ? 'bg-blue-600 text-white' : 
              step === 'details' || step === 'analysis' ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'
            }`}>
              1
            </div>
            <div className="text-sm font-medium text-gray-700">Select Properties</div>
            <div className={`w-12 h-0.5 ${
              step === 'details' || step === 'analysis' ? 'bg-green-600' : 'bg-gray-300'
            }`}></div>
            <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
              step === 'details' ? 'bg-blue-600 text-white' : 
              step === 'analysis' ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'
            }`}>
              2
            </div>
            <div className="text-sm font-medium text-gray-700">Project Details</div>
            <div className={`w-12 h-0.5 ${
              step === 'analysis' ? 'bg-green-600' : 'bg-gray-300'
            }`}></div>
            <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
              step === 'analysis' ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
            }`}>
              3
            </div>
            <div className="text-sm font-medium text-gray-700">Analysis</div>
          </div>
        </div>

        {/* Step Content */}
        {step === 'properties' && (
          <div className="space-y-6">
            {/* Selected Properties Summary */}
            {selectedProperties.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Selected Properties ({selectedProperties.length})
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>Total Land: {totalLandSize.toLocaleString()}m²</span>
                    <span>Total Value: {formatCurrency(totalValue)}</span>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {selectedProperties.map((property) => (
                    <div key={property.id} className="border border-gray-200 rounded-lg p-4 relative">
                      <button
                        onClick={() => handlePropertyRemove(property.id)}
                        className="absolute top-2 right-2 p-1 text-gray-400 hover:text-red-600 transition-colors"
                      >
                        <X className="h-4 w-4" />
                      </button>
                      <div className="pr-6">
                        <h4 className="font-medium text-gray-900 mb-1">{property.address}</h4>
                        <div className="text-sm text-gray-600 mb-2">
                          <MapPin className="h-4 w-4 inline mr-1" />
                          {property.suburb}, {property.state} {property.postcode}
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-gray-500">Land:</span>
                            <span className="font-medium ml-1">{property.landSize}m²</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Value:</span>
                            <span className="font-medium ml-1">{formatCurrency(property.currentPrice)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Property Selector */}
            <PropertySelector
              onPropertySelect={handlePropertySelect}
              selectedPropertyIds={selectedProperties.map(p => p.id)}
            />

            {/* Requirements Notice */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Building2 className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">Amalgamation Requirements</h4>
                  <p className="text-blue-800 text-sm mt-1">
                    Select at least 2 properties for amalgamation analysis. Properties should ideally be:
                  </p>
                  <ul className="text-blue-800 text-sm mt-2 space-y-1">
                    <li>• Adjacent or in close proximity</li>
                    <li>• In the same or compatible zoning</li>
                    <li>• Suitable for combined development</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 'details' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Project Details</h3>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Project Name *
                  </label>
                  <input
                    type="text"
                    value={projectName}
                    onChange={(e) => setProjectName(e.target.value)}
                    placeholder="Enter a name for this amalgamation project"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Development Type
                  </label>
                  <select
                    value={developmentType}
                    onChange={(e) => setDevelopmentType(e.target.value as any)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="residential">Residential</option>
                    <option value="commercial">Commercial</option>
                    <option value="mixed">Mixed Use</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Dwellings/Units
                  </label>
                  <input
                    type="number"
                    value={targetDwellings}
                    onChange={(e) => setTargetDwellings(parseInt(e.target.value) || 0)}
                    min="1"
                    max="200"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Estimated number of residential units or commercial spaces
                  </p>
                </div>

                {/* Project Summary */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Project Summary</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Properties:</span>
                      <span className="font-medium ml-2">{selectedProperties.length}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Total Land:</span>
                      <span className="font-medium ml-2">{totalLandSize.toLocaleString()}m²</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Total Value:</span>
                      <span className="font-medium ml-2">{formatCurrency(totalValue)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Development Type:</span>
                      <span className="font-medium ml-2 capitalize">{developmentType}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 'analysis' && (
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white rounded-lg shadow-sm p-12">
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-6"></div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Creating Analysis</h3>
                  <p className="text-gray-600 mb-2">Processing property data and calculating development potential...</p>
                  <div className="text-sm text-gray-500">This may take a few moments</div>
                </>
              ) : (
                <>
                  <Calculator className="h-16 w-16 text-blue-600 mx-auto mb-6" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Ready to Analyze</h3>
                  <p className="text-gray-600 mb-6">
                    We'll analyze the selected properties and calculate the combined development potential,
                    value uplift, and assembly strategy.
                  </p>
                  <button
                    onClick={createAmalgamation}
                    className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Start Analysis
                  </button>
                </>
              )}
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex items-center justify-between mt-8">
          <button
            onClick={() => {
              if (step === 'details') setStep('properties');
              else if (step === 'analysis') setStep('details');
            }}
            className={`px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors ${
              step === 'properties' ? 'invisible' : ''
            }`}
          >
            Back
          </button>

          <div className="text-sm text-gray-600">
            {step === 'properties' && selectedProperties.length > 0 && (
              <span>{selectedProperties.length} properties selected</span>
            )}
            {step === 'properties' && selectedProperties.length < 2 && (
              <span className="text-red-600">Select at least 2 properties to continue</span>
            )}
          </div>

          <button
            onClick={handleNext}
            disabled={!canProceed() || isCreating}
            className={`px-6 py-2 rounded-lg transition-colors ${
              canProceed() && !isCreating
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            } ${step === 'analysis' ? 'invisible' : ''}`}
          >
            {step === 'properties' ? 'Continue' : 'Create Analysis'}
          </button>
        </div>
      </div>
    </div>
  );
}
