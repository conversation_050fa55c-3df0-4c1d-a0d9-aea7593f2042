'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Building2, TrendingUp, Calculator, MapPin, Calendar, Edit, Trash2 } from 'lucide-react';

interface AmalgamationProject {
  id: string;
  name: string;
  propertyCount: number;
  totalLandSize: number;
  totalValue: number;
  estimatedUplift: number;
  roi: number;
  status: 'draft' | 'analyzing' | 'complete';
  createdAt: string;
  lastUpdated: string;
  properties: {
    id: string;
    address: string;
    suburb: string;
    landSize: number;
    currentPrice: number;
  }[];
}

// Mock amalgamation projects
const mockAmalgamationProjects: AmalgamationProject[] = [
  {
    id: '1',
    name: 'Collins Street Mixed Development',
    propertyCount: 3,
    totalLandSize: 2400,
    totalValue: 7500000,
    estimatedUplift: 4200000,
    roi: 56,
    status: 'complete',
    createdAt: '2023-10-15',
    lastUpdated: '2023-11-20',
    properties: [
      { id: '1', address: '45 Collins Street', suburb: 'Melbourne', landSize: 800, currentPrice: 2500000 },
      { id: '2', address: '47 Collins Street', suburb: 'Melbourne', landSize: 850, currentPrice: 2700000 },
      { id: '3', address: '49 Collins Street', suburb: 'Melbourne', landSize: 750, currentPrice: 2300000 }
    ]
  },
  {
    id: '2',
    name: 'Queen Street Residential Hub',
    propertyCount: 2,
    totalLandSize: 1600,
    totalValue: 4800000,
    estimatedUplift: 2400000,
    roi: 50,
    status: 'analyzing',
    createdAt: '2023-11-01',
    lastUpdated: '2023-11-25',
    properties: [
      { id: '4', address: '78 Queen Street', suburb: 'Brisbane', landSize: 920, currentPrice: 1800000 },
      { id: '5', address: '80 Queen Street', suburb: 'Brisbane', landSize: 680, currentPrice: 3000000 }
    ]
  },
  {
    id: '3',
    name: 'George Street Tower Concept',
    propertyCount: 4,
    totalLandSize: 3200,
    totalValue: 12800000,
    estimatedUplift: 8500000,
    roi: 66,
    status: 'draft',
    createdAt: '2023-11-20',
    lastUpdated: '2023-11-20',
    properties: [
      { id: '6', address: '123 George Street', suburb: 'Sydney', landSize: 650, currentPrice: 3200000 },
      { id: '7', address: '125 George Street', suburb: 'Sydney', landSize: 800, currentPrice: 3600000 },
      { id: '8', address: '127 George Street', suburb: 'Sydney', landSize: 900, currentPrice: 3200000 },
      { id: '9', address: '129 George Street', suburb: 'Sydney', landSize: 850, currentPrice: 2800000 }
    ]
  }
];

export default function AmalgamationPage() {
  const router = useRouter();
  const [projects, setProjects] = useState<AmalgamationProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'updated' | 'roi' | 'value'>('updated');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setProjects(mockAmalgamationProjects);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredAndSortedProjects = projects
    .filter(project => filterStatus === 'all' || project.status === filterStatus)
    .sort((a, b) => {
      switch (sortBy) {
        case 'roi':
          return b.roi - a.roi;
        case 'value':
          return b.totalValue - a.totalValue;
        case 'updated':
        default:
          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
      }
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete':
        return 'bg-green-100 text-green-800';
      case 'analyzing':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete':
        return '✓';
      case 'analyzing':
        return '⚡';
      case 'draft':
        return '📝';
      default:
        return '📄';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading amalgamation projects...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Building2 className="h-8 w-8 text-blue-600" />
                Amalgamation Analysis
              </h1>
              <p className="text-gray-600 mt-2">
                Analyze multi-property development opportunities and land assembly strategies
              </p>
            </div>
            <button
              onClick={() => router.push('/amalgamation/create')}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <Plus className="h-5 w-5" />
              New Analysis
            </button>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-2 mb-2">
              <Building2 className="h-5 w-5 text-blue-500" />
              <h3 className="font-medium text-gray-900">Total Projects</h3>
            </div>
            <div className="text-2xl font-bold text-blue-600">{projects.length}</div>
            <div className="text-sm text-gray-500">Active analyses</div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <h3 className="font-medium text-gray-900">Total Value</h3>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(projects.reduce((sum, p) => sum + p.totalValue, 0))}
            </div>
            <div className="text-sm text-gray-500">Combined property value</div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-2 mb-2">
              <Calculator className="h-5 w-5 text-purple-500" />
              <h3 className="font-medium text-gray-900">Potential Uplift</h3>
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {formatCurrency(projects.reduce((sum, p) => sum + p.estimatedUplift, 0))}
            </div>
            <div className="text-sm text-gray-500">Estimated value increase</div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-2 mb-2">
              <MapPin className="h-5 w-5 text-orange-500" />
              <h3 className="font-medium text-gray-900">Average ROI</h3>
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {Math.round(projects.reduce((sum, p) => sum + p.roi, 0) / projects.length)}%
            </div>
            <div className="text-sm text-gray-500">Return on investment</div>
          </div>
        </div>

        {/* Filters and Sort */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="analyzing">Analyzing</option>
                  <option value="complete">Complete</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="updated">Last Updated</option>
                  <option value="roi">ROI</option>
                  <option value="value">Total Value</option>
                </select>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              {filteredAndSortedProjects.length} of {projects.length} projects
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredAndSortedProjects.map((project) => (
            <div
              key={project.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{project.name}</h3>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>{project.propertyCount} properties</span>
                      <span>{project.totalLandSize.toLocaleString()}m²</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(project.status)}`}>
                      {getStatusIcon(project.status)} {project.status}
                    </span>
                  </div>
                </div>

                {/* Metrics */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-gray-500">Total Value</div>
                    <div className="font-semibold">{formatCurrency(project.totalValue)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Est. Uplift</div>
                    <div className="font-semibold text-green-600">{formatCurrency(project.estimatedUplift)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">ROI</div>
                    <div className="font-semibold text-blue-600">{project.roi}%</div>
                  </div>
                </div>

                {/* Properties List */}
                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-2">Properties</div>
                  <div className="space-y-1">
                    {project.properties.slice(0, 3).map((property) => (
                      <div key={property.id} className="text-sm text-gray-600 flex items-center justify-between">
                        <span>{property.address}, {property.suburb}</span>
                        <span className="font-medium">{formatCurrency(property.currentPrice)}</span>
                      </div>
                    ))}
                    {project.properties.length > 3 && (
                      <div className="text-sm text-gray-500">
                        +{project.properties.length - 3} more properties
                      </div>
                    )}
                  </div>
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-500">
                    <Calendar className="h-4 w-4 inline mr-1" />
                    Updated {formatDate(project.lastUpdated)}
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => router.push(`/amalgamation/${project.id}`)}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      View Analysis
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-1 text-gray-400 hover:text-red-600 transition-colors">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredAndSortedProjects.length === 0 && (
          <div className="text-center py-12">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
            <p className="text-gray-600 mb-4">Create your first amalgamation analysis to get started</p>
            <button
              onClick={() => router.push('/amalgamation/create')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create New Analysis
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
