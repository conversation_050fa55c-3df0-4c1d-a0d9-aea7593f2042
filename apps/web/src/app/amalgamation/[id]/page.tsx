'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Building2, Calculator, TrendingUp, MapPin, AlertTriangle } from 'lucide-react';
import AmalgamationAnalyzer from '@/components/amalgamation/AmalgamationAnalyzer';
import AssemblyStrategy from '@/components/amalgamation/AssemblyStrategy';
import ValueUpliftCalculator from '@/components/amalgamation/ValueUpliftCalculator';

interface AmalgamationProject {
  id: string;
  name: string;
  propertyCount: number;
  totalLandSize: number;
  totalValue: number;
  estimatedUplift: number;
  roi: number;
  status: 'draft' | 'analyzing' | 'complete';
  createdAt: string;
  lastUpdated: string;
  developmentType: string;
  targetDwellings: number;
  properties: {
    id: string;
    address: string;
    suburb: string;
    state: string;
    postcode: string;
    landSize: number;
    currentPrice: number;
    zoning: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  }[];
}

// Mock project data
const mockProjectData: { [key: string]: AmalgamationProject } = {
  '1': {
    id: '1',
    name: 'Collins Street Mixed Development',
    propertyCount: 3,
    totalLandSize: 2400,
    totalValue: 7500000,
    estimatedUplift: 4200000,
    roi: 56,
    status: 'complete',
    createdAt: '2023-10-15',
    lastUpdated: '2023-11-20',
    developmentType: 'Mixed Use',
    targetDwellings: 24,
    properties: [
      {
        id: '1',
        address: '45 Collins Street',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000',
        landSize: 800,
        currentPrice: 2500000,
        zoning: 'Mixed Use',
        coordinates: { lat: -37.8136, lng: 144.9631 }
      },
      {
        id: '2',
        address: '47 Collins Street',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000',
        landSize: 850,
        currentPrice: 2700000,
        zoning: 'Mixed Use',
        coordinates: { lat: -37.8136, lng: 144.9632 }
      },
      {
        id: '3',
        address: '49 Collins Street',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000',
        landSize: 750,
        currentPrice: 2300000,
        zoning: 'Mixed Use',
        coordinates: { lat: -37.8136, lng: 144.9633 }
      }
    ]
  }
};

export default function AmalgamationAnalysisPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<AmalgamationProject | null>(null);
  const [activeTab, setActiveTab] = useState<'analyzer' | 'strategy' | 'calculator'>('analyzer');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const projectData = mockProjectData[projectId];
      if (projectData) {
        setProject(projectData);
      }
      setLoading(false);
    }, 1000);
  }, [projectId]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete':
        return 'bg-green-100 text-green-800';
      case 'analyzing':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading amalgamation analysis...</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Project Not Found</h2>
          <p className="text-gray-600 mb-4">The requested amalgamation project could not be found.</p>
          <button
            onClick={() => router.push('/amalgamation')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Amalgamation
          </button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'analyzer', label: 'Analysis', icon: Calculator },
    { id: 'strategy', label: 'Assembly Strategy', icon: Building2 },
    { id: 'calculator', label: 'Value Calculator', icon: TrendingUp }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/amalgamation')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>
                <div className="flex items-center gap-4 text-gray-600 mt-1">
                  <span>{project.propertyCount} properties</span>
                  <span>{project.totalLandSize.toLocaleString()}m²</span>
                  <span>Updated {formatDate(project.lastUpdated)}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-center">
                <div className="text-sm text-gray-500">Estimated Uplift</div>
                <div className="text-2xl font-bold text-green-600">{formatCurrency(project.estimatedUplift)}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-500">ROI</div>
                <div className="text-2xl font-bold text-blue-600">{project.roi}%</div>
              </div>
              <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(project.status)}`}>
                {project.status}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Project Summary */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            <div>
              <div className="text-sm text-gray-500">Total Value</div>
              <div className="text-lg font-semibold">{formatCurrency(project.totalValue)}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Land Size</div>
              <div className="text-lg font-semibold">{project.totalLandSize.toLocaleString()}m²</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Development Type</div>
              <div className="text-lg font-semibold">{project.developmentType}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Target Units</div>
              <div className="text-lg font-semibold">{project.targetDwellings}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Est. Uplift</div>
              <div className="text-lg font-semibold text-green-600">{formatCurrency(project.estimatedUplift)}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">ROI</div>
              <div className="text-lg font-semibold text-blue-600">{project.roi}%</div>
            </div>
          </div>
        </div>

        {/* Properties List */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Properties in Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {project.properties.map((property) => (
              <div key={property.id} className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">{property.address}</h4>
                <div className="text-sm text-gray-600 mb-3">
                  <MapPin className="h-4 w-4 inline mr-1" />
                  {property.suburb}, {property.state} {property.postcode}
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-500">Land:</span>
                    <span className="font-medium ml-1">{property.landSize}m²</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Value:</span>
                    <span className="font-medium ml-1">{formatCurrency(property.currentPrice)}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-500">Zoning:</span>
                    <span className="font-medium ml-1">{property.zoning}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'analyzer' && <AmalgamationAnalyzer project={project} />}
            {activeTab === 'strategy' && <AssemblyStrategy project={project} />}
            {activeTab === 'calculator' && <ValueUpliftCalculator project={project} />}
          </div>
        </div>
      </div>
    </div>
  );
}
