'use client';

import { useAuth, withAuth } from '@/lib/auth';
import { formatCurrency, formatDate } from '@/lib/utils';
import {
    BarChart3,
    Calendar,
    Download,
    Eye,
    FileText,
    Filter,
    Home,
    MapPin,
    Plus,
    Search,
    Share2,
    TrendingUp
} from 'lucide-react';
import { useState } from 'react';

// Mock data for reports until API is ready
const mockReports = [
  {
    id: '1',
    title: 'Portfolio Performance Report',
    type: 'portfolio',
    description: 'Comprehensive analysis of your tracked properties performance',
    createdAt: new Date('2024-06-15'),
    status: 'completed',
    propertyCount: 5,
    totalValue: 4250000,
    valueChange: 125000,
    changePercent: 3.2,
  },
  {
    id: '2',
    title: 'Market Analysis - Toorak VIC',
    type: 'market',
    description: 'Detailed market trends and insights for Toorak suburb',
    createdAt: new Date('2024-06-12'),
    status: 'completed',
    suburbName: 'Toorak, VIC',
    medianPrice: 2850000,
    priceGrowth: 8.5,
  },
  {
    id: '3',
    title: 'Development Feasibility Study',
    type: 'development',
    description: 'Analysis of development potential for 123 Collins Street',
    createdAt: new Date('2024-06-10'),
    status: 'completed',
    propertyAddress: '123 Collins Street, Melbourne VIC',
    developmentScore: 85,
    estimatedUplift: 450000,
  },
  {
    id: '4',
    title: 'Investment Comparison Report',
    type: 'comparison',
    description: 'Side-by-side comparison of 3 investment properties',
    createdAt: new Date('2024-06-08'),
    status: 'processing',
    propertyCount: 3,
    estimatedCompletion: new Date('2024-06-18'),
  },
];

const reportTypes = [
  { value: 'all', label: 'All Reports' },
  { value: 'portfolio', label: 'Portfolio Reports' },
  { value: 'market', label: 'Market Analysis' },
  { value: 'development', label: 'Development Studies' },
  { value: 'comparison', label: 'Property Comparisons' },
];

function ReportsPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  // Filter and sort reports
  const filteredReports = mockReports
    .filter(report => {
      const matchesSearch = report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           report.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesType = selectedType === 'all' || report.type === selectedType;
      return matchesSearch && matchesType;
    })
    .sort((a, b) => {
      if (sortBy === 'newest') return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      if (sortBy === 'oldest') return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      if (sortBy === 'title') return a.title.localeCompare(b.title);
      return 0;
    });

  const getReportIcon = (type: string) => {
    switch (type) {
      case 'portfolio': return <BarChart3 className="w-5 h-5" />;
      case 'market': return <TrendingUp className="w-5 h-5" />;
      case 'development': return <Home className="w-5 h-5" />;
      case 'comparison': return <Eye className="w-5 h-5" />;
      default: return <FileText className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Reports</h1>
              <p className="text-gray-600 mt-2">
                Generate and manage comprehensive property intelligence reports
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="btn-outline">
                <Plus className="w-4 h-4 mr-2" />
                Generate Report
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search reports..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            {/* Type Filter */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {reportTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="title">Title A-Z</option>
            </select>

            {/* Clear Filters */}
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedType('all');
                setSortBy('newest');
              }}
              className="btn-outline"
            >
              <Filter className="w-4 h-4 mr-2" />
              Clear Filters
            </button>
          </div>
        </div>

        {/* Reports Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold text-gray-900">{mockReports.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockReports.filter(r => r.status === 'completed').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Calendar className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Processing</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockReports.filter(r => r.status === 'processing').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockReports.filter(r => 
                    new Date(r.createdAt).getMonth() === new Date().getMonth()
                  ).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Reports List */}
        {filteredReports.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || selectedType !== 'all' ? 'No reports found' : 'No reports yet'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery || selectedType !== 'all'
                ? 'Try adjusting your search or filters to find what you\'re looking for.'
                : 'Generate your first property intelligence report to get started.'
              }
            </p>
            <button className="btn-primary">
              <Plus className="w-4 h-4 mr-2" />
              Generate Report
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredReports.map((report) => (
              <div key={report.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      {/* Report Icon */}
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        {getReportIcon(report.type)}
                      </div>

                      {/* Report Details */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{report.title}</h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(report.status)}`}>
                            {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                          </span>
                        </div>

                        <p className="text-gray-600 mb-3">{report.description}</p>

                        {/* Report Metadata */}
                        <div className="flex items-center space-x-6 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {formatDate(report.createdAt)}
                          </div>

                          {report.propertyCount && (
                            <div className="flex items-center">
                              <Home className="w-4 h-4 mr-1" />
                              {report.propertyCount} {report.propertyCount === 1 ? 'property' : 'properties'}
                            </div>
                          )}

                          {report.suburbName && (
                            <div className="flex items-center">
                              <MapPin className="w-4 h-4 mr-1" />
                              {report.suburbName}
                            </div>
                          )}

                          {report.propertyAddress && (
                            <div className="flex items-center">
                              <MapPin className="w-4 h-4 mr-1" />
                              {report.propertyAddress}
                            </div>
                          )}
                        </div>

                        {/* Report Metrics */}
                        {report.status === 'completed' && (
                          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                            {report.totalValue && (
                              <div>
                                <p className="text-xs text-gray-500">Total Value</p>
                                <p className="font-semibold text-gray-900">{formatCurrency(report.totalValue)}</p>
                              </div>
                            )}

                            {report.valueChange && (
                              <div>
                                <p className="text-xs text-gray-500">Value Change</p>
                                <p className={`font-semibold ${report.valueChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {report.valueChange >= 0 ? '+' : ''}{formatCurrency(Math.abs(report.valueChange))}
                                </p>
                              </div>
                            )}

                            {report.medianPrice && (
                              <div>
                                <p className="text-xs text-gray-500">Median Price</p>
                                <p className="font-semibold text-gray-900">{formatCurrency(report.medianPrice)}</p>
                              </div>
                            )}

                            {report.priceGrowth && (
                              <div>
                                <p className="text-xs text-gray-500">Price Growth</p>
                                <p className="font-semibold text-green-600">+{report.priceGrowth}%</p>
                              </div>
                            )}

                            {report.developmentScore && (
                              <div>
                                <p className="text-xs text-gray-500">Development Score</p>
                                <p className="font-semibold text-gray-900">{report.developmentScore}/100</p>
                              </div>
                            )}

                            {report.estimatedUplift && (
                              <div>
                                <p className="text-xs text-gray-500">Estimated Uplift</p>
                                <p className="font-semibold text-green-600">{formatCurrency(report.estimatedUplift)}</p>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Processing Status */}
                        {report.status === 'processing' && report.estimatedCompletion && (
                          <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                            <p className="text-sm text-yellow-800">
                              <Calendar className="w-4 h-4 inline mr-1" />
                              Estimated completion: {formatDate(report.estimatedCompletion)}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      {report.status === 'completed' && (
                        <>
                          <button className="btn-outline btn-sm">
                            <Eye className="w-4 h-4 mr-1" />
                            View
                          </button>
                          <button className="btn-outline btn-sm">
                            <Download className="w-4 h-4 mr-1" />
                            Download
                          </button>
                          <button className="btn-outline btn-sm">
                            <Share2 className="w-4 h-4 mr-1" />
                            Share
                          </button>
                        </>
                      )}

                      {report.status === 'processing' && (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                          <span className="text-sm text-gray-600">Processing...</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default withAuth(ReportsPage);
