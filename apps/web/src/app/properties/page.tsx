'use client';

import { ComparisonBar } from '@/components/ui/comparison-bar';
import { EmptyState } from '@/components/ui/empty-state';
import { LoadingCard } from '@/components/ui/loading';
import { useComparison } from '@/contexts/ComparisonContext';
import { apiClient } from '@/lib/api';
import { formatCurrency, formatPropertyFeatures, formatPropertyType } from '@/lib/utils';
import { PropertySearchParams, PropertyType } from '@/types/api';
import { useQuery } from '@tanstack/react-query';
import { Activity, BarChart3, Filter, MapPin, Minus, Plus, Search, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Suspense, useState } from 'react';

// Add new filter types for Growth Potential and Development Score
interface ExtendedPropertySearchParams extends PropertySearchParams {
  growthPotential?: 'low' | 'medium' | 'high';
  developmentScore?: 'low' | 'medium' | 'high';
}

// Helper function to generate mock analytics data
const generateMockAnalytics = (property: any) => {
  const baseValue = property.currentPrice || property.lastSalePrice || 1000000;
  const growthPotential = Math.floor(Math.random() * 40) + 60; // 60-100
  const developmentScore = Math.floor(Math.random() * 30) + 70; // 70-100
  const marketPosition = Math.floor(Math.random() * 50) + 50; // 50-100
  const riskLevel = ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)];

  return {
    estimatedValue: baseValue,
    growthPotential,
    developmentScore,
    marketPosition,
    riskLevel,
    yearlyGrowth: (Math.random() * 10 + 2).toFixed(1), // 2-12%
    daysOnMarket: Math.floor(Math.random() * 60) + 20, // 20-80 days
  };
};

function PropertiesPageContent() {
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [filters, setFilters] = useState<ExtendedPropertySearchParams>({
    page: 1,
    limit: 12,
    search: searchParams.get('search') || undefined,
  });

  const { addProperty, removeProperty, isPropertySelected, canAddMore } = useComparison();

  const { data: propertiesData, isLoading, error } = useQuery({
    queryKey: ['properties', filters],
    queryFn: () => apiClient.getProperties(filters),
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters(prev => ({ ...prev, search: searchQuery || undefined, page: 1 }));
  };

  const handleFilterChange = (key: keyof ExtendedPropertySearchParams, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Something went wrong</h2>
          <p className="text-gray-600">Failed to load properties. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ComparisonBar />
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Property Valuations</h1>
            <div className="flex items-center space-x-4">
              <button className="btn-outline">
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </button>
              <button className="btn-outline">
                <MapPin className="w-4 h-4 mr-2" />
                Map View
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <form onSubmit={handleSearch} className="mb-6">
            <div className="relative max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Enter property address to get valuation"
                className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary"
              >
                Analyse
              </button>
            </div>
          </form>

          {/* Quick Filters */}
          <div className="flex flex-wrap gap-4">
            <select
              value={filters.propertyType?.[0] || ''}
              onChange={(e) => handleFilterChange('propertyType', e.target.value ? [e.target.value as PropertyType] : undefined)}
              className="input w-auto"
            >
              <option value="">All Types</option>
              {Object.values(PropertyType).map(type => (
                <option key={type} value={type}>{formatPropertyType(type)}</option>
              ))}
            </select>

            <select
              value={filters.growthPotential || ''}
              onChange={(e) => handleFilterChange('growthPotential', e.target.value || undefined)}
              className="input w-auto"
            >
              <option value="">Growth Potential</option>
              <option value="high">High Growth</option>
              <option value="medium">Medium Growth</option>
              <option value="low">Low Growth</option>
            </select>

            <select
              value={filters.developmentScore || ''}
              onChange={(e) => handleFilterChange('developmentScore', e.target.value || undefined)}
              className="input w-auto"
            >
              <option value="">Development Score</option>
              <option value="high">High Development</option>
              <option value="medium">Medium Development</option>
              <option value="low">Low Development</option>
            </select>

            <input
              type="number"
              placeholder="Min Price"
              value={filters.minPrice || ''}
              onChange={(e) => handleFilterChange('minPrice', e.target.value ? Number(e.target.value) : undefined)}
              className="input w-32"
            />

            <input
              type="number"
              placeholder="Max Price"
              value={filters.maxPrice || ''}
              onChange={(e) => handleFilterChange('maxPrice', e.target.value ? Number(e.target.value) : undefined)}
              className="input w-32"
            />

            <select
              value={filters.minBedrooms || ''}
              onChange={(e) => handleFilterChange('minBedrooms', e.target.value ? Number(e.target.value) : undefined)}
              className="input w-auto"
            >
              <option value="">Any Beds</option>
              {[1, 2, 3, 4, 5].map(num => (
                <option key={num} value={num}>{num}+ Beds</option>
              ))}
            </select>
          </div>
        </div>

        {/* Results */}
        <div className="mb-6">
          {propertiesData && (
            <p className="text-gray-600">
              Showing {propertiesData.data.length} of {propertiesData.meta.total} properties
            </p>
          )}
        </div>

        {/* Properties Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
        ) : propertiesData?.data.length === 0 ? (
          <EmptyState
            icon={MapPin}
            title="No properties found"
            description="We couldn't find any properties matching your search criteria. Try adjusting your filters or search terms."
            action={{
              label: "Clear Filters",
              onClick: () => {
                setFilters({ page: 1, limit: 12 });
                setSearchQuery('');
              }
            }}
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {propertiesData?.data.map((property) => {
              const analytics = generateMockAnalytics(property);
              const isSelected = isPropertySelected(property.id);

              const handleComparisonToggle = (e: React.MouseEvent) => {
                e.preventDefault();
                e.stopPropagation();
                if (isSelected) {
                  removeProperty(property.id);
                } else if (canAddMore) {
                  addProperty(property);
                }
              };

              return (
                <div key={property.id} className="relative">
                  {/* Comparison Checkbox */}
                  <div className="absolute top-3 left-3 z-20">
                    <button
                      onClick={handleComparisonToggle}
                      disabled={!isSelected && !canAddMore}
                      className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all ${
                        isSelected
                          ? 'bg-primary-600 border-primary-600 text-white'
                          : canAddMore
                          ? 'bg-white border-gray-300 hover:border-primary-500 text-gray-600'
                          : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                      }`}
                      title={isSelected ? 'Remove from comparison' : canAddMore ? 'Add to comparison' : 'Maximum 3 properties'}
                    >
                      {isSelected ? (
                        <Minus className="w-4 h-4" />
                      ) : (
                        <Plus className="w-4 h-4" />
                      )}
                    </button>
                  </div>

                  <Link href={`/properties/${property.id}`}>
                    <div className="card hover:shadow-lg transition-all duration-300 cursor-pointer group relative">
                    <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-lg">
                      {property.images?.[0] ? (
                        <img
                          src={property.images[0]}
                          alt={property.address}
                          className="w-full h-48 object-cover rounded-t-lg"
                        />
                      ) : (
                        <div className="w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center">
                          <MapPin className="w-12 h-12 text-gray-400" />
                        </div>
                      )}
                    </div>

                    <div className="card-content">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {property.address}
                        </h3>
                        <div className="text-right">
                          <span className="text-lg font-bold text-primary-600">
                            {formatCurrency(analytics.estimatedValue)}
                          </span>
                          <p className="text-xs text-gray-500">Est. Value</p>
                        </div>
                      </div>

                      <p className="text-gray-600 mb-3">
                        {formatPropertyFeatures(property.bedrooms, property.bathrooms, property.carSpaces)}
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="badge badge-secondary">
                          {formatPropertyType(property.propertyType)}
                        </span>
                        <button className="btn-ghost btn-sm">
                          <BarChart3 className="w-4 h-4 mr-1" />
                          Quick Analysis
                        </button>
                      </div>
                    </div>

                    {/* Quick Analysis Hover Card */}
                    <div className="absolute top-0 left-0 w-full h-full bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-10 p-4">
                      <div className="h-full flex flex-col">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-semibold text-gray-900">Quick Analysis</h4>
                          <Activity className="w-5 h-5 text-primary-600" />
                        </div>

                        <div className="space-y-3 flex-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Growth Potential</span>
                            <div className="flex items-center">
                              <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div
                                  className="bg-green-500 h-2 rounded-full"
                                  style={{ width: `${analytics.growthPotential}%` }}
                                ></div>
                              </div>
                              <span className="text-sm font-medium">{analytics.growthPotential}%</span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Development Score</span>
                            <div className="flex items-center">
                              <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div
                                  className="bg-blue-500 h-2 rounded-full"
                                  style={{ width: `${analytics.developmentScore}%` }}
                                ></div>
                              </div>
                              <span className="text-sm font-medium">{analytics.developmentScore}%</span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Market Position</span>
                            <span className="text-sm font-medium">{analytics.marketPosition}th percentile</span>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Risk Level</span>
                            <span className={`text-sm font-medium ${
                              analytics.riskLevel === 'Low' ? 'text-green-600' :
                              analytics.riskLevel === 'Medium' ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {analytics.riskLevel}
                            </span>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Yearly Growth</span>
                            <span className="text-sm font-medium text-green-600">+{analytics.yearlyGrowth}%</span>
                          </div>
                        </div>

                        <div className="mt-4 pt-3 border-t border-gray-200">
                          <div className="flex items-center justify-center">
                            <TrendingUp className="w-4 h-4 mr-2 text-primary-600" />
                            <span className="text-sm font-medium text-primary-600">View Full Analysis</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {propertiesData && propertiesData.meta.totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(propertiesData.meta.page - 1)}
                disabled={!propertiesData.meta.hasPreviousPage}
                className="btn-outline disabled:opacity-50"
              >
                Previous
              </button>

              {Array.from({ length: Math.min(5, propertiesData.meta.totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`btn ${page === propertiesData.meta.page ? 'btn-primary' : 'btn-outline'}`}
                  >
                    {page}
                  </button>
                );
              })}

              <button
                onClick={() => handlePageChange(propertiesData.meta.page + 1)}
                disabled={!propertiesData.meta.hasNextPage}
                className="btn-outline disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function PropertiesPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading properties...</p>
        </div>
      </div>
    }>
      <PropertiesPageContent />
    </Suspense>
  );
}
