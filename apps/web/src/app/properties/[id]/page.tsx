'use client';

import { PropertyIntelligenceDashboard } from '@/components/property/PropertyIntelligenceDashboard';
import { Loading } from '@/components/ui/loading';
import { apiClient } from '@/lib/api';
import { useAuth } from '@/lib/auth';
import { formatCurrency, formatDate, formatPropertyType } from '@/lib/utils';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ArrowLeft,
  BarChart3,
  Bath,
  Bed,
  Bell,
  Calendar,
  Car,
  ChevronLeft,
  ChevronRight,
  Eye,
  FileText,
  MapPin,
  School,
  Share2,
  Target,
  Train,
  TrendingUp
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'react-hot-toast';

export default function PropertyDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const queryClient = useQueryClient();
  const propertyId = params.id as string;

  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Fetch property details
  const { data: property, isLoading: propertyLoading, error: propertyError } = useQuery({
    queryKey: ['property', propertyId],
    queryFn: () => apiClient.getProperty(propertyId),
  });

  // Fetch valuation history
  const { data: valuationHistory, isLoading: valuationLoading } = useQuery({
    queryKey: ['valuation-history', propertyId],
    queryFn: () => apiClient.getValuationHistory(propertyId),
    enabled: !!propertyId,
  });

  // Fetch nearby amenities
  const { data: amenities, isLoading: amenitiesLoading } = useQuery({
    queryKey: ['amenities', propertyId],
    queryFn: () => apiClient.getNearbyAmenities(propertyId),
    enabled: !!propertyId,
  });

  // Track/untrack property mutation
  const trackPropertyMutation = useMutation({
    mutationFn: (propertyId: string) => apiClient.trackProperty(propertyId),
    onSuccess: () => {
      toast.success('Property is now being tracked!');
      queryClient.invalidateQueries({ queryKey: ['tracked-properties'] });
    },
    onError: () => {
      toast.error('Failed to track property');
    },
  });

  const untrackPropertyMutation = useMutation({
    mutationFn: (propertyId: string) => apiClient.untrackProperty(propertyId),
    onSuccess: () => {
      toast.success('Property removed from tracking');
      queryClient.invalidateQueries({ queryKey: ['tracked-properties'] });
    },
    onError: () => {
      toast.error('Failed to untrack property');
    },
  });

  const handleTrackProperty = () => {
    if (!isAuthenticated) {
      toast.error('Please sign in to track properties');
      router.push('/auth/login');
      return;
    }
    trackPropertyMutation.mutate(propertyId);
  };

  const handleUntrackProperty = () => {
    untrackPropertyMutation.mutate(propertyId);
  };

  const handleGenerateReport = () => {
    if (!isAuthenticated) {
      toast.error('Please sign in to generate reports');
      router.push('/auth/login');
      return;
    }
    // TODO: Implement report generation
    toast.success('Generating property report...');
  };

  const handleSetAlert = () => {
    if (!isAuthenticated) {
      toast.error('Please sign in to set alerts');
      router.push('/auth/login');
      return;
    }
    // TODO: Implement alert setting
    toast.success('Property alert set!');
  };

  const handleAnalyzeDevelopment = () => {
    if (!isAuthenticated) {
      toast.error('Please sign in to analyze development potential');
      router.push('/auth/login');
      return;
    }
    // TODO: Implement development analysis
    toast.success('Analyzing development potential...');
  };

  const nextImage = () => {
    if (property?.images && property.images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === property.images!.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (property?.images && property.images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? property.images!.length - 1 : prev - 1
      );
    }
  };

  if (propertyLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loading size="lg" />
      </div>
    );
  }

  if (propertyError || !property) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Property not found</h2>
          <p className="text-gray-600 mb-4">The property you're looking for doesn't exist.</p>
          <button
            onClick={() => router.push('/properties')}
            className="btn-primary"
          >
            Back to Properties
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back
            </button>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleTrackProperty}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Target className="w-5 h-5" />
                <span>Track</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Share2 className="w-5 h-5" />
                <span>Share</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image Gallery */}
            <div className="card">
              <div className="relative">
                {property.images && property.images.length > 0 ? (
                  <>
                    <img
                      src={property.images[currentImageIndex]}
                      alt={`${property.address} - Image ${currentImageIndex + 1}`}
                      className="w-full h-96 object-cover rounded-t-lg"
                    />
                    {property.images.length > 1 && (
                      <>
                        <button
                          onClick={prevImage}
                          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                        >
                          <ChevronLeft className="w-5 h-5" />
                        </button>
                        <button
                          onClick={nextImage}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                        >
                          <ChevronRight className="w-5 h-5" />
                        </button>
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                          {currentImageIndex + 1} / {property.images.length}
                        </div>
                      </>
                    )}
                  </>
                ) : (
                  <div className="w-full h-96 bg-gray-200 rounded-t-lg flex items-center justify-center">
                    <MapPin className="w-16 h-16 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Image Thumbnails */}
              {property.images && property.images.length > 1 && (
                <div className="p-4 border-t">
                  <div className="flex space-x-2 overflow-x-auto">
                    {property.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                          index === currentImageIndex ? 'border-primary-500' : 'border-gray-200'
                        }`}
                      >
                        <img
                          src={image}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Property Details */}
            <div className="card">
              <div className="card-header">
                <div className="flex items-start justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                      {property.address}
                    </h1>
                    <p className="text-lg text-gray-600">
                      {property.suburb?.name}, {property.suburb?.state} {property.suburb?.postcode}
                    </p>
                  </div>
                  {property.currentPrice && (
                    <div className="text-right">
                      <p className="text-3xl font-bold text-primary-600">
                        {formatCurrency(property.currentPrice)}
                      </p>
                      <p className="text-sm text-gray-600">Estimated Value</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="card-content">
                {/* Property Features */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  {property.bedrooms && (
                    <div className="flex items-center space-x-2">
                      <Bed className="w-5 h-5 text-gray-400" />
                      <span>{property.bedrooms} Bed{property.bedrooms > 1 ? 's' : ''}</span>
                    </div>
                  )}
                  {property.bathrooms && (
                    <div className="flex items-center space-x-2">
                      <Bath className="w-5 h-5 text-gray-400" />
                      <span>{property.bathrooms} Bath{property.bathrooms > 1 ? 's' : ''}</span>
                    </div>
                  )}
                  {property.carSpaces && (
                    <div className="flex items-center space-x-2">
                      <Car className="w-5 h-5 text-gray-400" />
                      <span>{property.carSpaces} Car{property.carSpaces > 1 ? 's' : ''}</span>
                    </div>
                  )}
                  {property.yearBuilt && (
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-5 h-5 text-gray-400" />
                      <span>Built {property.yearBuilt}</span>
                    </div>
                  )}
                </div>

                {/* Property Type and Status */}
                <div className="flex items-center space-x-4 mb-6">
                  <span className="badge badge-secondary">
                    {formatPropertyType(property.propertyType)}
                  </span>
                  <span className="badge badge-outline">
                    {formatPropertyType(property.status)}
                  </span>
                </div>

                {/* Description */}
                {property.description && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                    <p className="text-gray-600 leading-relaxed">{property.description}</p>
                  </div>
                )}

                {/* Features */}
                {property.features && property.features.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Features</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {property.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                          <span className="text-gray-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Property Specifications */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Specifications</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {property.landSize && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Land Size:</span>
                        <span className="font-medium">{property.landSize} m²</span>
                      </div>
                    )}
                    {property.buildingSize && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Building Size:</span>
                        <span className="font-medium">{property.buildingSize} m²</span>
                      </div>
                    )}
                    {property.lastSalePrice && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Last Sale Price:</span>
                        <span className="font-medium">{formatCurrency(property.lastSalePrice)}</span>
                      </div>
                    )}
                    {property.lastSaleDate && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Last Sale Date:</span>
                        <span className="font-medium">{formatDate(property.lastSaleDate)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Property Intelligence Dashboard */}
            <PropertyIntelligenceDashboard property={property} />

            {/* Valuation History */}
            {valuationHistory && (
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Valuation History</h3>
                  <p className="card-description">Property valuation trends over time</p>
                </div>
                <div className="card-content">
                  {valuationLoading ? (
                    <div className="flex justify-center py-8">
                      <Loading size="md" />
                    </div>
                  ) : valuationHistory.valuations && valuationHistory.valuations.length > 0 ? (
                    <div className="space-y-4">
                      {valuationHistory.valuations.slice(0, 5).map((valuation: any, index: number) => (
                        <div key={valuation.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">
                              {formatCurrency(valuation.estimatedValue)}
                            </p>
                            <p className="text-sm text-gray-600">
                              {formatDate(valuation.valuationDate)} • {valuation.method}
                            </p>
                          </div>
                          {valuation.confidence && (
                            <div className="text-right">
                              <p className="text-sm font-medium text-gray-900">
                                {(valuation.confidence * 100).toFixed(0)}%
                              </p>
                              <p className="text-xs text-gray-600">Confidence</p>
                            </div>
                          )}
                        </div>
                      ))}
                      <button className="btn-outline w-full">
                        View Full History
                      </button>
                    </div>
                  ) : (
                    <p className="text-gray-600 text-center py-8">No valuation history available</p>
                  )}
                </div>
              </div>
            )}

            {/* Nearby Amenities */}
            {amenities && (
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Nearby Amenities</h3>
                  <p className="card-description">Schools, transport, and local facilities</p>
                </div>
                <div className="card-content">
                  {amenitiesLoading ? (
                    <div className="flex justify-center py-8">
                      <Loading size="md" />
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* Schools */}
                      {amenities.schools && amenities.schools.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                            <School className="w-4 h-4 mr-2" />
                            Schools
                          </h4>
                          <div className="space-y-2">
                            {amenities.schools.slice(0, 3).map((school: any) => (
                              <div key={school.id} className="flex items-center justify-between">
                                <div>
                                  <p className="font-medium text-gray-900">{school.name}</p>
                                  <p className="text-sm text-gray-600">{school.type}</p>
                                </div>
                                <div className="text-right">
                                  <p className="text-sm font-medium">{school.distance?.toFixed(1)} km</p>
                                  {school.rating && (
                                    <p className="text-xs text-gray-600">★ {school.rating}/10</p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Transport */}
                      {amenities.transport && amenities.transport.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                            <Train className="w-4 h-4 mr-2" />
                            Transport
                          </h4>
                          <div className="space-y-2">
                            {amenities.transport.slice(0, 3).map((stop: any) => (
                              <div key={stop.id} className="flex items-center justify-between">
                                <div>
                                  <p className="font-medium text-gray-900">{stop.name}</p>
                                  <p className="text-sm text-gray-600">{stop.type}</p>
                                </div>
                                <div className="text-right">
                                  <p className="text-sm font-medium">{stop.distance?.toFixed(1)} km</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Distance to CBD */}
                      {amenities.distanceToCBD && (
                        <div className="pt-4 border-t border-gray-200">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-900">Distance to CBD</span>
                            <span className="text-primary-600 font-medium">
                              {amenities.distanceToCBD.toFixed(1)} km
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Property Intelligence Actions */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Property Intelligence</h3>
                <p className="card-description">Comprehensive property analysis and insights</p>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  <button
                    onClick={handleTrackProperty}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    <Eye className="w-5 h-5" />
                    <span>Track Property</span>
                  </button>

                  <button
                    onClick={handleGenerateReport}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FileText className="w-5 h-5" />
                    <span>Generate Report</span>
                  </button>

                  <button
                    onClick={handleSetAlert}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Bell className="w-5 h-5" />
                    <span>Set Alert</span>
                  </button>

                  <button
                    onClick={handleAnalyzeDevelopment}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <BarChart3 className="w-5 h-5" />
                    <span>Analyze Development</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Market Analysis */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Market Analysis</h3>
                <p className="card-description">Property market insights and trends</p>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  <button className="btn-outline w-full">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Get Valuation
                  </button>
                  <button className="btn-outline w-full">
                    <MapPin className="w-4 h-4 mr-2" />
                    View on Map
                  </button>
                  <button className="btn-outline w-full">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Market Trends
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
