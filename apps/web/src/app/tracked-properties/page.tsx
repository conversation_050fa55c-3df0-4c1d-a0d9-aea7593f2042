'use client';

import { withAuth } from '@/lib/auth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

/**
 * Redirect page for legacy /tracked-properties route
 * This route now redirects to /portfolio for consistency
 */
function TrackedPropertiesRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the new portfolio page
    router.replace('/portfolio');
  }, [router]);

  // Show a simple loading state while redirecting
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to Portfolio...</p>
      </div>
    </div>
  );
}

export default withAuth(TrackedPropertiesRedirect);
