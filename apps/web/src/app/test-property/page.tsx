'use client';

import { PropertyIntelligenceDashboard } from '@/components/property/PropertyIntelligenceDashboard';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function TestPropertyPage() {
  const router = useRouter();

  // Mock property data for testing
  const mockProperty = {
    id: 'test-property-1',
    address: '123 Brunswick Street, New Farm QLD 4005',
    propertyType: 'HOUSE',
    bedrooms: 3,
    bathrooms: 2,
    carSpaces: 2,
    landSize: 405,
    buildingSize: 180,
    yearBuilt: 1995,
    lastSalePrice: 1150000,
    lastSaleDate: '2023-03-15',
    currentPrice: 1275000,
    images: [
      'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop'
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back
            </button>
            <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              🧪 Test Property Dashboard
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Property Intelligence Dashboard Test</h1>
          <p className="text-gray-600">
            This is a test page showcasing the completed Property Intelligence Dashboard with all sections including:
            real-time valuation, intelligence scores, AI predictions, opportunities, risk assessment, 
            interactive maps, market context, location intelligence, and investment metrics.
          </p>
        </div>

        {/* Property Intelligence Dashboard */}
        <PropertyIntelligenceDashboard property={mockProperty} />
      </div>
    </div>
  );
}
