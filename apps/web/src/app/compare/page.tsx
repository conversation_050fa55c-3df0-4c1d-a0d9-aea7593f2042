'use client';

import { Loading } from '@/components/ui/loading';
import { apiClient } from '@/lib/api';
import { formatCurrency, formatPropertyType } from '@/lib/utils';
import { Property } from '@/types/api';
import { useQuery } from '@tanstack/react-query';
import {
    <PERSON>ertTriangle,
    ArrowLeft,
    Award,
    BarChart3,
    Bath,
    Bed,
    Building,
    Car,
    CheckCircle,
    Crown,
    DollarSign,
    MapPin,
    TrendingUp,
    XCircle
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

// Mock analytics function (same as in properties page)
const generateMockAnalytics = (property: Property) => {
  const baseValue = property.currentPrice || property.lastSalePrice || 1000000;
  const growthPotential = Math.floor(Math.random() * 40) + 60; // 60-100
  const developmentScore = Math.floor(Math.random() * 30) + 70; // 70-100
  const marketPosition = Math.floor(Math.random() * 50) + 50; // 50-100
  const riskLevel = ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)];
  const investmentScore = Math.floor(Math.random() * 30) + 70; // 70-100
  const rentalYield = (Math.random() * 4 + 2).toFixed(1); // 2-6%
  const capitalGrowth = (Math.random() * 8 + 3).toFixed(1); // 3-11%

  return {
    estimatedValue: baseValue,
    growthPotential,
    developmentScore,
    marketPosition,
    riskLevel,
    investmentScore,
    rentalYield,
    capitalGrowth,
    yearlyGrowth: (Math.random() * 10 + 2).toFixed(1), // 2-12%
    daysOnMarket: Math.floor(Math.random() * 60) + 20, // 20-80 days
  };
};

// Investment recommendation logic
const getBestInvestment = (properties: Property[]) => {
  const propertiesWithAnalytics = properties.map(property => ({
    property,
    analytics: generateMockAnalytics(property)
  }));

  // Calculate overall score based on multiple factors
  const scoredProperties = propertiesWithAnalytics.map(({ property, analytics }) => {
    const score = (
      analytics.investmentScore * 0.3 +
      analytics.growthPotential * 0.25 +
      analytics.developmentScore * 0.2 +
      (100 - analytics.marketPosition) * 0.15 + // Lower market position is better (more affordable)
      (analytics.riskLevel === 'Low' ? 90 : analytics.riskLevel === 'Medium' ? 70 : 50) * 0.1
    );

    return { property, analytics, score };
  });

  // Sort by score and return the best one
  const best = scoredProperties.sort((a, b) => b.score - a.score)[0];
  
  // Generate reasoning
  const reasons = [];
  if (best.analytics.investmentScore >= 80) reasons.push('Excellent investment fundamentals');
  if (best.analytics.growthPotential >= 80) reasons.push('High growth potential');
  if (best.analytics.developmentScore >= 80) reasons.push('Strong development opportunities');
  if (best.analytics.riskLevel === 'Low') reasons.push('Low risk profile');
  if (parseFloat(best.analytics.rentalYield) >= 5) reasons.push('Strong rental yield');

  return {
    ...best,
    reasons: reasons.length > 0 ? reasons : ['Balanced investment profile', 'Good market position']
  };
};

export default function ComparePage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [properties, setProperties] = useState<Property[]>([]);
  const [bestInvestment, setBestInvestment] = useState<any>(null);

  const propertyIds = searchParams.get('properties')?.split(',') || [];

  // Fetch properties
  const { data: fetchedProperties, isLoading, error } = useQuery({
    queryKey: ['compare-properties', propertyIds],
    queryFn: async () => {
      const promises = propertyIds.map(id => apiClient.getProperty(id));
      return Promise.all(promises);
    },
    enabled: propertyIds.length > 0,
  });

  useEffect(() => {
    if (fetchedProperties) {
      setProperties(fetchedProperties);
      setBestInvestment(getBestInvestment(fetchedProperties));
    }
  }, [fetchedProperties]);

  if (propertyIds.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">No properties to compare</h2>
          <p className="text-gray-600 mb-4">Please select properties from the properties page to compare.</p>
          <button
            onClick={() => router.push('/properties')}
            className="btn-primary"
          >
            Browse Properties
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loading size="lg" />
      </div>
    );
  }

  if (error || !properties.length) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Error loading properties</h2>
          <p className="text-gray-600 mb-4">There was an error loading the properties for comparison.</p>
          <button
            onClick={() => router.push('/properties')}
            className="btn-primary"
          >
            Back to Properties
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back
              </button>
              <h1 className="text-2xl font-bold text-gray-900">
                Property Comparison ({properties.length} properties)
              </h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Best Investment Recommendation */}
        {bestInvestment && (
          <div className="mb-8 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-green-900 mb-2">
                  🏆 Best Investment Opportunity
                </h3>
                <p className="text-green-800 font-medium mb-2">
                  {bestInvestment.property.address}
                </p>
                <p className="text-green-700 mb-3">
                  Investment Score: <span className="font-bold">{bestInvestment.analytics.investmentScore}/100</span>
                </p>
                <div className="space-y-1">
                  <p className="text-sm text-green-700 font-medium">Why this property:</p>
                  <ul className="text-sm text-green-600 space-y-1">
                    {bestInvestment.reasons.map((reason: string, index: number) => (
                      <li key={index} className="flex items-center">
                        <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                        {reason}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Comparison Table */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-900 w-48">
                    Property Details
                  </th>
                  {properties.map((property, index) => (
                    <th key={property.id} className="px-6 py-4 text-center text-sm font-medium text-gray-900 min-w-80">
                      <div className="space-y-2">
                        {bestInvestment?.property.id === property.id && (
                          <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <Crown className="w-3 h-3 mr-1" />
                            Best Investment
                          </div>
                        )}
                        <div className="font-semibold text-gray-900">{property.address}</div>
                        <div className="text-xs text-gray-600">
                          {property.suburb?.name}, {property.suburb?.state} {property.suburb?.postcode}
                        </div>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {/* Property Image */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">Property Image</td>
                  {properties.map((property) => (
                    <td key={property.id} className="px-6 py-4 text-center">
                      <div className="w-full h-32 bg-gray-200 rounded-lg overflow-hidden">
                        {property.images?.[0] ? (
                          <img
                            src={property.images[0]}
                            alt={property.address}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <MapPin className="w-8 h-8 text-gray-400" />
                          </div>
                        )}
                      </div>
                    </td>
                  ))}
                </tr>

                {/* Estimated Value */}
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">Estimated Value</td>
                  {properties.map((property) => {
                    const analytics = generateMockAnalytics(property);
                    return (
                      <td key={property.id} className="px-6 py-4 text-center">
                        <div className="text-lg font-bold text-primary-600">
                          {formatCurrency(analytics.estimatedValue)}
                        </div>
                      </td>
                    );
                  })}
                </tr>

                {/* Property Type */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">Property Type</td>
                  {properties.map((property) => (
                    <td key={property.id} className="px-6 py-4 text-center">
                      <span className="badge badge-secondary">
                        {formatPropertyType(property.propertyType)}
                      </span>
                    </td>
                  ))}
                </tr>

                {/* Bedrooms/Bathrooms/Car Spaces */}
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">Configuration</td>
                  {properties.map((property) => (
                    <td key={property.id} className="px-6 py-4 text-center">
                      <div className="space-y-1">
                        {property.bedrooms && (
                          <div className="flex items-center justify-center space-x-1">
                            <Bed className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">{property.bedrooms} bed{property.bedrooms > 1 ? 's' : ''}</span>
                          </div>
                        )}
                        {property.bathrooms && (
                          <div className="flex items-center justify-center space-x-1">
                            <Bath className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">{property.bathrooms} bath{property.bathrooms > 1 ? 's' : ''}</span>
                          </div>
                        )}
                        {property.carSpaces && (
                          <div className="flex items-center justify-center space-x-1">
                            <Car className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">{property.carSpaces} car{property.carSpaces > 1 ? 's' : ''}</span>
                          </div>
                        )}
                      </div>
                    </td>
                  ))}
                </tr>

                {/* Investment Score */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center space-x-2">
                      <Award className="w-4 h-4 text-primary-600" />
                      <span>Investment Score</span>
                    </div>
                  </td>
                  {properties.map((property) => {
                    const analytics = generateMockAnalytics(property);
                    const isWinner = bestInvestment?.property.id === property.id;
                    return (
                      <td key={property.id} className="px-6 py-4 text-center">
                        <div className="space-y-2">
                          <div className={`text-2xl font-bold ${isWinner ? 'text-green-600' : 'text-gray-900'}`}>
                            {analytics.investmentScore}/100
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${isWinner ? 'bg-green-500' : 'bg-primary-500'}`}
                              style={{ width: `${analytics.investmentScore}%` }}
                            ></div>
                          </div>
                          {isWinner && (
                            <div className="text-xs text-green-600 font-medium">Winner</div>
                          )}
                        </div>
                      </td>
                    );
                  })}
                </tr>

                {/* Growth Potential */}
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="w-4 h-4 text-green-600" />
                      <span>Growth Potential</span>
                    </div>
                  </td>
                  {properties.map((property) => {
                    const analytics = generateMockAnalytics(property);
                    return (
                      <td key={property.id} className="px-6 py-4 text-center">
                        <div className="space-y-2">
                          <div className="text-lg font-semibold text-gray-900">
                            {analytics.growthPotential}%
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-green-500 h-2 rounded-full"
                              style={{ width: `${analytics.growthPotential}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-600">
                            +{analytics.yearlyGrowth}% yearly
                          </div>
                        </div>
                      </td>
                    );
                  })}
                </tr>

                {/* Development Score */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center space-x-2">
                      <Building className="w-4 h-4 text-blue-600" />
                      <span>Development Score</span>
                    </div>
                  </td>
                  {properties.map((property) => {
                    const analytics = generateMockAnalytics(property);
                    return (
                      <td key={property.id} className="px-6 py-4 text-center">
                        <div className="space-y-2">
                          <div className="text-lg font-semibold text-gray-900">
                            {analytics.developmentScore}%
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${analytics.developmentScore}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                    );
                  })}
                </tr>

                {/* Market Position */}
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="w-4 h-4 text-purple-600" />
                      <span>Market Position</span>
                    </div>
                  </td>
                  {properties.map((property) => {
                    const analytics = generateMockAnalytics(property);
                    return (
                      <td key={property.id} className="px-6 py-4 text-center">
                        <div className="space-y-2">
                          <div className="text-lg font-semibold text-gray-900">
                            {analytics.marketPosition}th percentile
                          </div>
                          <div className="text-xs text-gray-600">
                            vs suburb average
                          </div>
                        </div>
                      </td>
                    );
                  })}
                </tr>

                {/* Risk Level */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="w-4 h-4 text-orange-600" />
                      <span>Risk Level</span>
                    </div>
                  </td>
                  {properties.map((property) => {
                    const analytics = generateMockAnalytics(property);
                    return (
                      <td key={property.id} className="px-6 py-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          analytics.riskLevel === 'Low'
                            ? 'bg-green-100 text-green-800'
                            : analytics.riskLevel === 'Medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {analytics.riskLevel === 'Low' && <CheckCircle className="w-3 h-3 mr-1" />}
                          {analytics.riskLevel === 'High' && <XCircle className="w-3 h-3 mr-1" />}
                          {analytics.riskLevel}
                        </span>
                      </td>
                    );
                  })}
                </tr>

                {/* Rental Yield */}
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-green-600" />
                      <span>Rental Yield</span>
                    </div>
                  </td>
                  {properties.map((property) => {
                    const analytics = generateMockAnalytics(property);
                    return (
                      <td key={property.id} className="px-6 py-4 text-center">
                        <div className="text-lg font-semibold text-green-600">
                          {analytics.rentalYield}%
                        </div>
                        <div className="text-xs text-gray-600">per annum</div>
                      </td>
                    );
                  })}
                </tr>

                {/* Land/Building Size */}
                <tr>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">Property Size</td>
                  {properties.map((property) => (
                    <td key={property.id} className="px-6 py-4 text-center">
                      <div className="space-y-1">
                        {property.landSize && (
                          <div className="text-sm">
                            <span className="font-medium">Land:</span> {property.landSize} m²
                          </div>
                        )}
                        {property.buildingSize && (
                          <div className="text-sm">
                            <span className="font-medium">Building:</span> {property.buildingSize} m²
                          </div>
                        )}
                        {property.yearBuilt && (
                          <div className="text-sm text-gray-600">
                            Built {property.yearBuilt}
                          </div>
                        )}
                      </div>
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
