'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Calculator, TrendingUp, Clock, CheckCircle, AlertCircle, Plus } from 'lucide-react';
import { useAuth } from '@/lib/auth';
import { apiClient } from '@/lib/api';
import { formatCurrency, formatDate, getConfidenceColor, getConfidenceLabel } from '@/lib/utils';
import { ValuationMethod, ValuationStatus } from '@/types/api';
import { Loading, LoadingCard } from '@/components/ui/loading';

export default function ValuationsPage() {
  const { isAuthenticated } = useAuth();
  const [filters, setFilters] = useState({
    page: 1,
    limit: 12,
  });

  const { data: valuationsData, isLoading, error } = useQuery({
    queryKey: ['valuations', filters],
    queryFn: () => apiClient.getValuations(filters),
    enabled: isAuthenticated,
  });

  const getStatusIcon = (status: ValuationStatus) => {
    switch (status) {
      case ValuationStatus.COMPLETED:
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case ValuationStatus.IN_PROGRESS:
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case ValuationStatus.PENDING:
        return <Clock className="w-5 h-5 text-gray-500" />;
      case ValuationStatus.EXPIRED:
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case ValuationStatus.CANCELLED:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: ValuationStatus) => {
    switch (status) {
      case ValuationStatus.COMPLETED:
        return 'text-green-600 bg-green-100';
      case ValuationStatus.IN_PROGRESS:
        return 'text-yellow-600 bg-yellow-100';
      case ValuationStatus.PENDING:
        return 'text-gray-600 bg-gray-100';
      case ValuationStatus.EXPIRED:
        return 'text-red-600 bg-red-100';
      case ValuationStatus.CANCELLED:
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getMethodLabel = (method: ValuationMethod) => {
    switch (method) {
      case ValuationMethod.AUTOMATED:
        return 'Automated';
      case ValuationMethod.COMPARATIVE:
        return 'Comparative';
      case ValuationMethod.PROFESSIONAL:
        return 'Professional';
      case ValuationMethod.HYBRID:
        return 'Hybrid';
      default:
        return method;
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Calculator className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Sign in Required</h2>
          <p className="text-gray-600 mb-6">Please sign in to view your valuations.</p>
          <a href="/auth/login" className="btn-primary">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Something went wrong</h2>
          <p className="text-gray-600">Failed to load valuations. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Valuations</h1>
            <button className="btn-primary">
              <Plus className="w-4 h-4 mr-2" />
              New Valuation
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Calculator className="w-6 h-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Valuations</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {isLoading ? <Loading size="sm" /> : valuationsData?.meta.total || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {isLoading ? <Loading size="sm" /> : 
                      valuationsData?.data.filter(v => v.status === ValuationStatus.COMPLETED).length || 0
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">In Progress</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {isLoading ? <Loading size="sm" /> : 
                      valuationsData?.data.filter(v => v.status === ValuationStatus.IN_PROGRESS).length || 0
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Value</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {isLoading ? <Loading size="sm" /> : 
                      valuationsData?.data.length ? 
                        formatCurrency(
                          valuationsData.data.reduce((sum, v) => sum + v.estimatedValue, 0) / valuationsData.data.length
                        ) : '$0'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Valuations List */}
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
        ) : valuationsData?.data.length === 0 ? (
          <div className="text-center py-12">
            <Calculator className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No valuations yet</h3>
            <p className="text-gray-600 mb-6">Get started by creating your first property valuation.</p>
            <button className="btn-primary">
              <Plus className="w-4 h-4 mr-2" />
              Create Valuation
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {valuationsData?.data.map((valuation) => (
              <div key={valuation.id} className="card hover:shadow-lg transition-shadow">
                <div className="card-content">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        {getStatusIcon(valuation.status)}
                        <h3 className="text-lg font-semibold text-gray-900">
                          Property Valuation #{valuation.id.slice(-8)}
                        </h3>
                        <span className={`badge ${getStatusColor(valuation.status)}`}>
                          {valuation.status.replace('_', ' ')}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-600">Estimated Value</p>
                          <p className="text-xl font-bold text-primary-600">
                            {formatCurrency(valuation.estimatedValue)}
                          </p>
                        </div>
                        
                        <div>
                          <p className="text-sm text-gray-600">Method</p>
                          <p className="font-medium text-gray-900">
                            {getMethodLabel(valuation.method)}
                          </p>
                        </div>
                        
                        <div>
                          <p className="text-sm text-gray-600">Confidence</p>
                          <p className={`font-medium ${getConfidenceColor(valuation.confidence)}`}>
                            {valuation.confidence ? 
                              `${(valuation.confidence * 100).toFixed(0)}% (${getConfidenceLabel(valuation.confidence)})` : 
                              'N/A'
                            }
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>Created: {formatDate(valuation.valuationDate)}</span>
                        {valuation.expiryDate && (
                          <span>Expires: {formatDate(valuation.expiryDate)}</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="ml-6">
                      <button className="btn-outline btn-sm">
                        View Details
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {valuationsData && valuationsData.meta.totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <div className="flex space-x-2">
              <button
                onClick={() => setFilters(prev => ({ ...prev, page: valuationsData.meta.page - 1 }))}
                disabled={!valuationsData.meta.hasPreviousPage}
                className="btn-outline disabled:opacity-50"
              >
                Previous
              </button>
              
              {Array.from({ length: Math.min(5, valuationsData.meta.totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => setFilters(prev => ({ ...prev, page }))}
                    className={`btn ${page === valuationsData.meta.page ? 'btn-primary' : 'btn-outline'}`}
                  >
                    {page}
                  </button>
                );
              })}
              
              <button
                onClick={() => setFilters(prev => ({ ...prev, page: valuationsData.meta.page + 1 }))}
                disabled={!valuationsData.meta.hasNextPage}
                className="btn-outline disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
