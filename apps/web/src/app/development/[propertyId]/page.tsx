'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Building, Calculator, TrendingUp, MapPin, AlertTriangle } from 'lucide-react';
import DevelopmentAnalyzer from '@/components/development/DevelopmentAnalyzer';
import BuildingEnvelopeVisualizer from '@/components/development/BuildingEnvelopeVisualizer';
import DevelopmentSimulator from '@/components/development/DevelopmentSimulator';
import ROIProjections from '@/components/development/ROIProjections';
import DAPrecedentAnalysis from '@/components/development/DAPrecedentAnalysis';

interface PropertyDetails {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  zoning: string;
  developmentScore: number;
  coordinates: {
    lat: number;
    lng: number;
  };
  buildingEnvelope: {
    maxHeight: number;
    setbacks: {
      front: number;
      rear: number;
      side: number;
    };
    floorSpaceRatio: number;
    siteCoverage: number;
  };
  developmentPotential: {
    maxDwellings: number;
    estimatedValue: number;
    roi: number;
    developmentCost: number;
    profit: number;
  };
}

// Mock property data
const mockPropertyData: { [key: string]: PropertyDetails } = {
  '1': {
    id: '1',
    address: '45 Collins Street',
    suburb: 'Melbourne',
    state: 'VIC',
    postcode: '3000',
    landSize: 800,
    currentPrice: 2500000,
    zoning: 'Mixed Use',
    developmentScore: 95,
    coordinates: { lat: -37.8136, lng: 144.9631 },
    buildingEnvelope: {
      maxHeight: 45,
      setbacks: { front: 3, rear: 6, side: 3 },
      floorSpaceRatio: 8.0,
      siteCoverage: 85
    },
    developmentPotential: {
      maxDwellings: 12,
      estimatedValue: 4800000,
      roi: 92,
      developmentCost: 2300000,
      profit: 2300000
    }
  }
};

export default function PropertyDevelopmentPage() {
  const params = useParams();
  const router = useRouter();
  const propertyId = params.propertyId as string;
  
  const [property, setProperty] = useState<PropertyDetails | null>(null);
  const [activeTab, setActiveTab] = useState<'analyzer' | 'envelope' | 'simulator' | 'roi' | 'precedents'>('analyzer');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const propertyData = mockPropertyData[propertyId];
      if (propertyData) {
        setProperty(propertyData);
      }
      setLoading(false);
    }, 1000);
  }, [propertyId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading development analysis...</p>
        </div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Property Not Found</h2>
          <p className="text-gray-600 mb-4">The requested property could not be found.</p>
          <button
            onClick={() => router.push('/development')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Development Analysis
          </button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'analyzer', label: 'Analysis', icon: Calculator },
    { id: 'envelope', label: '3D Envelope', icon: Building },
    { id: 'simulator', label: 'Simulator', icon: TrendingUp },
    { id: 'roi', label: 'ROI Projections', icon: TrendingUp },
    { id: 'precedents', label: 'DA Precedents', icon: MapPin }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/development')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{property.address}</h1>
                <div className="flex items-center text-gray-600 mt-1">
                  <MapPin className="h-4 w-4 mr-1" />
                  {property.suburb}, {property.state} {property.postcode}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-center">
                <div className="text-sm text-gray-500">Development Score</div>
                <div className="text-2xl font-bold text-green-600">{property.developmentScore}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-500">Potential ROI</div>
                <div className="text-2xl font-bold text-blue-600">{property.developmentPotential.roi}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Property Summary */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            <div>
              <div className="text-sm text-gray-500">Land Size</div>
              <div className="text-lg font-semibold">{property.landSize}m²</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Current Price</div>
              <div className="text-lg font-semibold">${(property.currentPrice / 1000000).toFixed(1)}M</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Zoning</div>
              <div className="text-lg font-semibold">{property.zoning}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Max Dwellings</div>
              <div className="text-lg font-semibold">{property.developmentPotential.maxDwellings}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Est. Value</div>
              <div className="text-lg font-semibold">${(property.developmentPotential.estimatedValue / 1000000).toFixed(1)}M</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Est. Profit</div>
              <div className="text-lg font-semibold text-green-600">${(property.developmentPotential.profit / 1000000).toFixed(1)}M</div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'analyzer' && <DevelopmentAnalyzer property={property} />}
            {activeTab === 'envelope' && <BuildingEnvelopeVisualizer property={property} />}
            {activeTab === 'simulator' && <DevelopmentSimulator property={property} />}
            {activeTab === 'roi' && <ROIProjections property={property} />}
            {activeTab === 'precedents' && <DAPrecedentAnalysis property={property} />}
          </div>
        </div>
      </div>
    </div>
  );
}
