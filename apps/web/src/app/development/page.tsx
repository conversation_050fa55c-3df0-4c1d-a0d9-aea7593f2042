'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, TrendingUp, Calculator, Building, MapPin, Filter } from 'lucide-react';

interface Property {
  id: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  landSize: number;
  currentPrice: number;
  developmentScore: number;
  developmentPotential: {
    maxDwellings: number;
    estimatedValue: number;
    roi: number;
  };
}

// Mock data for development-ready properties
const mockDevelopmentProperties: Property[] = [
  {
    id: '1',
    address: '45 Collins Street',
    suburb: 'Melbourne',
    state: 'VIC',
    postcode: '3000',
    landSize: 800,
    currentPrice: 2500000,
    developmentScore: 95,
    developmentPotential: {
      maxDwellings: 12,
      estimatedValue: 4800000,
      roi: 92
    }
  },
  {
    id: '2',
    address: '123 George Street',
    suburb: 'Sydney',
    state: 'NSW',
    postcode: '2000',
    landSize: 650,
    currentPrice: 3200000,
    developmentScore: 88,
    developmentPotential: {
      maxDwellings: 8,
      estimatedValue: 5600000,
      roi: 75
    }
  },
  {
    id: '3',
    address: '78 Queen Street',
    suburb: 'Brisbane',
    state: 'QLD',
    postcode: '4000',
    landSize: 920,
    currentPrice: 1800000,
    developmentScore: 82,
    developmentPotential: {
      maxDwellings: 15,
      estimatedValue: 3900000,
      roi: 117
    }
  }
];

export default function DevelopmentAnalysisPage() {
  const router = useRouter();
  const [properties, setProperties] = useState<Property[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'score' | 'roi' | 'value'>('score');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setProperties(mockDevelopmentProperties);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredProperties = properties
    .filter(property => 
      property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.suburb.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.developmentScore - a.developmentScore;
        case 'roi':
          return b.developmentPotential.roi - a.developmentPotential.roi;
        case 'value':
          return b.developmentPotential.estimatedValue - a.developmentPotential.estimatedValue;
        default:
          return 0;
      }
    });

  const handlePropertyClick = (propertyId: string) => {
    router.push(`/development/${propertyId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading development opportunities...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Building className="h-8 w-8 text-blue-600" />
                Development Analysis
              </h1>
              <p className="text-gray-600 mt-2">
                Discover high-potential development opportunities with AI-powered analysis
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="bg-blue-50 px-4 py-2 rounded-lg">
                <div className="text-sm text-blue-600 font-medium">Active Opportunities</div>
                <div className="text-2xl font-bold text-blue-900">{properties.length}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by address or suburb..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Sort */}
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-400" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'score' | 'roi' | 'value')}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="score">Development Score</option>
                <option value="roi">ROI Potential</option>
                <option value="value">Estimated Value</option>
              </select>
            </div>
          </div>
        </div>

        {/* Properties Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredProperties.map((property) => (
            <div
              key={property.id}
              onClick={() => handlePropertyClick(property.id)}
              className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-200"
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{property.address}</h3>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-1" />
                      {property.suburb}, {property.state} {property.postcode}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500">Development Score</div>
                    <div className="text-2xl font-bold text-green-600">{property.developmentScore}</div>
                  </div>
                </div>

                {/* Metrics */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-gray-500">Land Size</div>
                    <div className="font-semibold">{property.landSize}m²</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Current Price</div>
                    <div className="font-semibold">${(property.currentPrice / 1000000).toFixed(1)}M</div>
                  </div>
                </div>

                {/* Development Potential */}
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="text-sm font-medium text-blue-900 mb-2">Development Potential</div>
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div>
                      <div className="text-blue-600">Max Units</div>
                      <div className="font-semibold">{property.developmentPotential.maxDwellings}</div>
                    </div>
                    <div>
                      <div className="text-blue-600">Est. Value</div>
                      <div className="font-semibold">${(property.developmentPotential.estimatedValue / 1000000).toFixed(1)}M</div>
                    </div>
                    <div>
                      <div className="text-blue-600">ROI</div>
                      <div className="font-semibold text-green-600">{property.developmentPotential.roi}%</div>
                    </div>
                  </div>
                </div>

                {/* Action Button */}
                <button className="w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2">
                  <Calculator className="h-4 w-4" />
                  Analyze Development
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredProperties.length === 0 && (
          <div className="text-center py-12">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
            <p className="text-gray-600">Try adjusting your search criteria</p>
          </div>
        )}
      </div>
    </div>
  );
}
