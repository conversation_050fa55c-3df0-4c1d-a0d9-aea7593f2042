'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Search, MapPin, TrendingUp, Users, DollarSign, Star } from 'lucide-react';
import { apiClient } from '@/lib/api';
import { formatCurrency, formatNumber } from '@/lib/utils';
import { SuburbSearchParams } from '@/types/api';
import { Loading, LoadingCard } from '@/components/ui/loading';

export default function SuburbsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<SuburbSearchParams>({
    page: 1,
    limit: 12,
  });

  const { data: suburbsData, isLoading, error } = useQuery({
    queryKey: ['suburbs', filters],
    queryFn: () => apiClient.getSuburbs(filters),
  });

  const { data: topSuburbs } = useQuery({
    queryKey: ['top-suburbs'],
    queryFn: () => apiClient.getTopSuburbs('overall'),
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters(prev => ({ ...prev, search: searchQuery || undefined, page: 1 }));
  };

  const handleFilterChange = (key: keyof SuburbSearchParams, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Something went wrong</h2>
          <p className="text-gray-600">Failed to load suburbs. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Suburbs</h1>
            <div className="flex items-center space-x-4">
              <button className="btn-outline">
                <MapPin className="w-4 h-4 mr-2" />
                Map View
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <form onSubmit={handleSearch} className="mb-6">
            <div className="relative max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search suburbs by name, postcode, or state..."
                className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary"
              >
                Search
              </button>
            </div>
          </form>

          {/* Quick Filters */}
          <div className="flex flex-wrap gap-4">
            <select
              value={filters.state || ''}
              onChange={(e) => handleFilterChange('state', e.target.value || undefined)}
              className="input w-auto"
            >
              <option value="">All States</option>
              <option value="NSW">NSW</option>
              <option value="VIC">VIC</option>
              <option value="QLD">QLD</option>
              <option value="WA">WA</option>
              <option value="SA">SA</option>
              <option value="TAS">TAS</option>
              <option value="ACT">ACT</option>
              <option value="NT">NT</option>
            </select>

            <input
              type="number"
              placeholder="Min Price"
              value={filters.minPrice || ''}
              onChange={(e) => handleFilterChange('minPrice', e.target.value ? Number(e.target.value) : undefined)}
              className="input w-32"
            />

            <input
              type="number"
              placeholder="Max Price"
              value={filters.maxPrice || ''}
              onChange={(e) => handleFilterChange('maxPrice', e.target.value ? Number(e.target.value) : undefined)}
              className="input w-32"
            />

            <select
              value={filters.sortBy || ''}
              onChange={(e) => handleFilterChange('sortBy', e.target.value || undefined)}
              className="input w-auto"
            >
              <option value="">Sort By</option>
              <option value="name">Name</option>
              <option value="medianHousePrice">Median Price</option>
              <option value="population">Population</option>
              <option value="schoolRating">School Rating</option>
            </select>
          </div>
        </div>

        {/* Top Suburbs Section */}
        {topSuburbs && topSuburbs.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Top Performing Suburbs</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {topSuburbs.slice(0, 3).map((suburb, index) => (
                <div key={suburb.id} className="card">
                  <div className="card-content">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {suburb.name}, {suburb.state}
                      </h3>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-500 mr-1" />
                        <span className="text-sm font-medium">#{index + 1}</span>
                      </div>
                    </div>
                    <p className="text-gray-600 mb-2">{suburb.postcode}</p>
                    {suburb.medianHousePrice && (
                      <p className="text-lg font-bold text-primary-600">
                        {formatCurrency(suburb.medianHousePrice)}
                      </p>
                    )}
                    <div className="flex items-center justify-between mt-3 text-sm text-gray-600">
                      {suburb.population && (
                        <span className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {formatNumber(suburb.population)}
                        </span>
                      )}
                      {suburb.schoolRating && (
                        <span className="flex items-center">
                          <Star className="w-4 h-4 mr-1" />
                          {suburb.schoolRating}/10
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Results */}
        <div className="mb-6">
          {suburbsData && (
            <p className="text-gray-600">
              Showing {suburbsData.data.length} of {suburbsData.meta.total} suburbs
            </p>
          )}
        </div>

        {/* Suburbs Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {suburbsData?.data.map((suburb) => (
              <div key={suburb.id} className="card hover:shadow-lg transition-shadow cursor-pointer">
                <div className="card-content">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {suburb.name}
                      </h3>
                      <p className="text-gray-600">{suburb.state} {suburb.postcode}</p>
                    </div>
                    <MapPin className="w-5 h-5 text-gray-400" />
                  </div>

                  <div className="space-y-3">
                    {suburb.medianHousePrice && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Median House Price</span>
                        <span className="font-semibold text-primary-600">
                          {formatCurrency(suburb.medianHousePrice)}
                        </span>
                      </div>
                    )}

                    {suburb.medianUnitPrice && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Median Unit Price</span>
                        <span className="font-semibold text-primary-600">
                          {formatCurrency(suburb.medianUnitPrice)}
                        </span>
                      </div>
                    )}

                    {suburb.population && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Population</span>
                        <span className="font-medium">{formatNumber(suburb.population)}</span>
                      </div>
                    )}

                    {suburb.schoolRating && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">School Rating</span>
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-500 mr-1" />
                          <span className="font-medium">{suburb.schoolRating}/10</span>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <button className="btn-ghost btn-sm w-full">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {suburbsData && suburbsData.meta.totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(suburbsData.meta.page - 1)}
                disabled={!suburbsData.meta.hasPreviousPage}
                className="btn-outline disabled:opacity-50"
              >
                Previous
              </button>
              
              {Array.from({ length: Math.min(5, suburbsData.meta.totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`btn ${page === suburbsData.meta.page ? 'btn-primary' : 'btn-outline'}`}
                  >
                    {page}
                  </button>
                );
              })}
              
              <button
                onClick={() => handlePageChange(suburbsData.meta.page + 1)}
                disabled={!suburbsData.meta.hasNextPage}
                className="btn-outline disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
