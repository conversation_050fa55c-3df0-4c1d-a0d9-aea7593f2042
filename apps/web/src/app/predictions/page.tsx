'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Target, Brain, TrendingUp, AlertTriangle, Calendar, Zap, BarChart3, Settings } from 'lucide-react';

interface PredictionOverview {
  totalPredictions: number;
  averageAccuracy: number;
  activePredictions: number;
  highConfidencePredictions: number;
}

interface PredictionModel {
  id: string;
  name: string;
  type: 'price_prediction' | 'market_timing' | 'investment_score' | 'risk_assessment';
  accuracy: number;
  confidence: number;
  lastUpdated: string;
  status: 'active' | 'training' | 'inactive';
}

interface QuickPrediction {
  id: string;
  suburb: string;
  state: string;
  predictionType: string;
  currentValue: number;
  predictedValue: number;
  timeframe: string;
  confidence: number;
  trend: 'bullish' | 'bearish' | 'stable';
}

// Mock data
const mockOverview: PredictionOverview = {
  totalPredictions: 1247,
  averageAccuracy: 87.3,
  activePredictions: 156,
  highConfidencePredictions: 89
};

const mockModels: PredictionModel[] = [
  {
    id: '1',
    name: 'Price Prediction Engine',
    type: 'price_prediction',
    accuracy: 89.2,
    confidence: 94,
    lastUpdated: '2024-01-15',
    status: 'active'
  },
  {
    id: '2',
    name: 'Market Timing Optimizer',
    type: 'market_timing',
    accuracy: 85.7,
    confidence: 88,
    lastUpdated: '2024-01-14',
    status: 'active'
  },
  {
    id: '3',
    name: 'Investment Score Calculator',
    type: 'investment_score',
    accuracy: 91.4,
    confidence: 96,
    lastUpdated: '2024-01-15',
    status: 'active'
  },
  {
    id: '4',
    name: 'Risk Assessment Model',
    type: 'risk_assessment',
    accuracy: 83.6,
    confidence: 82,
    lastUpdated: '2024-01-13',
    status: 'training'
  }
];

const mockQuickPredictions: QuickPrediction[] = [
  {
    id: '1',
    suburb: 'Teneriffe',
    state: 'QLD',
    predictionType: 'Price Growth',
    currentValue: 1250000,
    predictedValue: 1425000,
    timeframe: '12 months',
    confidence: 92,
    trend: 'bullish'
  },
  {
    id: '2',
    suburb: 'Fitzroy North',
    state: 'VIC',
    predictionType: 'Investment Score',
    currentValue: 78,
    predictedValue: 85,
    timeframe: '6 months',
    confidence: 88,
    trend: 'bullish'
  },
  {
    id: '3',
    suburb: 'Newtown',
    state: 'NSW',
    predictionType: 'Market Timing',
    currentValue: 1680000,
    predictedValue: 1595000,
    timeframe: '3 months',
    confidence: 76,
    trend: 'bearish'
  }
];

export default function PredictiveAnalyticsPage() {
  const router = useRouter();
  const [overview, setOverview] = useState<PredictionOverview | null>(null);
  const [models, setModels] = useState<PredictionModel[]>([]);
  const [quickPredictions, setQuickPredictions] = useState<QuickPrediction[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'3m' | '6m' | '12m' | '24m'>('12m');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setOverview(mockOverview);
      setModels(mockModels);
      setQuickPredictions(mockQuickPredictions);
      setLoading(false);
    }, 1000);
  }, [selectedTimeframe]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getModelTypeIcon = (type: string) => {
    switch (type) {
      case 'price_prediction':
        return <TrendingUp className="h-5 w-5 text-blue-500" />;
      case 'market_timing':
        return <Calendar className="h-5 w-5 text-green-500" />;
      case 'investment_score':
        return <Target className="h-5 w-5 text-purple-500" />;
      case 'risk_assessment':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      default:
        return <Brain className="h-5 w-5 text-gray-500" />;
    }
  };

  const getModelTypeName = (type: string) => {
    switch (type) {
      case 'price_prediction':
        return 'Price Prediction';
      case 'market_timing':
        return 'Market Timing';
      case 'investment_score':
        return 'Investment Score';
      case 'risk_assessment':
        return 'Risk Assessment';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'training':
        return 'bg-blue-100 text-blue-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'bullish':
        return 'text-green-600';
      case 'bearish':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600 bg-green-50';
    if (confidence >= 75) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading predictive analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Brain className="h-8 w-8 text-purple-600" />
                Predictive Analytics
              </h1>
              <p className="text-gray-600 mt-2">
                AI-powered predictions and scenario modeling for informed investment decisions
              </p>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/predictions/scenario-modeling')}
                className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
              >
                <Zap className="h-5 w-5" />
                Scenario Modeling
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {overview && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center gap-2 mb-2">
                <Brain className="h-5 w-5 text-purple-500" />
                <h3 className="font-medium text-gray-900">Total Predictions</h3>
              </div>
              <div className="text-2xl font-bold text-purple-600">{overview.totalPredictions.toLocaleString()}</div>
              <div className="text-sm text-gray-500">Generated models</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center gap-2 mb-2">
                <Target className="h-5 w-5 text-green-500" />
                <h3 className="font-medium text-gray-900">Average Accuracy</h3>
              </div>
              <div className="text-2xl font-bold text-green-600">{overview.averageAccuracy}%</div>
              <div className="text-sm text-gray-500">Model performance</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-5 w-5 text-blue-500" />
                <h3 className="font-medium text-gray-900">Active Predictions</h3>
              </div>
              <div className="text-2xl font-bold text-blue-600">{overview.activePredictions}</div>
              <div className="text-sm text-gray-500">Currently running</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-gray-900">High Confidence</h3>
              </div>
              <div className="text-2xl font-bold text-orange-600">{overview.highConfidencePredictions}</div>
              <div className="text-sm text-gray-500">90%+ confidence</div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <button
            onClick={() => router.push('/predictions/price-prediction')}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow text-left"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="font-medium text-gray-900">Price Prediction</h3>
            </div>
            <p className="text-sm text-gray-600">Forecast property values</p>
          </button>

          <button
            onClick={() => router.push('/predictions/scenario-modeling')}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow text-left"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Zap className="h-5 w-5 text-purple-600" />
              </div>
              <h3 className="font-medium text-gray-900">Scenario Modeling</h3>
            </div>
            <p className="text-sm text-gray-600">What-if analysis</p>
          </button>

          <button
            onClick={() => router.push('/predictions/risk-assessment')}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow text-left"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
              </div>
              <h3 className="font-medium text-gray-900">Risk Assessment</h3>
            </div>
            <p className="text-sm text-gray-600">Investment risk analysis</p>
          </button>

          <button
            onClick={() => router.push('/predictions/market-timing')}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow text-left"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-5 w-5 text-green-600" />
              </div>
              <h3 className="font-medium text-gray-900">Market Timing</h3>
            </div>
            <p className="text-sm text-gray-600">Optimal timing analysis</p>
          </button>
        </div>

        {/* AI Models Status */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-500" />
                AI Models Status
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {models.map((model) => (
                  <div key={model.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      {getModelTypeIcon(model.type)}
                      <div>
                        <h4 className="font-medium text-gray-900">{model.name}</h4>
                        <div className="text-sm text-gray-600">
                          {getModelTypeName(model.type)} • Updated {new Date(model.lastUpdated).toLocaleDateString('en-AU')}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-2 mb-1">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(model.status)}`}>
                          {model.status}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {model.accuracy}% accuracy • {model.confidence}% confidence
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={() => router.push('/predictions/models')}
                className="w-full mt-4 text-purple-600 hover:text-purple-700 font-medium text-sm"
              >
                Manage AI Models →
              </button>
            </div>
          </div>

          {/* Recent Predictions */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Target className="h-5 w-5 text-green-500" />
                Recent Predictions
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {quickPredictions.map((prediction) => (
                  <div key={prediction.id} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{prediction.suburb}, {prediction.state}</h4>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getConfidenceColor(prediction.confidence)}`}>
                        {prediction.confidence}% confidence
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 mb-2">{prediction.predictionType} • {prediction.timeframe}</div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-gray-500">Current → Predicted</div>
                        <div className="font-medium">
                          {prediction.predictionType.includes('Score') ? 
                            `${prediction.currentValue} → ${prediction.predictedValue}` :
                            `${formatCurrency(prediction.currentValue)} → ${formatCurrency(prediction.predictedValue)}`
                          }
                        </div>
                      </div>
                      <div className={`text-sm font-medium ${getTrendColor(prediction.trend)}`}>
                        {prediction.trend === 'bullish' ? '↗' : prediction.trend === 'bearish' ? '↘' : '→'} 
                        {prediction.trend}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={() => router.push('/predictions/all')}
                className="w-full mt-4 text-purple-600 hover:text-purple-700 font-medium text-sm"
              >
                View All Predictions →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
