'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>Lef<PERSON>, Zap, Play, RotateCcw, Download, Settings } from 'lucide-react';
import ScenarioBuilder from '@/components/predictions/ScenarioBuilder';
import ScenarioResults from '@/components/predictions/ScenarioResults';

interface ScenarioParameters {
  propertyType: 'house' | 'unit' | 'townhouse';
  location: string;
  currentPrice: number;
  timeframe: '6m' | '12m' | '24m' | '60m';
  marketConditions: 'bull' | 'bear' | 'stable';
  interestRates: 'rising' | 'falling' | 'stable';
  populationGrowth: 'high' | 'medium' | 'low';
  infrastructureInvestment: 'major' | 'minor' | 'none';
  economicFactors: {
    gdpGrowth: number;
    unemployment: number;
    inflation: number;
  };
}

interface ScenarioResult {
  scenario: string;
  probability: number;
  priceChange: number;
  priceChangePercent: number;
  finalPrice: number;
  confidence: number;
  keyFactors: string[];
  risks: string[];
  opportunities: string[];
}

export default function ScenarioModelingPage() {
  const router = useRouter();
  const [parameters, setParameters] = useState<ScenarioParameters>({
    propertyType: 'house',
    location: 'Melbourne CBD',
    currentPrice: 800000,
    timeframe: '12m',
    marketConditions: 'stable',
    interestRates: 'stable',
    populationGrowth: 'medium',
    infrastructureInvestment: 'minor',
    economicFactors: {
      gdpGrowth: 2.5,
      unemployment: 4.2,
      inflation: 3.1
    }
  });

  const [results, setResults] = useState<ScenarioResult[] | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedScenario, setSelectedScenario] = useState<string>('base');

  const runScenario = async () => {
    setIsRunning(true);
    
    // Simulate AI processing
    setTimeout(() => {
      const mockResults = generateScenarioResults();
      setResults(mockResults);
      setIsRunning(false);
    }, 3000);
  };

  const generateScenarioResults = (): ScenarioResult[] => {
    const basePrice = parameters.currentPrice;
    
    // Generate different scenarios based on parameters
    const scenarios = [
      {
        scenario: 'Optimistic',
        probability: 25,
        priceChangePercent: getOptimisticChange(),
        keyFactors: ['Strong economic growth', 'Low interest rates', 'High demand'],
        risks: ['Market overheating', 'Policy changes'],
        opportunities: ['Capital growth', 'Rental yield improvement']
      },
      {
        scenario: 'Base Case',
        probability: 50,
        priceChangePercent: getBaseChange(),
        keyFactors: ['Stable market conditions', 'Moderate growth', 'Balanced supply/demand'],
        risks: ['Interest rate changes', 'Economic uncertainty'],
        opportunities: ['Steady appreciation', 'Market stability']
      },
      {
        scenario: 'Pessimistic',
        probability: 25,
        priceChangePercent: getPessimisticChange(),
        keyFactors: ['Economic downturn', 'Rising rates', 'Oversupply'],
        risks: ['Price decline', 'Liquidity issues'],
        opportunities: ['Buying opportunity', 'Market correction']
      }
    ];

    return scenarios.map(scenario => {
      const priceChange = basePrice * (scenario.priceChangePercent / 100);
      const finalPrice = basePrice + priceChange;
      const confidence = 85 + Math.random() * 10; // 85-95% confidence

      return {
        ...scenario,
        priceChange,
        finalPrice,
        confidence: Math.round(confidence)
      };
    });
  };

  const getOptimisticChange = () => {
    let change = 8; // Base optimistic growth
    
    if (parameters.marketConditions === 'bull') change += 5;
    if (parameters.interestRates === 'falling') change += 3;
    if (parameters.populationGrowth === 'high') change += 2;
    if (parameters.infrastructureInvestment === 'major') change += 4;
    if (parameters.timeframe === '24m') change *= 1.8;
    if (parameters.timeframe === '60m') change *= 3.5;
    
    return Math.min(change, 25); // Cap at 25%
  };

  const getBaseChange = () => {
    let change = 4; // Base stable growth
    
    if (parameters.marketConditions === 'bull') change += 2;
    if (parameters.marketConditions === 'bear') change -= 2;
    if (parameters.interestRates === 'rising') change -= 1;
    if (parameters.populationGrowth === 'high') change += 1;
    if (parameters.infrastructureInvestment === 'major') change += 2;
    if (parameters.timeframe === '24m') change *= 1.6;
    if (parameters.timeframe === '60m') change *= 2.8;
    
    return Math.max(change, -5); // Floor at -5%
  };

  const getPessimisticChange = () => {
    let change = -2; // Base pessimistic decline
    
    if (parameters.marketConditions === 'bear') change -= 5;
    if (parameters.interestRates === 'rising') change -= 3;
    if (parameters.populationGrowth === 'low') change -= 2;
    if (parameters.timeframe === '24m') change *= 1.5;
    if (parameters.timeframe === '60m') change *= 2.2;
    
    return Math.max(change, -20); // Floor at -20%
  };

  const resetParameters = () => {
    setParameters({
      propertyType: 'house',
      location: 'Melbourne CBD',
      currentPrice: 800000,
      timeframe: '12m',
      marketConditions: 'stable',
      interestRates: 'stable',
      populationGrowth: 'medium',
      infrastructureInvestment: 'minor',
      economicFactors: {
        gdpGrowth: 2.5,
        unemployment: 4.2,
        inflation: 3.1
      }
    });
    setResults(null);
  };

  const exportResults = () => {
    if (!results) return;
    
    const csvContent = [
      ['Scenario', 'Probability', 'Price Change %', 'Final Price', 'Confidence'].join(','),
      ...results.map(result => 
        [result.scenario, result.probability, result.priceChangePercent.toFixed(2), result.finalPrice, result.confidence].join(',')
      )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `scenario-analysis-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/predictions')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <Zap className="h-7 w-7 text-purple-600" />
                  Scenario Modeling
                </h1>
                <p className="text-gray-600 mt-1">
                  What-if analysis for different market conditions and investment scenarios
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {results && (
                <button
                  onClick={exportResults}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export
                </button>
              )}
              <button
                onClick={resetParameters}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Reset
              </button>
              <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Settings className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Scenario Builder */}
          <div className="lg:col-span-1">
            <ScenarioBuilder
              parameters={parameters}
              onParametersChange={setParameters}
              onRunScenario={runScenario}
              isRunning={isRunning}
            />
          </div>

          {/* Results */}
          <div className="lg:col-span-2">
            {isRunning ? (
              <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-6"></div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Running Scenario Analysis</h3>
                <p className="text-gray-600 mb-2">Processing market conditions and economic factors...</p>
                <div className="text-sm text-gray-500">This may take a few moments</div>
              </div>
            ) : results ? (
              <ScenarioResults
                results={results}
                parameters={parameters}
                selectedScenario={selectedScenario}
                onScenarioSelect={setSelectedScenario}
              />
            ) : (
              <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                <Zap className="h-16 w-16 text-purple-600 mx-auto mb-6" />
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Ready for Analysis</h3>
                <p className="text-gray-600 mb-6">
                  Configure your scenario parameters and run the analysis to see potential outcomes
                  under different market conditions.
                </p>
                <button
                  onClick={runScenario}
                  className="bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 mx-auto"
                >
                  <Play className="h-5 w-5" />
                  Run Analysis
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
