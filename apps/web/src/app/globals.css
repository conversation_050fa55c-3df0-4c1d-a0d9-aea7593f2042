@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 h-10 py-2 px-4;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 py-2 px-4;
  }

  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900 h-10 py-2 px-4;
  }
  
  .btn-sm {
    @apply h-9 px-3 text-xs;
  }
  
  .btn-lg {
    @apply h-11 px-8;
  }

  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Card styles */
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-gray-600;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .badge-default {
    @apply badge border-transparent bg-primary-600 text-white hover:bg-primary-700;
  }

  .badge-secondary {
    @apply badge border-transparent bg-secondary-100 text-secondary-900 hover:bg-secondary-200;
  }

  .badge-destructive {
    @apply badge border-transparent bg-red-600 text-white hover:bg-red-700;
  }

  .badge-outline {
    @apply badge text-gray-900 border-gray-300;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
