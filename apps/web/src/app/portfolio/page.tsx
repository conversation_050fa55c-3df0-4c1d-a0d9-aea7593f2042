'use client';

import { LoadingCard } from '@/components/ui/loading';
import { apiClient } from '@/lib/api';
import { useAuth, withAuth } from '@/lib/auth';
import { formatCurrency, formatPropertyFeatures } from '@/lib/utils';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    Activity,
    AlertCircle,
    BarChart3,
    Bell,
    Calendar,
    Home,
    MapPin,
    PieChart,
    Target,
    Trash2,
    TrendingUp
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-hot-toast';

// Helper function to generate mock tracking metrics
const generateTrackingMetrics = (property: any) => {
  const trackedDays = Math.floor(Math.random() * 180) + 30; // 30-210 days
  const originalValue = property.currentPrice || property.lastSalePrice || 1000000;
  const currentValue = originalValue * (1 + (Math.random() * 0.2 - 0.1)); // ±10% change
  const valueChange = currentValue - originalValue;
  const percentChange = ((valueChange / originalValue) * 100);
  const alertsSet = Math.floor(Math.random() * 4) + 1; // 1-4 alerts
  
  return {
    trackedSince: new Date(Date.now() - trackedDays * 24 * 60 * 60 * 1000),
    originalValue,
    currentValue,
    valueChange,
    percentChange,
    alertsSet,
    trackedDays,
  };
};

// Helper function to calculate portfolio summary
const calculatePortfolioSummary = (properties: any[]) => {
  if (!properties || properties.length === 0) {
    return {
      totalValue: 0,
      totalChange: 0,
      totalChangePercent: 0,
      averageGrowth: 0,
      bestPerformer: null,
      worstPerformer: null,
      totalAlerts: 0,
    };
  }

  const propertiesWithMetrics = properties.map(property => ({
    ...property,
    metrics: generateTrackingMetrics(property)
  }));

  const totalValue = propertiesWithMetrics.reduce((sum, p) => sum + p.metrics.currentValue, 0);
  const totalOriginalValue = propertiesWithMetrics.reduce((sum, p) => sum + p.metrics.originalValue, 0);
  const totalChange = totalValue - totalOriginalValue;
  const totalChangePercent = totalOriginalValue > 0 ? (totalChange / totalOriginalValue) * 100 : 0;
  const averageGrowth = propertiesWithMetrics.reduce((sum, p) => sum + p.metrics.percentChange, 0) / propertiesWithMetrics.length;
  const totalAlerts = propertiesWithMetrics.reduce((sum, p) => sum + p.metrics.alertsSet, 0);

  const bestPerformer = propertiesWithMetrics.reduce((best, current) => 
    current.metrics.percentChange > best.metrics.percentChange ? current : best
  );

  const worstPerformer = propertiesWithMetrics.reduce((worst, current) => 
    current.metrics.percentChange < worst.metrics.percentChange ? current : worst
  );

  return {
    totalValue,
    totalChange,
    totalChangePercent,
    averageGrowth,
    bestPerformer,
    worstPerformer,
    totalAlerts,
    propertiesWithMetrics,
  };
};

function PortfolioPage() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch tracked properties
  const { data: trackedProperties, isLoading, error } = useQuery({
    queryKey: ['tracked-properties'],
    queryFn: () => apiClient.getTrackedProperties(),
  });

  // Remove property mutation
  const removePropertyMutation = useMutation({
    mutationFn: (propertyId: string) => apiClient.untrackProperty(propertyId),
    onSuccess: () => {
      toast.success('Property removed from portfolio');
      queryClient.invalidateQueries({ queryKey: ['tracked-properties'] });
    },
    onError: () => {
      toast.error('Failed to remove property');
    },
  });

  const handleRemoveProperty = (propertyId: string) => {
    if (window.confirm('Are you sure you want to remove this property from your portfolio?')) {
      removePropertyMutation.mutate(propertyId);
    }
  };

  // Calculate portfolio summary
  const portfolioSummary = calculatePortfolioSummary(trackedProperties || []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {[...Array(3)].map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Portfolio</h1>
              <p className="text-gray-600 mt-2">
                Track and analyze your property investments with real-time insights
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/properties" className="btn-outline">
                <Home className="w-4 h-4 mr-2" />
                Add Properties
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <p className="text-red-600">Failed to load portfolio. Please try again.</p>
            </div>
          </div>
        )}

        {/* Portfolio Summary - Only show if there are properties */}
        {!isLoading && trackedProperties && trackedProperties.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {/* Total Portfolio Value */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Portfolio Value</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {formatCurrency(portfolioSummary.totalValue)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <PieChart className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <TrendingUp className={`w-4 h-4 mr-1 ${
                  portfolioSummary.totalChangePercent >= 0 ? 'text-green-500' : 'text-red-500 rotate-180'
                }`} />
                <span className={`text-sm font-medium ${
                  portfolioSummary.totalChangePercent >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {portfolioSummary.totalChangePercent >= 0 ? '+' : ''}{portfolioSummary.totalChangePercent.toFixed(1)}%
                </span>
                <span className="text-sm text-gray-500 ml-2">
                  ({portfolioSummary.totalChange >= 0 ? '+' : ''}{formatCurrency(Math.abs(portfolioSummary.totalChange))})
                </span>
              </div>
            </div>

            {/* Average Growth */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Growth</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {portfolioSummary.averageGrowth >= 0 ? '+' : ''}{portfolioSummary.averageGrowth.toFixed(1)}%
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Across {trackedProperties.length} {trackedProperties.length === 1 ? 'property' : 'properties'}
                </p>
              </div>
            </div>

            {/* Active Alerts */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {portfolioSummary.totalAlerts}
                  </p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Bell className="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Monitoring market changes
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && trackedProperties && trackedProperties.length === 0 && (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Target className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Your portfolio is empty
            </h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Start building your property portfolio by adding properties to track.
              Monitor value changes, market trends, and receive personalized insights.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/properties" className="btn-primary">
                <Home className="w-4 h-4 mr-2" />
                Browse Properties
              </Link>
              <Link href="/suburbs" className="btn-outline">
                Explore Suburbs
              </Link>
            </div>
          </div>
        )}

        {/* Properties Grid */}
        {trackedProperties && trackedProperties.length > 0 && (
          <>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Your Properties ({trackedProperties.length})
              </h2>
              <div className="text-sm text-gray-500">
                Last updated: {new Date().toLocaleDateString()}
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {portfolioSummary.propertiesWithMetrics?.map((property: any) => (
                <div key={property.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                  {/* Property Image */}
                  <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                    {property.images?.[0] ? (
                      <img
                        src={property.images[0]}
                        alt={property.address}
                        className="w-full h-48 object-cover"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                        <MapPin className="w-12 h-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Property Details */}
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <Link href={`/properties/${property.id}`}>
                          <h3 className="text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors cursor-pointer truncate">
                            {property.address}
                          </h3>
                        </Link>
                        <p className="text-gray-600 text-sm">
                          {formatPropertyFeatures(property.bedrooms, property.bathrooms, property.carSpaces)}
                        </p>
                      </div>
                      <button
                        onClick={() => handleRemoveProperty(property.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors ml-2"
                        title="Remove from portfolio"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>

                    {/* Portfolio Metrics */}
                    <div className="space-y-3 mb-4">
                      {/* Current Value */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Current Value</span>
                        <div className="text-right">
                          <div className="font-semibold text-gray-900">
                            {formatCurrency(property.metrics.currentValue)}
                          </div>
                          <div className={`text-xs flex items-center ${
                            property.metrics.percentChange >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            <TrendingUp className={`w-3 h-3 mr-1 ${
                              property.metrics.percentChange < 0 ? 'rotate-180' : ''
                            }`} />
                            {property.metrics.percentChange >= 0 ? '+' : ''}{property.metrics.percentChange.toFixed(1)}%
                          </div>
                        </div>
                      </div>

                      {/* Value Change Amount */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Total Change</span>
                        <span className={`text-sm font-medium ${
                          property.metrics.valueChange >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {property.metrics.valueChange >= 0 ? '+' : ''}{formatCurrency(Math.abs(property.metrics.valueChange))}
                        </span>
                      </div>

                      {/* Tracking Duration */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          In Portfolio
                        </span>
                        <span className="text-sm text-gray-900">
                          {property.metrics.trackedDays} days
                        </span>
                      </div>

                      {/* Alerts Set */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 flex items-center">
                          <Bell className="w-3 h-3 mr-1" />
                          Active Alerts
                        </span>
                        <span className="text-sm text-gray-900">
                          {property.metrics.alertsSet}
                        </span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Link
                        href={`/properties/${property.id}`}
                        className="flex-1 btn-outline btn-sm text-center"
                      >
                        <Activity className="w-4 h-4 mr-1" />
                        View Analysis
                      </Link>
                      <button className="btn-primary btn-sm">
                        <Bell className="w-4 h-4 mr-1" />
                        Alerts
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default withAuth(PortfolioPage);
