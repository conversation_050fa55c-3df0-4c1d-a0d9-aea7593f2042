'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, TrendingUp, TrendingDown, Calendar, BarChart3, Filter } from 'lucide-react';
import TrendAnalyzer from '@/components/market/TrendAnalyzer';

interface TrendData {
  period: string;
  value: number;
  change: number;
  changePercent: number;
}

interface MarketTrend {
  suburb: string;
  state: string;
  currentValue: number;
  trends: TrendData[];
  summary: {
    currentValue: number;
    periodChange: number;
    periodChangePercent: number;
    volatility: number;
    trend: 'bullish' | 'bearish' | 'stable';
  };
  forecast: {
    period: string;
    predictedValue: number;
    confidence: number;
  }[];
}

export default function MarketTrendsPage() {
  const router = useRouter();
  const [selectedSuburb, setSelectedSuburb] = useState<string>('Melbourne CBD');
  const [selectedPeriod, setSelectedPeriod] = useState<'3m' | '6m' | '12m' | '24m'>('12m');
  const [selectedMetric, setSelectedMetric] = useState<'median_price' | 'sales_volume' | 'days_on_market'>('median_price');
  const [trendData, setTrendData] = useState<MarketTrend | null>(null);
  const [loading, setLoading] = useState(true);

  const suburbs = [
    'Melbourne CBD', 'Sydney CBD', 'Brisbane CBD', 'Adelaide CBD', 'Perth CBD',
    'Toorak', 'Double Bay', 'New Farm', 'Unley', 'Cottesloe',
    'South Yarra', 'Paddington', 'Fortitude Valley', 'North Adelaide', 'Fremantle'
  ];

  const metrics = [
    { id: 'median_price', label: 'Median Price', unit: '$' },
    { id: 'sales_volume', label: 'Sales Volume', unit: 'sales' },
    { id: 'days_on_market', label: 'Days on Market', unit: 'days' }
  ];

  useEffect(() => {
    loadTrendData();
  }, [selectedSuburb, selectedPeriod, selectedMetric]);

  const loadTrendData = async () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockTrendData = generateMockTrendData();
      setTrendData(mockTrendData);
      setLoading(false);
    }, 1000);
  };

  const generateMockTrendData = (): MarketTrend => {
    const periods = getPeriods();
    const baseValue = selectedMetric === 'median_price' ? 800000 : 
                     selectedMetric === 'sales_volume' ? 50 : 30;
    
    const trends: TrendData[] = periods.map((period, index) => {
      const variation = (Math.random() - 0.5) * 0.2; // ±10% variation
      const value = baseValue * (1 + variation + (index * 0.02)); // Slight upward trend
      const change = index > 0 ? value - trends[index - 1].value : 0;
      const changePercent = index > 0 ? (change / trends[index - 1].value) * 100 : 0;
      
      return {
        period,
        value,
        change,
        changePercent
      };
    });

    const currentValue = trends[trends.length - 1].value;
    const firstValue = trends[0].value;
    const periodChange = currentValue - firstValue;
    const periodChangePercent = (periodChange / firstValue) * 100;
    const volatility = Math.sqrt(trends.reduce((sum, t) => sum + Math.pow(t.changePercent, 2), 0) / trends.length);
    
    const trend: 'bullish' | 'bearish' | 'stable' = 
      periodChangePercent > 5 ? 'bullish' : 
      periodChangePercent < -5 ? 'bearish' : 'stable';

    const forecast = generateForecast(currentValue, trend);

    return {
      suburb: selectedSuburb,
      state: getStateForSuburb(selectedSuburb),
      currentValue,
      trends,
      summary: {
        currentValue,
        periodChange,
        periodChangePercent,
        volatility,
        trend
      },
      forecast
    };
  };

  const getPeriods = () => {
    const periods = [];
    const now = new Date();
    const monthsBack = selectedPeriod === '3m' ? 3 : selectedPeriod === '6m' ? 6 : selectedPeriod === '12m' ? 12 : 24;
    
    for (let i = monthsBack; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      periods.push(date.toLocaleDateString('en-AU', { month: 'short', year: 'numeric' }));
    }
    
    return periods;
  };

  const generateForecast = (currentValue: number, trend: string) => {
    const forecastPeriods = ['Next Month', '3 Months', '6 Months', '12 Months'];
    const trendMultiplier = trend === 'bullish' ? 1.02 : trend === 'bearish' ? 0.98 : 1.001;
    
    return forecastPeriods.map((period, index) => {
      const months = index + 1;
      const predictedValue = currentValue * Math.pow(trendMultiplier, months);
      const confidence = Math.max(60, 90 - (index * 8)); // Decreasing confidence over time
      
      return {
        period,
        predictedValue,
        confidence
      };
    });
  };

  const getStateForSuburb = (suburb: string) => {
    if (suburb.includes('Melbourne') || suburb.includes('Toorak') || suburb.includes('South Yarra')) return 'VIC';
    if (suburb.includes('Sydney') || suburb.includes('Double Bay') || suburb.includes('Paddington')) return 'NSW';
    if (suburb.includes('Brisbane') || suburb.includes('New Farm') || suburb.includes('Fortitude Valley')) return 'QLD';
    if (suburb.includes('Adelaide') || suburb.includes('Unley') || suburb.includes('North Adelaide')) return 'SA';
    if (suburb.includes('Perth') || suburb.includes('Cottesloe') || suburb.includes('Fremantle')) return 'WA';
    return 'VIC';
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === '$') {
      return new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    } else {
      return `${Math.round(value)} ${unit}`;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'bullish':
        return 'text-green-600 bg-green-50';
      case 'bearish':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'bullish':
        return <TrendingUp className="h-4 w-4" />;
      case 'bearish':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/market')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <TrendingUp className="h-7 w-7 text-green-600" />
                  Market Trends
                </h1>
                <p className="text-gray-600 mt-1">
                  Analyze market trends and forecast future performance
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Suburb Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Suburb</label>
              <select
                value={selectedSuburb}
                onChange={(e) => setSelectedSuburb(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {suburbs.map(suburb => (
                  <option key={suburb} value={suburb}>{suburb}</option>
                ))}
              </select>
            </div>

            {/* Period Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Time Period</label>
              <div className="flex bg-gray-100 rounded-lg p-1">
                {[
                  { id: '3m', label: '3M' },
                  { id: '6m', label: '6M' },
                  { id: '12m', label: '12M' },
                  { id: '24m', label: '24M' }
                ].map((period) => (
                  <button
                    key={period.id}
                    onClick={() => setSelectedPeriod(period.id as any)}
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded transition-colors ${
                      selectedPeriod === period.id
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {period.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Metric Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Metric</label>
              <select
                value={selectedMetric}
                onChange={(e) => setSelectedMetric(e.target.value as any)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {metrics.map(metric => (
                  <option key={metric.id} value={metric.id}>{metric.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Trend Summary */}
        {trendData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-sm text-gray-500">Current Value</div>
              <div className="text-2xl font-bold text-blue-600">
                {formatValue(trendData.summary.currentValue, metrics.find(m => m.id === selectedMetric)?.unit || '')}
              </div>
              <div className="text-sm text-gray-600">{selectedSuburb}</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-sm text-gray-500">Period Change</div>
              <div className={`text-2xl font-bold ${
                trendData.summary.periodChangePercent > 0 ? 'text-green-600' : 
                trendData.summary.periodChangePercent < 0 ? 'text-red-600' : 'text-gray-600'
              }`}>
                {trendData.summary.periodChangePercent > 0 ? '+' : ''}{trendData.summary.periodChangePercent.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">
                {formatValue(Math.abs(trendData.summary.periodChange), metrics.find(m => m.id === selectedMetric)?.unit || '')}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-sm text-gray-500">Volatility</div>
              <div className="text-2xl font-bold text-orange-600">
                {trendData.summary.volatility.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Price variation</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-sm text-gray-500">Market Trend</div>
              <div className={`text-2xl font-bold flex items-center gap-2 ${getTrendColor(trendData.summary.trend).split(' ')[0]}`}>
                {getTrendIcon(trendData.summary.trend)}
                <span className="capitalize">{trendData.summary.trend}</span>
              </div>
              <div className={`text-sm px-2 py-1 rounded-full inline-block mt-1 ${getTrendColor(trendData.summary.trend)}`}>
                {trendData.summary.trend === 'bullish' ? 'Strong growth' : 
                 trendData.summary.trend === 'bearish' ? 'Declining' : 'Stable market'}
              </div>
            </div>
          </div>
        )}

        {/* Trend Analysis */}
        {loading ? (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Trend Analysis</h3>
            <p className="text-gray-600">Analyzing market data and generating forecasts...</p>
          </div>
        ) : trendData ? (
          <TrendAnalyzer 
            data={trendData} 
            metric={metrics.find(m => m.id === selectedMetric)!}
            period={selectedPeriod}
          />
        ) : (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
            <p className="text-gray-600">Unable to load trend data for the selected criteria</p>
          </div>
        )}
      </div>
    </div>
  );
}
