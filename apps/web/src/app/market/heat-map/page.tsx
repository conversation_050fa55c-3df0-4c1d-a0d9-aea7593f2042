'use client';

import MarketHeatMap from '@/components/market/MarketHeatMap';
import { ArrowLeft, Download, Info, Layers, MapPin, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface HeatMapData {
  type: 'price_growth' | 'median_price' | 'sales_volume' | 'investment_score';
  data: {
    suburbId: string;
    name: string;
    state: string;
    latitude: number;
    longitude: number;
    value: number;
    color: string;
    intensity: number;
  }[];
  legend: {
    min: number;
    max: number;
    unit: string;
    colorScale: string[];
  };
  lastUpdated: Date;
}

export default function HeatMapPage() {
  const router = useRouter();
  const [selectedLayer, setSelectedLayer] = useState<'price_growth' | 'median_price' | 'sales_volume' | 'investment_score'>('price_growth');
  const [selectedState, setSelectedState] = useState<string>('all');
  const [heatMapData, setHeatMapData] = useState<HeatMapData | null>(null);
  const [loading, setLoading] = useState(true);

  const layers = [
    {
      id: 'price_growth',
      name: 'Price Growth',
      description: '12-month price growth percentage',
      color: 'green'
    },
    {
      id: 'median_price',
      name: 'Median Price',
      description: 'Current median property prices',
      color: 'blue'
    },
    {
      id: 'sales_volume',
      name: 'Sales Volume',
      description: 'Number of sales in last 3 months',
      color: 'purple'
    },
    {
      id: 'investment_score',
      name: 'Investment Score',
      description: 'AI-calculated investment potential',
      color: 'orange'
    }
  ];

  useEffect(() => {
    loadHeatMapData();
  }, [selectedLayer, selectedState]);

  const loadHeatMapData = async () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      // Generate mock heat map data
      const mockData: HeatMapData = {
        type: selectedLayer,
        data: generateMockHeatMapData(selectedLayer),
        legend: {
          min: selectedLayer === 'price_growth' ? -5 : selectedLayer === 'median_price' ? 400000 : selectedLayer === 'sales_volume' ? 0 : 50,
          max: selectedLayer === 'price_growth' ? 25 : selectedLayer === 'median_price' ? 2000000 : selectedLayer === 'sales_volume' ? 100 : 100,
          unit: selectedLayer === 'price_growth' ? '%' : selectedLayer === 'median_price' ? '$' : selectedLayer === 'sales_volume' ? 'sales' : 'score',
          colorScale: getColorScale(selectedLayer)
        },
        lastUpdated: new Date()
      };
      
      setHeatMapData(mockData);
      setLoading(false);
    }, 1000);
  };

  const generateMockHeatMapData = (layer: string) => {
    // Mock suburb data for major Australian cities
    const suburbs = [
      { name: 'Toorak', state: 'VIC', lat: -37.8467, lng: 145.0072 },
      { name: 'Double Bay', state: 'NSW', lat: -33.8773, lng: 151.2443 },
      { name: 'New Farm', state: 'QLD', lat: -27.4661, lng: 153.0515 },
      { name: 'Unley', state: 'SA', lat: -34.9495, lng: 138.6067 },
      { name: 'Cottesloe', state: 'WA', lat: -31.9959, lng: 115.7581 },
      { name: 'South Yarra', state: 'VIC', lat: -37.8386, lng: 144.9931 },
      { name: 'Paddington', state: 'NSW', lat: -33.8847, lng: 151.2303 },
      { name: 'Fortitude Valley', state: 'QLD', lat: -27.4558, lng: 153.0348 },
      { name: 'North Adelaide', state: 'SA', lat: -34.9048, lng: 138.5931 },
      { name: 'Fremantle', state: 'WA', lat: -32.0569, lng: 115.7439 }
    ];

    return suburbs.map((suburb, index) => {
      let value: number;
      let intensity: number;
      let color: string;

      switch (layer) {
        case 'price_growth':
          value = -5 + Math.random() * 30; // -5% to 25%
          intensity = (value + 5) / 30;
          color = `rgba(34, 197, 94, ${intensity})`;
          break;
        case 'median_price':
          value = 400000 + Math.random() * 1600000; // $400k to $2M
          intensity = (value - 400000) / 1600000;
          color = `rgba(59, 130, 246, ${intensity})`;
          break;
        case 'sales_volume':
          value = Math.random() * 100; // 0 to 100 sales
          intensity = value / 100;
          color = `rgba(147, 51, 234, ${intensity})`;
          break;
        case 'investment_score':
          value = 50 + Math.random() * 50; // 50-100 score
          intensity = (value - 50) / 50;
          color = `rgba(249, 115, 22, ${intensity})`;
          break;
        default:
          value = 0;
          intensity = 0;
          color = 'rgba(0, 0, 0, 0)';
      }

      return {
        suburbId: `suburb_${index}`,
        name: suburb.name,
        state: suburb.state,
        latitude: suburb.lat,
        longitude: suburb.lng,
        value,
        color,
        intensity
      };
    }).filter(suburb => selectedState === 'all' || suburb.state === selectedState);
  };

  const getColorScale = (layer: string) => {
    switch (layer) {
      case 'price_growth':
        return ['#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e'];
      case 'median_price':
        return ['#dbeafe', '#93c5fd', '#60a5fa', '#3b82f6', '#1d4ed8'];
      case 'sales_volume':
        return ['#f3e8ff', '#d8b4fe', '#c084fc', '#a855f7', '#9333ea'];
      case 'investment_score':
        return ['#fed7aa', '#fdba74', '#fb923c', '#f97316', '#ea580c'];
      default:
        return ['#f3f4f6', '#d1d5db', '#9ca3af', '#6b7280', '#374151'];
    }
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === '$') {
      return new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    } else if (unit === '%') {
      return `${value.toFixed(1)}%`;
    } else {
      return `${Math.round(value)}${unit}`;
    }
  };

  const exportData = () => {
    if (!heatMapData) return;
    
    const csvContent = [
      ['Suburb', 'State', 'Value', 'Unit'].join(','),
      ...heatMapData.data.map(item => 
        [item.name, item.state, item.value.toFixed(2), heatMapData.legend.unit].join(',')
      )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `market-heatmap-${selectedLayer}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/market')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <MapPin className="h-7 w-7 text-blue-600" />
                  Market Heat Maps
                </h1>
                <p className="text-gray-600 mt-1">
                  Interactive visualization of market data across Australia
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={exportData}
                disabled={!heatMapData}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2 disabled:opacity-50"
              >
                <Download className="h-4 w-4" />
                Export
              </button>
              <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Settings className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Layer Selection */}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-3">
                <Layers className="h-4 w-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Data Layer</span>
              </div>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                {layers.map((layer) => (
                  <button
                    key={layer.id}
                    onClick={() => setSelectedLayer(layer.id as any)}
                    className={`p-3 rounded-lg border-2 text-left transition-all ${
                      selectedLayer === layer.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900 mb-1">{layer.name}</div>
                    <div className="text-xs text-gray-600">{layer.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* State Filter */}
            <div>
              <div className="text-sm font-medium text-gray-700 mb-3">State Filter</div>
              <select
                value={selectedState}
                onChange={(e) => setSelectedState(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All States</option>
                <option value="NSW">NSW</option>
                <option value="VIC">VIC</option>
                <option value="QLD">QLD</option>
                <option value="SA">SA</option>
                <option value="WA">WA</option>
                <option value="TAS">TAS</option>
                <option value="NT">NT</option>
                <option value="ACT">ACT</option>
              </select>
            </div>
          </div>
        </div>

        {/* Heat Map */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                {layers.find(l => l.id === selectedLayer)?.name} Heat Map
              </h3>
              {heatMapData && (
                <div className="text-sm text-gray-600">
                  Last updated: {heatMapData.lastUpdated.toLocaleDateString('en-AU')}
                </div>
              )}
            </div>
          </div>
          
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading heat map data...</p>
                </div>
              </div>
            ) : heatMapData ? (
              <MarketHeatMap data={heatMapData} />
            ) : (
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
                  <p className="text-gray-600">Unable to load heat map data for the selected criteria</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Legend and Info */}
        {heatMapData && (
          <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Legend */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Legend</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Minimum</span>
                  <span className="font-medium">{formatValue(heatMapData.legend.min, heatMapData.legend.unit)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Maximum</span>
                  <span className="font-medium">{formatValue(heatMapData.legend.max, heatMapData.legend.unit)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Scale:</span>
                  <div className="flex gap-1">
                    {heatMapData.legend.colorScale.map((color, index) => (
                      <div
                        key={index}
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: color }}
                      ></div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Info */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Info className="h-5 w-5 text-blue-500" />
                Data Insights
              </h3>
              <div className="space-y-2 text-sm text-gray-700">
                <div>• Data points: {heatMapData.data.length} suburbs</div>
                <div>• Coverage: {selectedState === 'all' ? 'Australia-wide' : selectedState}</div>
                <div>• Update frequency: Daily</div>
                <div>• Data sources: Multiple listing services, government records</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
