'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { BarChart3, TrendingUp, MapPin, Zap, Filter, Calendar, ArrowUp, ArrowDown } from 'lucide-react';

interface MarketOverview {
  totalSuburbs: number;
  averageGrowth: number;
  hotMarkets: number;
  totalVolume: number;
}

interface MarketMover {
  id: string;
  name: string;
  state: string;
  growthPercent: number;
  medianPrice: number;
  salesVolume: number;
  trend: 'up' | 'down' | 'stable';
}

interface EmergingSuburb {
  id: string;
  name: string;
  state: string;
  medianPrice: number;
  growthRate: number;
  emergingScore: number;
  keyFactors: string[];
}

// Mock data
const mockMarketOverview: MarketOverview = {
  totalSuburbs: 1247,
  averageGrowth: 8.4,
  hotMarkets: 156,
  totalVolume: 2847000000
};

const mockMarketMovers: MarketMover[] = [
  {
    id: '1',
    name: 'Teneriffe',
    state: 'QLD',
    growthPercent: 18.5,
    medianPrice: 1250000,
    salesVolume: 47,
    trend: 'up'
  },
  {
    id: '2',
    name: 'Fitzroy North',
    state: 'VIC',
    growthPercent: 16.2,
    medianPrice: 1450000,
    salesVolume: 32,
    trend: 'up'
  },
  {
    id: '3',
    name: 'Newtown',
    state: 'NSW',
    growthPercent: 14.8,
    medianPrice: 1680000,
    salesVolume: 28,
    trend: 'up'
  },
  {
    id: '4',
    name: 'West End',
    state: 'QLD',
    growthPercent: -5.2,
    medianPrice: 890000,
    salesVolume: 15,
    trend: 'down'
  }
];

const mockEmergingSuburbs: EmergingSuburb[] = [
  {
    id: '1',
    name: 'Woolloongabba',
    state: 'QLD',
    medianPrice: 750000,
    growthRate: 12.3,
    emergingScore: 92,
    keyFactors: ['Cross River Rail', 'Urban renewal', 'Proximity to CBD']
  },
  {
    id: '2',
    name: 'Footscray',
    state: 'VIC',
    medianPrice: 680000,
    growthRate: 11.8,
    emergingScore: 88,
    keyFactors: ['Transport upgrades', 'Gentrification', 'Affordable entry point']
  },
  {
    id: '3',
    name: 'Redfern',
    state: 'NSW',
    medianPrice: 920000,
    growthRate: 10.5,
    emergingScore: 85,
    keyFactors: ['Tech hub development', 'University proximity', 'Infrastructure investment']
  }
];

export default function MarketIntelligencePage() {
  const router = useRouter();
  const [overview, setOverview] = useState<MarketOverview | null>(null);
  const [marketMovers, setMarketMovers] = useState<MarketMover[]>([]);
  const [emergingSuburbs, setEmergingSuburbs] = useState<EmergingSuburb[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1m' | '3m' | '6m' | '12m'>('3m');
  const [selectedState, setSelectedState] = useState<string>('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setOverview(mockMarketOverview);
      setMarketMovers(mockMarketMovers);
      setEmergingSuburbs(mockEmergingSuburbs);
      setLoading(false);
    }, 1000);
  }, [selectedTimeframe, selectedState]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatLargeNumber = (num: number) => {
    if (num >= 1000000000) {
      return `$${(num / 1000000000).toFixed(1)}B`;
    } else if (num >= 1000000) {
      return `$${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `$${(num / 1000).toFixed(1)}K`;
    }
    return `$${num}`;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <ArrowDown className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded-full"></div>;
    }
  };

  const getTrendColor = (growthPercent: number) => {
    if (growthPercent > 0) return 'text-green-600';
    if (growthPercent < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading market intelligence...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                Market Intelligence
              </h1>
              <p className="text-gray-600 mt-2">
                Real-time market insights, trends, and emerging opportunities across Australia
              </p>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/market/heat-map')}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <MapPin className="h-5 w-5" />
                Heat Maps
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Timeframe:</span>
                <div className="flex bg-gray-100 rounded-lg p-1">
                  {[
                    { id: '1m', label: '1M' },
                    { id: '3m', label: '3M' },
                    { id: '6m', label: '6M' },
                    { id: '12m', label: '12M' }
                  ].map((timeframe) => (
                    <button
                      key={timeframe.id}
                      onClick={() => setSelectedTimeframe(timeframe.id as any)}
                      className={`px-3 py-1 text-sm font-medium rounded transition-colors ${
                        selectedTimeframe === timeframe.id
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      {timeframe.label}
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <select
                  value={selectedState}
                  onChange={(e) => setSelectedState(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All States</option>
                  <option value="NSW">NSW</option>
                  <option value="VIC">VIC</option>
                  <option value="QLD">QLD</option>
                  <option value="SA">SA</option>
                  <option value="WA">WA</option>
                </select>
              </div>
            </div>
            
            <div className="text-sm text-gray-600">
              Last updated: {new Date().toLocaleDateString('en-AU')}
            </div>
          </div>
        </div>

        {/* Market Overview */}
        {overview && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center gap-2 mb-2">
                <MapPin className="h-5 w-5 text-blue-500" />
                <h3 className="font-medium text-gray-900">Total Suburbs</h3>
              </div>
              <div className="text-2xl font-bold text-blue-600">{overview.totalSuburbs.toLocaleString()}</div>
              <div className="text-sm text-gray-500">Tracked markets</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-5 w-5 text-green-500" />
                <h3 className="font-medium text-gray-900">Average Growth</h3>
              </div>
              <div className="text-2xl font-bold text-green-600">{overview.averageGrowth}%</div>
              <div className="text-sm text-gray-500">Last 12 months</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-gray-900">Hot Markets</h3>
              </div>
              <div className="text-2xl font-bold text-orange-600">{overview.hotMarkets}</div>
              <div className="text-sm text-gray-500">Above 10% growth</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="h-5 w-5 text-purple-500" />
                <h3 className="font-medium text-gray-900">Sales Volume</h3>
              </div>
              <div className="text-2xl font-bold text-purple-600">{formatLargeNumber(overview.totalVolume)}</div>
              <div className="text-sm text-gray-500">Total market value</div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <button
            onClick={() => router.push('/market/heat-map')}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow text-left"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <MapPin className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="font-medium text-gray-900">Heat Maps</h3>
            </div>
            <p className="text-sm text-gray-600">Interactive market visualization</p>
          </button>

          <button
            onClick={() => router.push('/market/trends')}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow text-left"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <h3 className="font-medium text-gray-900">Trends</h3>
            </div>
            <p className="text-sm text-gray-600">Market trend analysis</p>
          </button>

          <button
            onClick={() => router.push('/market/emerging')}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow text-left"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Zap className="h-5 w-5 text-orange-600" />
              </div>
              <h3 className="font-medium text-gray-900">Emerging</h3>
            </div>
            <p className="text-sm text-gray-600">Emerging opportunities</p>
          </button>

          <button
            onClick={() => router.push('/market/movers')}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow text-left"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-5 w-5 text-purple-600" />
              </div>
              <h3 className="font-medium text-gray-900">Movers</h3>
            </div>
            <p className="text-sm text-gray-600">Top performing suburbs</p>
          </button>
        </div>

        {/* Market Movers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-500" />
                Top Market Movers
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {marketMovers.slice(0, 5).map((mover) => (
                  <div key={mover.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      {getTrendIcon(mover.trend)}
                      <div>
                        <h4 className="font-medium text-gray-900">{mover.name}</h4>
                        <div className="text-sm text-gray-600">{mover.state} • {mover.salesVolume} sales</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${getTrendColor(mover.growthPercent)}`}>
                        {mover.growthPercent > 0 ? '+' : ''}{mover.growthPercent}%
                      </div>
                      <div className="text-sm text-gray-600">{formatCurrency(mover.medianPrice)}</div>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={() => router.push('/market/movers')}
                className="w-full mt-4 text-blue-600 hover:text-blue-700 font-medium text-sm"
              >
                View All Market Movers →
              </button>
            </div>
          </div>

          {/* Emerging Suburbs */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Zap className="h-5 w-5 text-orange-500" />
                Emerging Opportunities
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {emergingSuburbs.slice(0, 3).map((suburb) => (
                  <div key={suburb.id} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{suburb.name}</h4>
                      <div className="text-right">
                        <div className="text-sm text-gray-500">Score</div>
                        <div className="text-lg font-bold text-orange-600">{suburb.emergingScore}</div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">{suburb.state}</span>
                      <span className="text-sm font-medium text-green-600">+{suburb.growthRate}%</span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 mb-1">{formatCurrency(suburb.medianPrice)}</div>
                    <div className="flex flex-wrap gap-1">
                      {suburb.keyFactors.slice(0, 2).map((factor, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                          {factor}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={() => router.push('/market/emerging')}
                className="w-full mt-4 text-blue-600 hover:text-blue-700 font-medium text-sm"
              >
                View All Emerging Suburbs →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
