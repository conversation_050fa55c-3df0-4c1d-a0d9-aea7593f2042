require('dotenv').config({ path: '../../.env' });

const { spawn } = require('child_process');
const http = require('http');

console.log('🧪 Testing Login Functionality');
console.log('==============================');

// Function to test login endpoint
async function testLogin() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: 'demo123456'
    });

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Response Status: ${res.statusCode}`);
        console.log(`📋 Response Headers:`, res.headers);
        console.log(`📄 Response Body:`, data);
        
        if (res.statusCode === 200) {
          try {
            const response = JSON.parse(data);
            if (response.accessToken) {
              console.log('✅ Login successful! Token received.');
              console.log('🔑 Access Token:', response.accessToken.substring(0, 20) + '...');
              resolve(response);
            } else {
              console.log('❌ Login failed: No access token in response');
              reject(new Error('No access token received'));
            }
          } catch (error) {
            console.log('❌ Login failed: Invalid JSON response');
            reject(error);
          }
        } else {
          console.log('❌ Login failed with status:', res.statusCode);
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Request failed:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// Function to check if server is running
function checkServer() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/api/v1',
      method: 'GET'
    }, (res) => {
      resolve(true);
    });

    req.on('error', () => {
      resolve(false);
    });

    req.end();
  });
}

// Main test function
async function runTest() {
  console.log('🔍 Checking if server is running...');
  
  const isRunning = await checkServer();
  
  if (!isRunning) {
    console.log('⚠️  Server not running. Starting server...');
    
    // Start the server
    const server = spawn('node', ['dist/main.js'], {
      stdio: 'pipe',
      env: { ...process.env }
    });

    let serverStarted = false;

    server.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('SERVER:', output.trim());
      
      if (output.includes('API is running on')) {
        serverStarted = true;
        console.log('✅ Server started successfully');
        
        // Wait a moment then test login
        setTimeout(async () => {
          try {
            await testLogin();
            console.log('🎉 Login test completed successfully!');
          } catch (error) {
            console.error('❌ Login test failed:', error.message);
          } finally {
            server.kill();
            process.exit(0);
          }
        }, 1000);
      }
    });

    server.stderr.on('data', (data) => {
      console.error('SERVER ERROR:', data.toString().trim());
    });

    server.on('close', (code) => {
      if (!serverStarted) {
        console.error('❌ Server failed to start');
        process.exit(1);
      }
    });

    // Timeout after 15 seconds
    setTimeout(() => {
      if (!serverStarted) {
        console.error('❌ Server startup timeout');
        server.kill();
        process.exit(1);
      }
    }, 15000);

  } else {
    console.log('✅ Server is already running');
    
    try {
      await testLogin();
      console.log('🎉 Login test completed successfully!');
    } catch (error) {
      console.error('❌ Login test failed:', error.message);
    }
  }
}

runTest();
