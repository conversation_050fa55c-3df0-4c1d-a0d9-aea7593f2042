# @revalu/api

NestJS API for the Revalu property intelligence platform.

## 🎉 Setup Complete!

The API is now running with:
- ✅ CORS Configuration
- ✅ Swagger Documentation at http://localhost:3001/api/docs
- ✅ Global Validation
- ✅ Database Integration via @revalu/database
- ✅ Environment Configuration
- ✅ Security Middleware
- ✅ Health Checks & Metrics

## Quick Commands

```bash
pnpm dev              # Start development server
pnpm build            # Build application
pnpm test             # Run tests
```

## Endpoints

- GET /api/v1 - Health check
- GET /api/v1/health - Detailed health
- GET /api/v1/metrics - API metrics
- GET /api/docs - Swagger documentation

