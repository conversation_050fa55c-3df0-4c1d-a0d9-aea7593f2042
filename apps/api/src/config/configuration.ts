export default () => ({
  // Environment
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // API Configuration
  api: {
    port: parseInt(process.env.API_PORT, 10) || 3001,
    host: process.env.API_HOST || 'localhost',
    baseUrl: process.env.API_BASE_URL || 'http://localhost:3001',
  },

  // Web App Configuration
  web: {
    port: parseInt(process.env.WEB_PORT, 10) || 3000,
    host: process.env.WEB_HOST || 'localhost',
    baseUrl: process.env.WEB_BASE_URL || 'http://localhost:3000',
  },

  // Database Configuration
  database: {
    url: process.env.DATABASE_URL,
  },

  // Supabase Configuration
  supabase: {
    url: process.env.SUPABASE_URL,
    anonKey: process.env.SUPABASE_ANON_KEY,
    serviceKey: process.env.SUPABASE_SERVICE_KEY,
  },

  // Redis Configuration
  redis: {
    url: process.env.UPSTASH_REDIS_URL,
    token: process.env.UPSTASH_REDIS_TOKEN,
  },

  // Authentication & Security
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
    sessionSecret: process.env.SESSION_SECRET || 'your-session-secret',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
  },

  // External APIs
  external: {
    openaiApiKey: process.env.OPENAI_API_KEY,
    googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY,
    firecrawlApiKey: process.env.FIRECRAWL_API_KEY,
  },

  // Email Configuration
  email: {
    smtpHost: process.env.SMTP_HOST,
    smtpPort: parseInt(process.env.SMTP_PORT, 10) || 587,
    smtpUser: process.env.SMTP_USER,
    smtpPassword: process.env.SMTP_PASSWORD,
    fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
  },

  // File Storage
  storage: {
    uploadDir: process.env.UPLOAD_DIR || './uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 10485760, // 10MB
    allowedFileTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || ['jpg', 'jpeg', 'png', 'pdf'],
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
  },

  // ML Predictor Service
  mlService: {
    url: process.env.ML_SERVICE_URL || 'http://localhost:8000',
    modelPath: process.env.ML_MODEL_PATH || './models',
    confidenceThreshold: parseFloat(process.env.PREDICTION_CONFIDENCE_THRESHOLD) || 0.8,
  },

  // Monitoring & Analytics
  monitoring: {
    sentryDsn: process.env.SENTRY_DSN,
    googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID,
  },

  // AWS Configuration
  aws: {
    region: process.env.AWS_REGION || 'us-east-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    s3BucketName: process.env.S3_BUCKET_NAME,
  },

  // Rate Limiting
  throttle: {
    ttl: 60, // Time to live in seconds
    limit: 100, // Maximum number of requests within TTL
  },

  // Cache Configuration
  cache: {
    ttl: 300, // 5 minutes default TTL
    max: 1000, // Maximum number of items in cache
  },
});
