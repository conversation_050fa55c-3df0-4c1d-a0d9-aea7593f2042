import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from './common/database/prisma.service';

@Injectable()
export class AppService {
  constructor(
    private configService: ConfigService,
    private prismaService: PrismaService,
  ) {}

  getHealth() {
    return {
      success: true,
      message: 'Revalu API is running',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      environment: this.configService.get('NODE_ENV', 'development'),
    };
  }

  async getDetailedHealth() {
    const startTime = Date.now();

    // Check database health
    const isDatabaseHealthy = await this.prismaService.isHealthy();

    // Get memory usage
    const memoryUsage = process.memoryUsage();

    const healthData = {
      api: 'healthy',
      database: isDatabaseHealthy ? 'healthy' : 'unhealthy',
      uptime: process.uptime(),
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
      },
      responseTime: Date.now() - startTime,
      nodeVersion: process.version,
      platform: process.platform,
    };

    return {
      success: isDatabaseHealthy,
      data: healthData,
      timestamp: new Date().toISOString(),
    };
  }

  async getMetrics() {
    try {
      const dbMetrics = await this.prismaService.getMetrics();

      return {
        success: true,
        data: {
          database: dbMetrics,
          api: {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            cpu: process.cpuUsage(),
            version: process.version,
          },
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: {
          message: 'Failed to retrieve metrics',
          details: error.message,
        },
        timestamp: new Date().toISOString(),
      };
    }
  }
}
