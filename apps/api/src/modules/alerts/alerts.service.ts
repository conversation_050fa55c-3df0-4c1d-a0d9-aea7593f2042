import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common/database/prisma.service';
import {
    AlertHistoryDto,
    AlertHistoryResponseDto,
    AlertResponseDto,
    AlertSummaryDto,
    AlertType,
    CreateAlertDto,
    TriggerAlertDto,
    UpdateAlertDto
} from './dto/alerts.dto';

@Injectable()
export class AlertsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(userId: string, dto: CreateAlertDto): Promise<AlertResponseDto> {
    // Validate property or suburb exists
    if (dto.propertyId) {
      const property = await this.prisma.client.property.findUnique({
        where: { id: dto.propertyId },
      });
      if (!property) {
        throw new BadRequestException('Property not found');
      }
    }

    if (dto.suburbId) {
      const suburb = await this.prisma.client.suburb.findUnique({
        where: { id: dto.suburbId },
      });
      if (!suburb) {
        throw new BadRequestException('Suburb not found');
      }
    }

    const alert = await this.prisma.client.alert.create({
      data: {
        userId,
        name: dto.name,
        type: dto.type,
        propertyId: dto.propertyId,
        suburbId: dto.suburbId,
        conditions: dto.conditions,
        isActive: dto.isActive ?? true,
      },
      include: {
        property: {
          include: {
            suburb: true,
          },
        },
        suburb: true,
        history: {
          orderBy: { triggeredAt: 'desc' },
          take: 1,
        },
      },
    });

    return this.formatAlertResponse(alert);
  }

  async findAllByUser(userId: string): Promise<AlertResponseDto[]> {
    const alerts = await this.prisma.client.alert.findMany({
      where: { userId },
      include: {
        property: {
          include: {
            suburb: true,
          },
        },
        suburb: true,
        history: {
          orderBy: { triggeredAt: 'desc' },
          take: 1,
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return alerts.map(alert => this.formatAlertResponse(alert));
  }

  async findOne(id: string, userId: string): Promise<AlertResponseDto> {
    const alert = await this.prisma.client.alert.findFirst({
      where: { id, userId },
      include: {
        property: {
          include: {
            suburb: true,
          },
        },
        suburb: true,
        history: {
          orderBy: { triggeredAt: 'desc' },
          take: 1,
        },
      },
    });

    if (!alert) {
      throw new NotFoundException('Alert not found');
    }

    return this.formatAlertResponse(alert);
  }

  async update(id: string, userId: string, dto: UpdateAlertDto): Promise<AlertResponseDto> {
    const existingAlert = await this.prisma.client.alert.findFirst({
      where: { id, userId },
    });

    if (!existingAlert) {
      throw new NotFoundException('Alert not found');
    }

    const alert = await this.prisma.client.alert.update({
      where: { id },
      data: {
        ...dto,
        updatedAt: new Date(),
      },
      include: {
        property: {
          include: {
            suburb: true,
          },
        },
        suburb: true,
        history: {
          orderBy: { triggeredAt: 'desc' },
          take: 1,
        },
      },
    });

    return this.formatAlertResponse(alert);
  }

  async delete(id: string, userId: string): Promise<void> {
    const alert = await this.prisma.client.alert.findFirst({
      where: { id, userId },
    });

    if (!alert) {
      throw new NotFoundException('Alert not found');
    }

    await this.prisma.client.alert.delete({
      where: { id },
    });
  }

  async getHistory(dto: AlertHistoryDto): Promise<AlertHistoryResponseDto> {
    const alert = await this.prisma.client.alert.findUnique({
      where: { id: dto.alertId },
    });

    if (!alert) {
      throw new NotFoundException('Alert not found');
    }

    const history = await this.prisma.client.alertHistory.findMany({
      where: { alertId: dto.alertId },
      orderBy: { triggeredAt: 'desc' },
      take: dto.limit || 50,
    });

    const totalTriggers = await this.prisma.client.alertHistory.count({
      where: { alertId: dto.alertId },
    });

    return {
      history: history.map(h => ({
        id: h.id,
        triggeredAt: h.triggeredAt,
        data: h.data,
        notified: h.notified,
        notifiedAt: h.notifiedAt,
      })),
      totalTriggers,
      alert: {
        id: alert.id,
        name: alert.name,
        type: alert.type as AlertType,
        isActive: alert.isActive,
      },
    };
  }

  async trigger(dto: TriggerAlertDto): Promise<void> {
    const alert = await this.prisma.client.alert.findUnique({
      where: { id: dto.alertId },
    });

    if (!alert) {
      throw new NotFoundException('Alert not found');
    }

    if (!alert.isActive) {
      throw new BadRequestException('Alert is not active');
    }

    // Create history record
    await this.prisma.client.alertHistory.create({
      data: {
        alertId: dto.alertId,
        data: dto.triggerData,
        notified: dto.sendNotification ?? true,
        notifiedAt: dto.sendNotification ? new Date() : null,
      },
    });

    // Update alert last triggered
    await this.prisma.client.alert.update({
      where: { id: dto.alertId },
      data: {
        lastTriggered: new Date(),
      },
    });

    // TODO: Send notification (email, SMS, push notification)
    if (dto.sendNotification) {
      await this.sendNotification(alert, dto.triggerData);
    }
  }

  async getSummary(userId: string): Promise<AlertSummaryDto> {
    const alerts = await this.prisma.client.alert.findMany({
      where: { userId },
      include: {
        history: {
          orderBy: { triggeredAt: 'desc' },
        },
      },
    });

    const totalActive = alerts.filter(a => a.isActive).length;
    
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentTriggers = alerts.reduce((count, alert) => {
      return count + alert.history.filter(h => h.triggeredAt > last24Hours).length;
    }, 0);

    // Calculate most triggered type
    const typeCount: Record<string, number> = {};
    alerts.forEach(alert => {
      typeCount[alert.type] = (typeCount[alert.type] || 0) + alert.history.length;
    });
    const mostTriggeredType = Object.keys(typeCount).length > 0
      ? Object.keys(typeCount).reduce((a, b) =>
          typeCount[a] > typeCount[b] ? a : b
        ) as AlertType
      : AlertType.VALUE_CHANGE;

    // Recent activity
    const allHistory = alerts.flatMap(alert => 
      alert.history.map(h => ({
        ...h,
        alertName: alert.name,
        alertType: alert.type as AlertType,
      }))
    );
    
    const recentActivity = allHistory
      .sort((a, b) => b.triggeredAt.getTime() - a.triggeredAt.getTime())
      .slice(0, 10)
      .map(h => ({
        alertId: h.alertId,
        alertName: h.alertName,
        type: h.alertType,
        triggeredAt: h.triggeredAt,
        summary: this.generateTriggerSummary(h.alertType, h.data),
      }));

    // Performance by type
    const performanceByType = Object.keys(AlertType).map(type => {
      const alertsOfType = alerts.filter(a => a.type === type);
      const totalTriggers = alertsOfType.reduce((sum, a) => sum + a.history.length, 0);
      const lastTriggered = alertsOfType
        .flatMap(a => a.history)
        .sort((a, b) => b.triggeredAt.getTime() - a.triggeredAt.getTime())[0]?.triggeredAt;

      return {
        type: type as AlertType,
        count: alertsOfType.length,
        avgTriggersPerAlert: alertsOfType.length > 0 ? totalTriggers / alertsOfType.length : 0,
        lastTriggered,
      };
    }).filter(p => p.count > 0);

    return {
      totalActive,
      recentTriggers,
      mostTriggeredType,
      recentActivity,
      performanceByType,
    };
  }

  private formatAlertResponse(alert: any): AlertResponseDto {
    return {
      id: alert.id,
      name: alert.name,
      type: alert.type,
      propertyId: alert.propertyId,
      suburbId: alert.suburbId,
      conditions: alert.conditions,
      isActive: alert.isActive,
      lastTriggered: alert.lastTriggered,
      triggerCount: alert.history?.length || 0,
      createdAt: alert.createdAt,
      property: alert.property ? {
        id: alert.property.id,
        address: alert.property.address,
        suburb: alert.property.suburb.name,
        currentValue: alert.property.lastSalePrice,
      } : undefined,
      suburb: alert.suburb ? {
        id: alert.suburb.id,
        name: alert.suburb.name,
        state: alert.suburb.state,
        medianPrice: alert.suburb.medianPrice,
      } : undefined,
    };
  }

  private async sendNotification(alert: any, triggerData: any): Promise<void> {
    // TODO: Implement notification sending
    // This could include email, SMS, push notifications, etc.
    console.log(`Sending notification for alert ${alert.id}:`, triggerData);
  }

  private generateTriggerSummary(type: AlertType, data: any): string {
    switch (type) {
      case AlertType.VALUE_CHANGE:
        return `Property value changed by ${data.changePercent}%`;
      case AlertType.PRICE_DROP:
        return `Price dropped by $${data.priceChange?.toLocaleString()}`;
      case AlertType.NEW_LISTING:
        return `New property listed matching criteria`;
      case AlertType.DEVELOPMENT_APPLICATION:
        return `New development application submitted`;
      case AlertType.INFRASTRUCTURE_UPDATE:
        return `Infrastructure project update: ${data.projectName}`;
      case AlertType.SCHOOL_RATING_CHANGE:
        return `School rating changed from ${data.oldRating} to ${data.newRating}`;
      case AlertType.MARKET_TREND:
        return `Market trend alert: ${data.trendDirection} movement`;
      case AlertType.RISK_ALERT:
        return `Risk alert: ${data.riskType}`;
      default:
        return 'Alert triggered';
    }
  }
}
