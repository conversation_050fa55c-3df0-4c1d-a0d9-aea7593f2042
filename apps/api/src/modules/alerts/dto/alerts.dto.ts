import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsEnum, IsObject, IsN<PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';

export enum AlertType {
  VALUE_CHANGE = 'VALUE_CHANGE',
  NEW_LISTING = 'NEW_LISTING',
  PRICE_DROP = 'PRICE_DROP',
  DEVELOPMENT_APPLICATION = 'DEVELOPMENT_APPLICATION',
  INFRASTRUCTURE_UPDATE = 'INFRASTRUCTURE_UPDATE',
  SCHOOL_RATING_CHANGE = 'SCHOOL_RATING_CHANGE',
  MARKET_TREND = 'MARKET_TREND',
  RISK_ALERT = 'RISK_ALERT',
  CUSTOM = 'CUSTOM',
}

export class CreateAlertDto {
  @ApiProperty({ description: 'Alert name' })
  @IsString()
  name: string;

  @ApiProperty({ enum: AlertType, description: 'Type of alert' })
  @IsEnum(AlertType)
  type: AlertType;

  @ApiPropertyOptional({ description: 'Property ID to monitor' })
  @IsOptional()
  @IsString()
  propertyId?: string;

  @ApiPropertyOptional({ description: 'Suburb ID to monitor' })
  @IsOptional()
  @IsString()
  suburbId?: string;

  @ApiProperty({ description: 'Alert conditions and thresholds' })
  @IsObject()
  conditions: {
    // Value change conditions
    valueChangePercent?: number;
    valueChangeAmount?: number;
    
    // Price conditions
    maxPrice?: number;
    minPrice?: number;
    priceDropPercent?: number;
    
    // Market conditions
    marketTrendDirection?: 'up' | 'down' | 'stable';
    marketChangePercent?: number;
    
    // Development conditions
    developmentRadius?: number; // km
    developmentTypes?: string[];
    
    // School conditions
    schoolRatingChange?: number;
    
    // Custom conditions
    customCriteria?: any;
  };

  @ApiPropertyOptional({ description: 'Whether alert is active', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateAlertDto {
  @ApiPropertyOptional({ description: 'Updated alert name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Updated conditions' })
  @IsOptional()
  @IsObject()
  conditions?: any;

  @ApiPropertyOptional({ description: 'Whether alert is active' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class AlertResponseDto {
  @ApiProperty({ description: 'Alert ID' })
  id: string;

  @ApiProperty({ description: 'Alert name' })
  name: string;

  @ApiProperty({ enum: AlertType, description: 'Alert type' })
  type: AlertType;

  @ApiProperty({ description: 'Property ID being monitored' })
  propertyId?: string;

  @ApiProperty({ description: 'Suburb ID being monitored' })
  suburbId?: string;

  @ApiProperty({ description: 'Alert conditions' })
  conditions: any;

  @ApiProperty({ description: 'Whether alert is active' })
  isActive: boolean;

  @ApiProperty({ description: 'Last time alert was triggered' })
  lastTriggered?: Date;

  @ApiProperty({ description: 'Number of times triggered' })
  triggerCount: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Property details if applicable' })
  property?: {
    id: string;
    address: string;
    suburb: string;
    currentValue?: number;
  };

  @ApiProperty({ description: 'Suburb details if applicable' })
  suburb?: {
    id: string;
    name: string;
    state: string;
    medianPrice?: number;
  };
}

export class AlertHistoryDto {
  @ApiProperty({ description: 'Alert ID' })
  @IsString()
  alertId: string;

  @ApiPropertyOptional({ description: 'Number of history records to return', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}

export class AlertHistoryResponseDto {
  @ApiProperty({ description: 'Alert history records' })
  history: Array<{
    id: string;
    triggeredAt: Date;
    data: any;
    notified: boolean;
    notifiedAt?: Date;
  }>;

  @ApiProperty({ description: 'Total trigger count' })
  totalTriggers: number;

  @ApiProperty({ description: 'Alert details' })
  alert: {
    id: string;
    name: string;
    type: AlertType;
    isActive: boolean;
  };
}

export class TriggerAlertDto {
  @ApiProperty({ description: 'Alert ID to trigger' })
  @IsString()
  alertId: string;

  @ApiProperty({ description: 'Data that triggered the alert' })
  @IsObject()
  triggerData: any;

  @ApiPropertyOptional({ description: 'Whether to send notification', default: true })
  @IsOptional()
  @IsBoolean()
  sendNotification?: boolean;
}

export class AlertSummaryDto {
  @ApiProperty({ description: 'Total active alerts' })
  totalActive: number;

  @ApiProperty({ description: 'Alerts triggered in last 24 hours' })
  recentTriggers: number;

  @ApiProperty({ description: 'Most triggered alert type' })
  mostTriggeredType: AlertType;

  @ApiProperty({ description: 'Recent alert activity' })
  recentActivity: Array<{
    alertId: string;
    alertName: string;
    type: AlertType;
    triggeredAt: Date;
    summary: string;
  }>;

  @ApiProperty({ description: 'Alert performance by type' })
  performanceByType: Array<{
    type: AlertType;
    count: number;
    avgTriggersPerAlert: number;
    lastTriggered?: Date;
  }>;
}
