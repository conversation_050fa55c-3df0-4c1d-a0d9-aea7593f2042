import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Post,
  Body,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { SuburbsService } from './suburbs.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Public } from '../auth/decorators/public.decorator';
import {
  SuburbSearchDto,
  SuburbResponseDto,
  SuburbStatsDto,
  MarketTrendsDto,
  SuburbComparisonDto,
  TopSuburbsDto,
} from './dto/suburb.dto';
import {
  ApiSuccessResponse,
  ApiPaginatedResponse,
  ApiErrorResponse,
} from '../../common/decorators/api-response.decorator';
import { PaginatedResponseDto } from '../../common/dto/pagination.dto';

@ApiTags('suburbs')
@Controller('suburbs')
export class SuburbsController {
  constructor(private readonly suburbsService: SuburbsService) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Search and filter suburbs' })
  @ApiPaginatedResponse(SuburbResponseDto, 'Suburbs retrieved successfully')
  @ApiErrorResponse(400, 'Bad Request - Invalid search parameters')
  async findAll(
    @Query() searchDto: SuburbSearchDto,
  ): Promise<PaginatedResponseDto<SuburbResponseDto>> {
    return this.suburbsService.findAll(searchDto);
  }

  @Get('top')
  @Public()
  @ApiOperation({ summary: 'Get top suburbs by criteria' })
  @ApiQuery({
    name: 'criteria',
    enum: ['price', 'growth', 'amenities', 'safety', 'overall'],
    description: 'Ranking criteria',
    example: 'overall',
  })
  @ApiQuery({
    name: 'limit',
    type: Number,
    description: 'Number of results to return',
    example: 10,
    required: false,
  })
  @ApiSuccessResponse(TopSuburbsDto, 'Top suburbs retrieved successfully')
  @ApiErrorResponse(400, 'Bad Request - Invalid criteria or limit')
  async getTopSuburbs(
    @Query('criteria') criteria: 'price' | 'growth' | 'amenities' | 'safety' | 'overall' = 'overall',
    @Query('limit') limit: number = 10,
  ): Promise<TopSuburbsDto> {
    return this.suburbsService.getTopSuburbs(criteria, limit);
  }

  @Post('compare')
  @Public()
  @ApiOperation({ summary: 'Compare multiple suburbs' })
  @ApiResponse({
    status: 200,
    description: 'Suburb comparison completed successfully',
    type: SuburbComparisonDto,
  })
  @ApiErrorResponse(400, 'Bad Request - Invalid suburb IDs or comparison parameters')
  async compareSuburbs(
    @Body() body: { suburbIds: string[] },
  ): Promise<SuburbComparisonDto> {
    return this.suburbsService.compareSuburbs(body.suburbIds);
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get suburb by ID' })
  @ApiParam({ name: 'id', description: 'Suburb ID', example: 'clp1234567890' })
  @ApiSuccessResponse(SuburbResponseDto, 'Suburb retrieved successfully')
  @ApiErrorResponse(404, 'Not Found - Suburb not found')
  async findOne(@Param('id') id: string): Promise<SuburbResponseDto> {
    return this.suburbsService.findOne(id);
  }

  @Get(':id/statistics')
  @Public()
  @ApiOperation({ summary: 'Get detailed suburb statistics' })
  @ApiParam({ name: 'id', description: 'Suburb ID', example: 'clp1234567890' })
  @ApiSuccessResponse(SuburbStatsDto, 'Suburb statistics retrieved successfully')
  @ApiErrorResponse(404, 'Not Found - Suburb not found')
  async getStatistics(@Param('id') id: string): Promise<SuburbStatsDto> {
    return this.suburbsService.getStatistics(id);
  }

  @Get(':id/market-trends')
  @Public()
  @ApiOperation({ summary: 'Get market trends for a suburb' })
  @ApiParam({ name: 'id', description: 'Suburb ID', example: 'clp1234567890' })
  @ApiSuccessResponse(MarketTrendsDto, 'Market trends retrieved successfully')
  @ApiErrorResponse(404, 'Not Found - Suburb not found')
  async getMarketTrends(@Param('id') id: string): Promise<MarketTrendsDto> {
    return this.suburbsService.getMarketTrends(id);
  }
}
