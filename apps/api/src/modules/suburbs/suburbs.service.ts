import {
    BadRequestException,
    Injectable,
    Logger,
    NotFoundException,
} from '@nestjs/common';
import { buildSuburbSearchWhere, createPaginationOptions } from '@revalu/database';
import { PrismaService } from '../../common/database/prisma.service';
import { PaginatedResponseDto } from '../../common/dto/pagination.dto';
import {
    MarketTrendsDto,
    SuburbComparisonDto,
    SuburbResponseDto,
    SuburbSearchDto,
    SuburbStatsDto,
    TopSuburbsDto,
} from './dto/suburb.dto';

@Injectable()
export class SuburbsService {
  private readonly logger = new Logger(SuburbsService.name);

  constructor(private prisma: PrismaService) {}

  async findAll(searchDto: SuburbSearchDto): Promise<PaginatedResponseDto<SuburbResponseDto>> {
    const { page, limit, sortBy, sortOrder, ...filters } = searchDto;
    const pagination = createPaginationOptions({ page, limit });
    const where = buildSuburbSearchWhere(filters);

    // Build order by clause
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder || 'asc';
    } else {
      orderBy.name = 'asc';
    }

    try {
      const [suburbs, total] = await Promise.all([
        this.prisma.client.suburb.findMany({
          where,
          include: {
            _count: {
              select: {
                properties: true,
                schools: true,
                transportStops: true,
              },
            },
          },
          orderBy,
          skip: pagination.skip,
          take: pagination.take,
        }),
        this.prisma.client.suburb.count({ where }),
      ]);

      const transformedSuburbs = await Promise.all(
        suburbs.map(async (suburb) => {
          const propertyStats = await this.getBasicPropertyStats(suburb.id);
          return this.transformSuburbResponse(suburb, propertyStats);
        }),
      );

      return {
        success: true,
        data: transformedSuburbs,
        meta: {
          total,
          page: pagination.page,
          limit: pagination.limit,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page * pagination.limit < total,
          hasPrev: pagination.page > 1,
        },
        message: 'Suburbs retrieved successfully',
      };
    } catch (error) {
      this.logger.error('Failed to fetch suburbs:', error);
      throw new BadRequestException('Failed to fetch suburbs');
    }
  }

  async findOne(id: string): Promise<SuburbResponseDto> {
    const suburb = await this.prisma.client.suburb.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            properties: true,
            schools: true,
            transportStops: true,
          },
        },
      },
    });

    if (!suburb) {
      throw new NotFoundException('Suburb not found');
    }

    const propertyStats = await this.getBasicPropertyStats(suburb.id);
    return this.transformSuburbResponse(suburb, propertyStats);
  }

  async getStatistics(id: string): Promise<SuburbStatsDto> {
    const suburb = await this.prisma.client.suburb.findUnique({
      where: { id },
      include: {
        schools: {
          select: { id: true, name: true, schoolType: true, rating: true },
        },
        transportStops: {
          select: { id: true, name: true, transportType: true, routes: true },
        },
      },
    });

    if (!suburb) {
      throw new NotFoundException('Suburb not found');
    }

    try {
      const [propertyStats, marketTrends, crimeStats] = await Promise.all([
        this.getDetailedPropertyStats(id),
        this.getMarketTrendAnalysis(id),
        this.getCrimeStatistics(id),
      ]);

      return {
        suburbId: suburb.id,
        suburbName: suburb.name,
        propertyStats,
        marketTrends,
        demographics: {
          population: suburb.population || 0,
          medianAge: suburb.medianAge || 0,
          area: suburb.area || 0,
          density: suburb.population && suburb.area ? suburb.population / suburb.area : 0,
        },
        amenities: {
          schools: suburb.schools.map(school => ({
            id: school.id,
            name: school.name,
            type: school.schoolType,
            rating: school.rating,
          })),
          transport: suburb.transportStops.map(stop => ({
            id: stop.id,
            name: stop.name,
            type: stop.transportType,
            routes: stop.routes,
          })),
          totalSchools: suburb.schools.length,
          totalTransportStops: suburb.transportStops.length,
        },
        safety: crimeStats,
      };
    } catch (error) {
      this.logger.error(`Failed to get statistics for suburb ${id}:`, error);
      throw new BadRequestException('Failed to get suburb statistics');
    }
  }

  async getMarketTrends(id: string): Promise<MarketTrendsDto> {
    const suburb = await this.prisma.client.suburb.findUnique({
      where: { id },
      select: { id: true, name: true },
    });

    if (!suburb) {
      throw new NotFoundException('Suburb not found');
    }

    try {
      const trends = await this.prisma.client.marketTrend.findMany({
        where: { suburbId: id },
        orderBy: { startDate: 'desc' },
        take: 12, // Last 12 periods
      });

      const analysis = this.analyzeMarketTrends(trends);

      return {
        suburbId: suburb.id,
        suburbName: suburb.name,
        trends: trends.map(trend => ({
          id: trend.id,
          period: trend.period,
          direction: trend.direction,
          percentageChange: trend.percentageChange,
          averagePrice: trend.averagePrice || 0,
          medianPrice: trend.medianPrice || 0,
          salesVolume: trend.salesVolume || 0,
          daysOnMarket: trend.daysOnMarket || 0,
          startDate: trend.startDate,
          endDate: trend.endDate,
        })),
        analysis,
      };
    } catch (error) {
      this.logger.error(`Failed to get market trends for suburb ${id}:`, error);
      throw new BadRequestException('Failed to get market trends');
    }
  }

  async compareSuburbs(suburbIds: string[]): Promise<SuburbComparisonDto> {
    if (suburbIds.length < 2 || suburbIds.length > 10) {
      throw new BadRequestException('Please provide between 2 and 10 suburbs for comparison');
    }

    try {
      const suburbs = await this.prisma.client.suburb.findMany({
        where: { id: { in: suburbIds } },
        include: {
          schools: { select: { rating: true } },
          transportStops: true,
          crimeStatistics: {
            where: { year: new Date().getFullYear() },
            select: { incidents: true, rate: true },
          },
        },
      });

      if (suburbs.length !== suburbIds.length) {
        throw new BadRequestException('One or more suburbs not found');
      }

      const comparison = await Promise.all(
        suburbs.map(async (suburb, index) => {
          const metrics = await this.calculateSuburbMetrics(suburb);
          const score = this.calculateOverallScore(metrics);

          return {
            suburb: {
              id: suburb.id,
              name: suburb.name,
              state: suburb.state,
              postcode: suburb.postcode,
            },
            metrics,
            score,
            rank: 0, // Will be set after sorting
          };
        }),
      );

      // Sort by score and assign ranks
      comparison.sort((a, b) => b.score - a.score);
      comparison.forEach((item, index) => {
        item.rank = index + 1;
      });

      return {
        comparison,
        criteria: {
          priceWeight: 0.25,
          growthWeight: 0.2,
          amenitiesWeight: 0.2,
          safetyWeight: 0.2,
          accessibilityWeight: 0.15,
        },
      };
    } catch (error) {
      this.logger.error('Failed to compare suburbs:', error);
      throw new BadRequestException('Failed to compare suburbs');
    }
  }

  async getTopSuburbs(
    criteria: 'price' | 'growth' | 'amenities' | 'safety' | 'overall',
    limit: number = 10,
  ): Promise<TopSuburbsDto> {
    if (limit > 50) {
      throw new BadRequestException('Limit cannot exceed 50');
    }

    try {
      const suburbs = await this.prisma.client.suburb.findMany({
        include: {
          schools: { select: { rating: true } },
          transportStops: true,
          marketTrends: {
            orderBy: { startDate: 'desc' },
            take: 4, // Last 4 quarters
          },
          crimeStatistics: {
            where: { year: new Date().getFullYear() },
          },
        },
      });

      const rankedSuburbs = await Promise.all(
        suburbs.map(async (suburb) => {
          const metrics = await this.calculateSuburbMetrics(suburb);
          const value = this.getValueByCriteria(criteria, metrics);
          const score = this.calculateCriteriaScore(criteria, metrics);
          const highlights = this.generateHighlights(suburb, metrics);

          return {
            suburb: {
              id: suburb.id,
              name: suburb.name,
              state: suburb.state,
              postcode: suburb.postcode,
            },
            value,
            score,
            highlights,
          };
        }),
      );

      // Sort by score and take top results
      rankedSuburbs.sort((a, b) => b.score - a.score);
      const topSuburbs = rankedSuburbs.slice(0, limit);

      // Add ranks
      const rankedResults = topSuburbs.map((suburb, index) => ({
        rank: index + 1,
        ...suburb,
      }));

      const averageValue = rankedSuburbs.reduce((sum, s) => sum + s.value, 0) / rankedSuburbs.length;

      return {
        criteria,
        suburbs: rankedResults,
        summary: {
          totalAnalyzed: rankedSuburbs.length,
          averageValue,
          topPercentile: (limit / rankedSuburbs.length) * 100,
          methodology: this.getMethodologyDescription(criteria),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get top suburbs by ${criteria}:`, error);
      throw new BadRequestException('Failed to get top suburbs');
    }
  }

  private async getBasicPropertyStats(suburbId: string) {
    const properties = await this.prisma.client.property.findMany({
      where: { suburbId },
      select: {
        lastSalePrice: true,
        propertyType: true,
      },
    });

    if (properties.length === 0) {
      return {
        totalProperties: 0,
        averagePrice: 0,
        priceRange: { min: 0, max: 0 },
        byType: {},
      };
    }

    const prices = properties
      .map(p => p.lastSalePrice)
      .filter(price => price !== null) as number[];

    const averagePrice = prices.length > 0 
      ? prices.reduce((sum, price) => sum + price, 0) / prices.length 
      : 0;

    const byType = properties.reduce((acc, property) => {
      acc[property.propertyType] = (acc[property.propertyType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalProperties: properties.length,
      averagePrice,
      priceRange: {
        min: prices.length > 0 ? Math.min(...prices) : 0,
        max: prices.length > 0 ? Math.max(...prices) : 0,
      },
      byType,
    };
  }

  private async getDetailedPropertyStats(suburbId: string) {
    const [properties, statusStats] = await Promise.all([
      this.prisma.client.property.findMany({
        where: { suburbId },
        select: {
          lastSalePrice: true,
          propertyType: true,
          status: true,
          createdAt: true,
          lastSaleDate: true,
        },
      }),
      this.prisma.client.property.groupBy({
        by: ['status'],
        where: { suburbId },
        _count: true,
      }),
    ]);

    const basicStats = await this.getBasicPropertyStats(suburbId);

    // Calculate median price
    const prices = properties
      .map(p => p.lastSalePrice)
      .filter(price => price !== null) as number[];
    
    prices.sort((a, b) => a - b);
    const medianPrice = prices.length > 0
      ? prices[Math.floor(prices.length / 2)]
      : 0;

    // Calculate average days on market (simplified)
    const averageDaysOnMarket = 30; // Placeholder calculation

    const byStatus = statusStats.reduce((acc, item) => {
      acc[item.status] = item._count;
      return acc;
    }, {} as Record<string, number>);

    return {
      ...basicStats,
      medianPrice,
      byStatus,
      averageDaysOnMarket,
    };
  }

  private async getMarketTrendAnalysis(suburbId: string) {
    const trends = await this.prisma.client.marketTrend.findMany({
      where: { suburbId },
      orderBy: { startDate: 'desc' },
      take: 12,
    });

    const analysis = this.analyzeMarketTrends(trends);
    
    // Calculate growth rates
    const monthlyGrowth = trends.length > 0 ? trends[0].percentageChange : 0;
    const quarterlyGrowth = trends.slice(0, 3).reduce((sum, t) => sum + t.percentageChange, 0) / 3;
    const yearlyGrowth = trends.reduce((sum, t) => sum + t.percentageChange, 0) / trends.length;

    // Calculate sales volume changes
    const thisMonth = trends[0]?.salesVolume || 0;
    const lastMonth = trends[1]?.salesVolume || 0;
    const volumeChange = lastMonth > 0 ? ((thisMonth - lastMonth) / lastMonth) * 100 : 0;

    return {
      currentTrend: analysis.overallTrend,
      priceGrowth: {
        monthly: monthlyGrowth,
        quarterly: quarterlyGrowth,
        yearly: yearlyGrowth,
      },
      salesVolume: {
        thisMonth,
        lastMonth,
        changePercent: volumeChange,
      },
    };
  }

  private async getCrimeStatistics(suburbId: string) {
    const crimeStats = await this.prisma.client.crimeStatistic.findMany({
      where: {
        suburbId,
        year: new Date().getFullYear(),
      },
    });

    const totalIncidents = crimeStats.reduce((sum, stat) => sum + stat.incidents, 0);
    const averageRate = crimeStats.length > 0
      ? crimeStats.reduce((sum, stat) => sum + (stat.rate || 0), 0) / crimeStats.length
      : 0;

    const recentIncidents = crimeStats.reduce((acc, stat) => {
      acc[stat.crimeType] = stat.incidents;
      return acc;
    }, {} as Record<string, number>);

    // Calculate safety score (inverse of crime rate, normalized to 0-10)
    const safetyScore = Math.max(0, 10 - (averageRate * 2));

    return {
      crimeRate: averageRate,
      safetyScore,
      recentIncidents,
    };
  }

  private analyzeMarketTrends(trends: any[]) {
    if (trends.length === 0) {
      return {
        overallTrend: 'STABLE' as const,
        volatility: 'low' as const,
        momentum: 'stable' as const,
        priceGrowthRate: 0,
        marketHealth: 'moderate' as const,
      };
    }

    const recentTrends = trends.slice(0, 3);
    const upTrends = recentTrends.filter(t => t.direction === 'UP').length;
    const downTrends = recentTrends.filter(t => t.direction === 'DOWN').length;

    const overallTrend = upTrends > downTrends ? 'UP' as const : downTrends > upTrends ? 'DOWN' as const : 'STABLE' as const;
    
    const changes = trends.map(t => Math.abs(t.percentageChange));
    const avgChange = changes.reduce((sum, c) => sum + c, 0) / changes.length;
    const volatility = avgChange > 10 ? 'high' as const : avgChange > 5 ? 'medium' as const : 'low' as const;

    const priceGrowthRate = trends.reduce((sum, t) => sum + t.percentageChange, 0) / trends.length;
    
    const momentum = trends.length >= 2 && Math.abs(trends[0].percentageChange) > Math.abs(trends[1].percentageChange)
      ? 'accelerating' as const : 'stable' as const;

    const marketHealth = priceGrowthRate > 5 ? 'strong' as const : priceGrowthRate < -5 ? 'weak' as const : 'moderate' as const;

    return {
      overallTrend,
      volatility,
      momentum,
      priceGrowthRate,
      marketHealth,
    };
  }

  private async calculateSuburbMetrics(suburb: any) {
    const propertyStats = await this.getBasicPropertyStats(suburb.id);
    
    const schoolRating = suburb.schools.length > 0
      ? suburb.schools.reduce((sum: number, s: any) => sum + (s.rating || 0), 0) / suburb.schools.length
      : 0;

    const crimeRate = suburb.crimeStatistics.length > 0
      ? suburb.crimeStatistics.reduce((sum: number, c: any) => sum + (c.rate || 0), 0) / suburb.crimeStatistics.length
      : 0;

    const transportScore = Math.min(10, suburb.transportStops.length * 2);

    return {
      medianPrice: suburb.medianPrice || propertyStats.averagePrice,
      priceGrowth: 5, // Placeholder - would calculate from market trends
      population: suburb.population || 0,
      medianAge: suburb.medianAge || 0,
      crimeRate,
      schoolRating,
      transportScore,
    };
  }

  private calculateOverallScore(metrics: any): number {
    // Normalize and weight different metrics
    const priceScore = Math.min(10, metrics.medianPrice / 500000); // Normalize to property price
    const growthScore = Math.max(0, Math.min(10, metrics.priceGrowth + 5)); // -5 to +15 range
    const safetyScore = Math.max(0, 10 - metrics.crimeRate);
    const amenityScore = (metrics.schoolRating + metrics.transportScore) / 2;

    return (priceScore * 0.25 + growthScore * 0.2 + safetyScore * 0.3 + amenityScore * 0.25);
  }

  private getValueByCriteria(criteria: string, metrics: any): number {
    switch (criteria) {
      case 'price': return metrics.medianPrice;
      case 'growth': return metrics.priceGrowth;
      case 'amenities': return (metrics.schoolRating + metrics.transportScore) / 2;
      case 'safety': return 10 - metrics.crimeRate;
      case 'overall': return this.calculateOverallScore(metrics);
      default: return 0;
    }
  }

  private calculateCriteriaScore(criteria: string, metrics: any): number {
    return this.getValueByCriteria(criteria, metrics);
  }

  private generateHighlights(suburb: any, metrics: any): string[] {
    const highlights: string[] = [];
    
    if (metrics.medianPrice > 2000000) highlights.push('Premium location');
    if (metrics.priceGrowth > 10) highlights.push('Strong price growth');
    if (metrics.schoolRating > 8) highlights.push('Excellent schools');
    if (metrics.crimeRate < 2) highlights.push('Very safe area');
    if (metrics.transportScore > 8) highlights.push('Great transport links');
    
    return highlights.slice(0, 3); // Limit to top 3 highlights
  }

  private getMethodologyDescription(criteria: string): string {
    const descriptions = {
      price: 'Ranked by median property price',
      growth: 'Ranked by price growth rate over the last 12 months',
      amenities: 'Ranked by school ratings and transport accessibility',
      safety: 'Ranked by crime statistics and safety scores',
      overall: 'Ranked by weighted combination of all factors',
    };
    
    return descriptions[criteria] || 'Custom ranking methodology';
  }

  private transformSuburbResponse(suburb: any, propertyStats?: any): SuburbResponseDto {
    return {
      id: suburb.id,
      name: suburb.name,
      state: suburb.state,
      postcode: suburb.postcode,
      country: suburb.country,
      latitude: suburb.latitude,
      longitude: suburb.longitude,
      population: suburb.population,
      area: suburb.area,
      medianAge: suburb.medianAge,
      medianPrice: suburb.medianPrice,
      createdAt: suburb.createdAt,
      updatedAt: suburb.updatedAt,
      propertyStats,
      amenities: suburb._count ? {
        schools: suburb._count.schools,
        transportStops: suburb._count.transportStops,
      } : undefined,
    };
  }
}
