import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsArray,
  Min,
  Max,
  <PERSON><PERSON><PERSON>tude,
  IsLongitude,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TrendDirection, TrendPeriod } from '@revalu/database';
import { PaginationDto } from '../../../common/dto/pagination.dto';

export class SuburbSearchDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Suburb name to search for',
    example: 'Toorak',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'State to filter by',
    example: 'VIC',
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({
    description: 'Postcode to filter by',
    example: '3142',
  })
  @IsOptional()
  @IsString()
  postcode?: string;

  @ApiPropertyOptional({
    description: 'Minimum population',
    example: 5000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minPopulation?: number;

  @ApiPropertyOptional({
    description: 'Maximum population',
    example: 50000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxPopulation?: number;

  @ApiPropertyOptional({
    description: 'Minimum median price',
    example: 500000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minMedianPrice?: number;

  @ApiPropertyOptional({
    description: 'Maximum median price',
    example: 3000000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxMedianPrice?: number;
}

export class SuburbResponseDto {
  @ApiProperty({ description: 'Suburb ID', example: 'clp1234567890' })
  id: string;

  @ApiProperty({ description: 'Suburb name', example: 'Toorak' })
  name: string;

  @ApiProperty({ description: 'State', example: 'VIC' })
  state: string;

  @ApiProperty({ description: 'Postcode', example: '3142' })
  postcode: string;

  @ApiProperty({ description: 'Country', example: 'Australia' })
  country: string;

  @ApiPropertyOptional({ description: 'Latitude', example: -37.8403 })
  latitude?: number;

  @ApiPropertyOptional({ description: 'Longitude', example: 145.0075 })
  longitude?: number;

  @ApiPropertyOptional({ description: 'Population', example: 12817 })
  population?: number;

  @ApiPropertyOptional({ description: 'Area in square kilometers', example: 6.1 })
  area?: number;

  @ApiPropertyOptional({ description: 'Median age', example: 42.5 })
  medianAge?: number;

  @ApiPropertyOptional({ description: 'Median price', example: 2500000 })
  medianPrice?: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Property statistics' })
  propertyStats?: {
    totalProperties: number;
    averagePrice: number;
    priceRange: { min: number; max: number };
    byType: Record<string, number>;
  };

  @ApiPropertyOptional({ description: 'Amenities count' })
  amenities?: {
    schools: number;
    transportStops: number;
  };
}

export class SuburbStatsDto {
  @ApiProperty({ description: 'Suburb ID', example: 'clp1234567890' })
  suburbId: string;

  @ApiProperty({ description: 'Suburb name', example: 'Toorak' })
  suburbName: string;

  @ApiProperty({ description: 'Property statistics' })
  propertyStats: {
    totalProperties: number;
    averagePrice: number;
    medianPrice: number;
    priceRange: { min: number; max: number };
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    averageDaysOnMarket: number;
  };

  @ApiProperty({ description: 'Market trends' })
  marketTrends: {
    currentTrend: TrendDirection;
    priceGrowth: {
      monthly: number;
      quarterly: number;
      yearly: number;
    };
    salesVolume: {
      thisMonth: number;
      lastMonth: number;
      changePercent: number;
    };
  };

  @ApiProperty({ description: 'Demographics' })
  demographics: {
    population: number;
    medianAge: number;
    area: number;
    density: number;
  };

  @ApiProperty({ description: 'Amenities' })
  amenities: {
    schools: Array<{
      id: string;
      name: string;
      type: string;
      rating?: number;
    }>;
    transport: Array<{
      id: string;
      name: string;
      type: string;
      routes: string[];
    }>;
    totalSchools: number;
    totalTransportStops: number;
  };

  @ApiProperty({ description: 'Safety statistics' })
  safety: {
    crimeRate: number;
    safetyScore: number;
    recentIncidents: Record<string, number>;
  };
}

export class MarketTrendsDto {
  @ApiProperty({ description: 'Suburb ID', example: 'clp1234567890' })
  suburbId: string;

  @ApiProperty({ description: 'Suburb name', example: 'Toorak' })
  suburbName: string;

  @ApiProperty({ description: 'Market trends by period' })
  trends: Array<{
    id: string;
    period: TrendPeriod;
    direction: TrendDirection;
    percentageChange: number;
    averagePrice: number;
    medianPrice: number;
    salesVolume: number;
    daysOnMarket: number;
    startDate: Date;
    endDate: Date;
  }>;

  @ApiProperty({ description: 'Trend analysis' })
  analysis: {
    overallTrend: TrendDirection;
    volatility: 'low' | 'medium' | 'high';
    momentum: 'accelerating' | 'stable' | 'decelerating';
    priceGrowthRate: number;
    marketHealth: 'strong' | 'moderate' | 'weak';
  };
}

export class SuburbComparisonDto {
  @ApiProperty({ description: 'Comparison results' })
  comparison: Array<{
    suburb: {
      id: string;
      name: string;
      state: string;
      postcode: string;
    };
    metrics: {
      medianPrice: number;
      priceGrowth: number;
      population: number;
      medianAge: number;
      crimeRate: number;
      schoolRating: number;
      transportScore: number;
    };
    score: number;
    rank: number;
  }>;

  @ApiProperty({ description: 'Comparison criteria' })
  criteria: {
    priceWeight: number;
    growthWeight: number;
    amenitiesWeight: number;
    safetyWeight: number;
    accessibilityWeight: number;
  };
}

export class TopSuburbsDto {
  @ApiProperty({ description: 'Criteria used for ranking' })
  criteria: string;

  @ApiProperty({ description: 'Top suburbs' })
  suburbs: Array<{
    rank: number;
    suburb: {
      id: string;
      name: string;
      state: string;
      postcode: string;
    };
    value: number;
    score: number;
    highlights: string[];
  }>;

  @ApiProperty({ description: 'Analysis summary' })
  summary: {
    totalAnalyzed: number;
    averageValue: number;
    topPercentile: number;
    methodology: string;
  };
}
