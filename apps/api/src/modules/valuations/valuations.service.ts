import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { calculateDistance } from '@revalu/database';
import { PrismaService } from '../../common/database/prisma.service';
import { PaginatedResponseDto } from '../../common/dto/pagination.dto';
import {
  CreateValuationDto,
  MarketComparisonDto,
  UpdateValuationDto,
  ValuationHistoryDto,
  ValuationResponseDto,
  ValuationSearchDto,
} from './dto/valuation.dto';

@Injectable()
export class ValuationsService {
  private readonly logger = new Logger(ValuationsService.name);

  constructor(private prisma: PrismaService) {}

  async create(createValuationDto: CreateValuationDto): Promise<ValuationResponseDto> {
    const { propertyId, ...valuationData } = createValuationDto;

    // Verify property exists
    const property = await this.prisma.client.property.findUnique({
      where: { id: propertyId },
      include: {
        suburb: {
          select: { name: true, state: true, postcode: true },
        },
      },
    });

    if (!property) {
      throw new BadRequestException('Property not found');
    }

    try {
      const valuation = await this.prisma.client.valuation.create({
        data: {
          ...valuationData,
          propertyId,
          valuationDate: valuationData.valuationDate
            ? new Date(valuationData.valuationDate)
            : new Date(),
          expiryDate: valuationData.expiryDate ? new Date(valuationData.expiryDate) : undefined,
        },
        include: {
          property: {
            select: {
              id: true,
              address: true,
              propertyType: true,
              suburb: {
                select: { name: true, state: true, postcode: true },
              },
            },
          },
        },
      });

      this.logger.log(`Valuation created: ${valuation.id} for property ${propertyId}`);
      return this.transformValuationResponse(valuation);
    } catch (error) {
      this.logger.error('Failed to create valuation:', error);
      throw new BadRequestException('Failed to create valuation');
    }
  }

  async findAll(
    searchDto: ValuationSearchDto,
  ): Promise<PaginatedResponseDto<ValuationResponseDto>> {
    const { page = 1, limit = 20, sortBy, sortOrder, ...filters } = searchDto;
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (filters.propertyId) where.propertyId = filters.propertyId;
    if (filters.method) where.method = filters.method;
    if (filters.status) where.status = filters.status;
    if (filters.minValue || filters.maxValue) {
      where.estimatedValue = {};
      if (filters.minValue) where.estimatedValue.gte = filters.minValue;
      if (filters.maxValue) where.estimatedValue.lte = filters.maxValue;
    }
    if (filters.dateFrom || filters.dateTo) {
      where.valuationDate = {};
      if (filters.dateFrom) where.valuationDate.gte = new Date(filters.dateFrom);
      if (filters.dateTo) where.valuationDate.lte = new Date(filters.dateTo);
    }

    // Build order by clause
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder || 'desc';
    } else {
      orderBy.valuationDate = 'desc';
    }

    try {
      const [valuations, total] = await Promise.all([
        this.prisma.client.valuation.findMany({
          where,
          include: {
            property: {
              select: {
                id: true,
                address: true,
                propertyType: true,
                suburb: {
                  select: { name: true, state: true, postcode: true },
                },
              },
            },
          },
          orderBy,
          skip,
          take: limit,
        }),
        this.prisma.client.valuation.count({ where }),
      ]);

      const transformedValuations = valuations.map(this.transformValuationResponse);

      return {
        success: true,
        data: transformedValuations,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
        message: 'Valuations retrieved successfully',
      };
    } catch (error) {
      this.logger.error('Failed to fetch valuations:', error);
      throw new BadRequestException('Failed to fetch valuations');
    }
  }

  async findOne(id: string): Promise<ValuationResponseDto> {
    const valuation = await this.prisma.client.valuation.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            address: true,
            propertyType: true,
            suburb: {
              select: { name: true, state: true, postcode: true },
            },
          },
        },
      },
    });

    if (!valuation) {
      throw new NotFoundException('Valuation not found');
    }

    return this.transformValuationResponse(valuation);
  }

  async update(id: string, updateValuationDto: UpdateValuationDto): Promise<ValuationResponseDto> {
    // Check if valuation exists
    const existingValuation = await this.prisma.client.valuation.findUnique({
      where: { id },
    });

    if (!existingValuation) {
      throw new NotFoundException('Valuation not found');
    }

    try {
      const valuation = await this.prisma.client.valuation.update({
        where: { id },
        data: {
          ...updateValuationDto,
          expiryDate: updateValuationDto.expiryDate
            ? new Date(updateValuationDto.expiryDate)
            : undefined,
        },
        include: {
          property: {
            select: {
              id: true,
              address: true,
              propertyType: true,
              suburb: {
                select: { name: true, state: true, postcode: true },
              },
            },
          },
        },
      });

      this.logger.log(`Valuation updated: ${valuation.id}`);
      return this.transformValuationResponse(valuation);
    } catch (error) {
      this.logger.error(`Failed to update valuation ${id}:`, error);
      throw new BadRequestException('Failed to update valuation');
    }
  }

  async remove(id: string): Promise<{ message: string }> {
    // Check if valuation exists
    const existingValuation = await this.prisma.client.valuation.findUnique({
      where: { id },
    });

    if (!existingValuation) {
      throw new NotFoundException('Valuation not found');
    }

    try {
      await this.prisma.client.valuation.delete({
        where: { id },
      });

      this.logger.log(`Valuation deleted: ${id}`);
      return { message: 'Valuation deleted successfully' };
    } catch (error) {
      this.logger.error(`Failed to delete valuation ${id}:`, error);
      throw new BadRequestException('Failed to delete valuation');
    }
  }

  async getPropertyHistory(propertyId: string): Promise<ValuationHistoryDto> {
    // Verify property exists
    const property = await this.prisma.client.property.findUnique({
      where: { id: propertyId },
      select: { id: true, address: true },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    try {
      const valuations = await this.prisma.client.valuation.findMany({
        where: { propertyId },
        orderBy: { valuationDate: 'desc' },
        select: {
          id: true,
          estimatedValue: true,
          confidence: true,
          method: true,
          valuationDate: true,
          status: true,
        },
      });

      // Calculate trend analysis
      const trend = this.calculateTrend(valuations);

      return {
        propertyId: property.id,
        propertyAddress: property.address,
        valuations,
        trend,
      };
    } catch (error) {
      this.logger.error(`Failed to get valuation history for property ${propertyId}:`, error);
      throw new BadRequestException('Failed to get valuation history');
    }
  }

  async getMarketComparison(valuationId: string): Promise<MarketComparisonDto> {
    const valuation = await this.prisma.client.valuation.findUnique({
      where: { id: valuationId },
      include: {
        property: {
          include: {
            suburb: true,
          },
        },
      },
    });

    if (!valuation) {
      throw new NotFoundException('Valuation not found');
    }

    try {
      // Find comparable properties in the same suburb
      const comparableProperties = await this.prisma.client.property.findMany({
        where: {
          suburbId: valuation.property.suburbId,
          id: { not: valuation.propertyId },
          propertyType: valuation.property.propertyType,
        },
        include: {
          valuations: {
            orderBy: { valuationDate: 'desc' },
            take: 1,
          },
        },
        take: 10,
      });

      // Calculate similarities and distances
      const comparables = comparableProperties
        .filter((prop) => prop.valuations.length > 0)
        .map((prop) => {
          const latestValuation = prop.valuations[0];
          const similarity = this.calculateSimilarity(valuation.property, prop);
          const distance = this.calculatePropertyDistance(valuation.property, prop);

          return {
            id: prop.id,
            address: prop.address,
            estimatedValue: latestValuation.estimatedValue,
            similarity,
            distance,
            valuationDate: latestValuation.valuationDate,
          };
        })
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, 5);

      // Calculate market statistics
      const allValues = comparables.map((c) => c.estimatedValue);
      const averageValue = allValues.reduce((sum, val) => sum + val, 0) / allValues.length || 0;
      const sortedValues = allValues.sort((a, b) => a - b);
      const medianValue =
        sortedValues.length > 0 ? sortedValues[Math.floor(sortedValues.length / 2)] : 0;

      const pricePerSqm = valuation.property.buildingSize
        ? valuation.estimatedValue / valuation.property.buildingSize
        : 0;

      const marketPosition =
        valuation.estimatedValue > averageValue * 1.1
          ? 'above'
          : valuation.estimatedValue < averageValue * 0.9
            ? 'below'
            : 'average';

      return {
        targetProperty: {
          id: valuation.property.id,
          address: valuation.property.address,
          estimatedValue: valuation.estimatedValue,
          valuationDate: valuation.valuationDate,
        },
        comparables,
        marketStats: {
          averageValue,
          medianValue,
          pricePerSqm,
          marketPosition,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get market comparison for valuation ${valuationId}:`, error);
      throw new BadRequestException('Failed to get market comparison');
    }
  }

  private calculateTrend(valuations: any[]) {
    if (valuations.length < 2) {
      return {
        direction: 'stable' as const,
        percentageChange: 0,
        averageValue: valuations[0]?.estimatedValue || 0,
        latestValue: valuations[0]?.estimatedValue || 0,
      };
    }

    const latest = valuations[0].estimatedValue;
    const previous = valuations[1].estimatedValue;
    const percentageChange = ((latest - previous) / previous) * 100;

    const direction =
      percentageChange > 5
        ? ('up' as const)
        : percentageChange < -5
          ? ('down' as const)
          : ('stable' as const);
    const averageValue =
      valuations.reduce((sum, v) => sum + v.estimatedValue, 0) / valuations.length;

    return {
      direction,
      percentageChange,
      averageValue,
      latestValue: latest,
    };
  }

  private calculateSimilarity(property1: any, property2: any): number {
    let similarity = 0;
    let factors = 0;

    // Property type match
    if (property1.propertyType === property2.propertyType) {
      similarity += 0.3;
    }
    factors += 0.3;

    // Bedroom similarity
    if (property1.bedrooms && property2.bedrooms) {
      const bedroomDiff = Math.abs(property1.bedrooms - property2.bedrooms);
      similarity += Math.max(0, 0.2 - bedroomDiff * 0.05);
      factors += 0.2;
    }

    // Size similarity
    if (property1.buildingSize && property2.buildingSize) {
      const sizeDiff =
        Math.abs(property1.buildingSize - property2.buildingSize) / property1.buildingSize;
      similarity += Math.max(0, 0.25 - sizeDiff);
      factors += 0.25;
    }

    // Age similarity
    if (property1.yearBuilt && property2.yearBuilt) {
      const ageDiff = Math.abs(property1.yearBuilt - property2.yearBuilt);
      similarity += Math.max(0, 0.15 - ageDiff * 0.002);
      factors += 0.15;
    }

    // Location (same suburb already filtered)
    similarity += 0.1;
    factors += 0.1;

    return factors > 0 ? similarity / factors : 0;
  }

  private calculatePropertyDistance(property1: any, property2: any): number {
    if (
      !property1.latitude ||
      !property1.longitude ||
      !property2.latitude ||
      !property2.longitude
    ) {
      return 0;
    }

    return calculateDistance(
      property1.latitude,
      property1.longitude,
      property2.latitude,
      property2.longitude,
    );
  }

  private transformValuationResponse(valuation: any): ValuationResponseDto {
    return {
      id: valuation.id,
      propertyId: valuation.propertyId,
      estimatedValue: valuation.estimatedValue,
      confidence: valuation.confidence,
      method: valuation.method,
      status: valuation.status,
      valuationDate: valuation.valuationDate,
      expiryDate: valuation.expiryDate,
      factors: valuation.factors,
      comparables: valuation.comparables,
      notes: valuation.notes,
      createdAt: valuation.createdAt,
      updatedAt: valuation.updatedAt,
      property: valuation.property,
    };
  }
}
