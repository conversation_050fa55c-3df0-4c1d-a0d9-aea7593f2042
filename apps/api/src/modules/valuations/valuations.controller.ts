import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ValuationsService } from './valuations.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Public } from '../auth/decorators/public.decorator';
import {
  CreateValuationDto,
  UpdateValuationDto,
  ValuationSearchDto,
  ValuationResponseDto,
  ValuationHistoryDto,
  MarketComparisonDto,
} from './dto/valuation.dto';
import {
  ApiSuccessResponse,
  ApiPaginatedResponse,
  ApiErrorResponse,
} from '../../common/decorators/api-response.decorator';
import { PaginatedResponseDto } from '../../common/dto/pagination.dto';

@ApiTags('valuations')
@Controller('valuations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ValuationsController {
  constructor(private readonly valuationsService: ValuationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new property valuation' })
  @ApiSuccessResponse(ValuationResponseDto, 'Valuation created successfully')
  @ApiErrorResponse(400, 'Bad Request - Invalid valuation data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  async create(@Body() createValuationDto: CreateValuationDto): Promise<ValuationResponseDto> {
    return this.valuationsService.create(createValuationDto);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Search and filter valuations' })
  @ApiPaginatedResponse(ValuationResponseDto, 'Valuations retrieved successfully')
  @ApiErrorResponse(400, 'Bad Request - Invalid search parameters')
  async findAll(
    @Query() searchDto: ValuationSearchDto,
  ): Promise<PaginatedResponseDto<ValuationResponseDto>> {
    return this.valuationsService.findAll(searchDto);
  }

  @Get('property/:propertyId/history')
  @Public()
  @ApiOperation({ summary: 'Get valuation history for a property' })
  @ApiParam({ name: 'propertyId', description: 'Property ID', example: 'clp1234567890' })
  @ApiSuccessResponse(ValuationHistoryDto, 'Valuation history retrieved successfully')
  @ApiErrorResponse(404, 'Not Found - Property not found')
  async getPropertyHistory(@Param('propertyId') propertyId: string): Promise<ValuationHistoryDto> {
    return this.valuationsService.getPropertyHistory(propertyId);
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get valuation by ID' })
  @ApiParam({ name: 'id', description: 'Valuation ID', example: 'clp1234567890' })
  @ApiSuccessResponse(ValuationResponseDto, 'Valuation retrieved successfully')
  @ApiErrorResponse(404, 'Not Found - Valuation not found')
  async findOne(@Param('id') id: string): Promise<ValuationResponseDto> {
    return this.valuationsService.findOne(id);
  }

  @Get(':id/market-comparison')
  @Public()
  @ApiOperation({ summary: 'Get market comparison for a valuation' })
  @ApiParam({ name: 'id', description: 'Valuation ID', example: 'clp1234567890' })
  @ApiSuccessResponse(MarketComparisonDto, 'Market comparison retrieved successfully')
  @ApiErrorResponse(404, 'Not Found - Valuation not found')
  async getMarketComparison(@Param('id') id: string): Promise<MarketComparisonDto> {
    return this.valuationsService.getMarketComparison(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update valuation by ID' })
  @ApiParam({ name: 'id', description: 'Valuation ID', example: 'clp1234567890' })
  @ApiSuccessResponse(ValuationResponseDto, 'Valuation updated successfully')
  @ApiErrorResponse(400, 'Bad Request - Invalid update data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(404, 'Not Found - Valuation not found')
  async update(
    @Param('id') id: string,
    @Body() updateValuationDto: UpdateValuationDto,
  ): Promise<ValuationResponseDto> {
    return this.valuationsService.update(id, updateValuationDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete valuation by ID' })
  @ApiParam({ name: 'id', description: 'Valuation ID', example: 'clp1234567890' })
  @ApiResponse({
    status: 200,
    description: 'Valuation deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Valuation deleted successfully' },
      },
    },
  })
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(404, 'Not Found - Valuation not found')
  async remove(@Param('id') id: string): Promise<{ message: string }> {
    return this.valuationsService.remove(id);
  }
}
