import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsEnum,
  IsDateString,
  Min,
  Max,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ValuationMethod, ValuationStatus } from '@revalu/database';
import { PaginationDto } from '../../../common/dto/pagination.dto';

export class CreateValuationDto {
  @ApiProperty({
    description: 'Property ID',
    example: 'clp1234567890',
  })
  @IsString()
  propertyId: string;

  @ApiProperty({
    description: 'Estimated property value',
    example: 2500000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  estimatedValue: number;

  @ApiPropertyOptional({
    description: 'Confidence score (0-1)',
    example: 0.85,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;

  @ApiProperty({
    description: 'Valuation method used',
    enum: ValuationMethod,
    example: ValuationMethod.AUTOMATED,
  })
  @IsEnum(ValuationMethod)
  method: ValuationMethod;

  @ApiPropertyOptional({
    description: 'Valuation status',
    enum: ValuationStatus,
    example: ValuationStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(ValuationStatus)
  status?: ValuationStatus;

  @ApiPropertyOptional({
    description: 'Valuation date',
    example: '2024-01-15T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  valuationDate?: string;

  @ApiPropertyOptional({
    description: 'Expiry date for the valuation',
    example: '2024-07-15T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiPropertyOptional({
    description: 'Factors that influenced the valuation',
    example: {
      location: 0.3,
      size: 0.25,
      condition: 0.2,
      market: 0.25,
    },
  })
  @IsOptional()
  @IsObject()
  factors?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Comparable properties used',
    example: [
      { address: '125 Main St', price: 2400000, similarity: 0.9 },
      { address: '127 Main St', price: 2600000, similarity: 0.85 },
    ],
  })
  @IsOptional()
  @IsObject()
  comparables?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Property in excellent condition with recent renovations',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateValuationDto {
  @ApiPropertyOptional({
    description: 'Estimated property value',
    example: 2600000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  estimatedValue?: number;

  @ApiPropertyOptional({
    description: 'Confidence score (0-1)',
    example: 0.9,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;

  @ApiPropertyOptional({
    description: 'Valuation status',
    enum: ValuationStatus,
    example: ValuationStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(ValuationStatus)
  status?: ValuationStatus;

  @ApiPropertyOptional({
    description: 'Expiry date for the valuation',
    example: '2024-12-15T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiPropertyOptional({
    description: 'Factors that influenced the valuation',
  })
  @IsOptional()
  @IsObject()
  factors?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Comparable properties used',
  })
  @IsOptional()
  @IsObject()
  comparables?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Updated valuation based on recent market changes',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class ValuationSearchDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Property ID to filter by',
    example: 'clp1234567890',
  })
  @IsOptional()
  @IsString()
  propertyId?: string;

  @ApiPropertyOptional({
    description: 'Valuation method to filter by',
    enum: ValuationMethod,
    example: ValuationMethod.AUTOMATED,
  })
  @IsOptional()
  @IsEnum(ValuationMethod)
  method?: ValuationMethod;

  @ApiPropertyOptional({
    description: 'Valuation status to filter by',
    enum: ValuationStatus,
    example: ValuationStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(ValuationStatus)
  status?: ValuationStatus;

  @ApiPropertyOptional({
    description: 'Minimum estimated value',
    example: 1000000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minValue?: number;

  @ApiPropertyOptional({
    description: 'Maximum estimated value',
    example: 5000000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxValue?: number;

  @ApiPropertyOptional({
    description: 'Date from (ISO string)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  dateFrom?: string;

  @ApiPropertyOptional({
    description: 'Date to (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  dateTo?: string;
}

export class ValuationResponseDto {
  @ApiProperty({ description: 'Valuation ID', example: 'clp1234567890' })
  id: string;

  @ApiProperty({ description: 'Property ID', example: 'clp1234567890' })
  propertyId: string;

  @ApiProperty({ description: 'Estimated property value', example: 2500000 })
  estimatedValue: number;

  @ApiPropertyOptional({ description: 'Confidence score', example: 0.85 })
  confidence?: number;

  @ApiProperty({ description: 'Valuation method', enum: ValuationMethod })
  method: ValuationMethod;

  @ApiProperty({ description: 'Valuation status', enum: ValuationStatus })
  status: ValuationStatus;

  @ApiProperty({ description: 'Valuation date' })
  valuationDate: Date;

  @ApiPropertyOptional({ description: 'Expiry date' })
  expiryDate?: Date;

  @ApiPropertyOptional({ description: 'Valuation factors' })
  factors?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Comparable properties' })
  comparables?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Additional notes' })
  notes?: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Property information' })
  property: {
    id: string;
    address: string;
    propertyType: string;
    suburb: {
      name: string;
      state: string;
      postcode: string;
    };
  };
}

export class ValuationHistoryDto {
  @ApiProperty({ description: 'Property ID', example: 'clp1234567890' })
  propertyId: string;

  @ApiProperty({ description: 'Property address' })
  propertyAddress: string;

  @ApiProperty({ description: 'Valuation history' })
  valuations: Array<{
    id: string;
    estimatedValue: number;
    confidence?: number;
    method: ValuationMethod;
    valuationDate: Date;
    status: ValuationStatus;
  }>;

  @ApiProperty({ description: 'Value trend analysis' })
  trend: {
    direction: 'up' | 'down' | 'stable';
    percentageChange: number;
    averageValue: number;
    latestValue: number;
  };
}

export class MarketComparisonDto {
  @ApiProperty({ description: 'Target property valuation' })
  targetProperty: {
    id: string;
    address: string;
    estimatedValue: number;
    valuationDate: Date;
  };

  @ApiProperty({ description: 'Comparable properties' })
  comparables: Array<{
    id: string;
    address: string;
    estimatedValue: number;
    similarity: number;
    distance: number;
    valuationDate: Date;
  }>;

  @ApiProperty({ description: 'Market statistics' })
  marketStats: {
    averageValue: number;
    medianValue: number;
    pricePerSqm: number;
    marketPosition: 'below' | 'average' | 'above';
  };
}
