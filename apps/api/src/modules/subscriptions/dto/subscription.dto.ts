import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsNumber, IsBoolean, IsDateString } from 'class-validator';
import { PlanType, SubscriptionStatus, BillingInterval } from '@revalu/database';

export class CreateSubscriptionDto {
  @ApiProperty({
    description: 'Plan type',
    enum: PlanType,
    example: PlanType.HOME_PLUS,
  })
  @IsEnum(PlanType)
  planType: PlanType;

  @ApiProperty({
    description: 'Billing interval',
    enum: BillingInterval,
    example: BillingInterval.MONTHLY,
  })
  @IsEnum(BillingInterval)
  billingInterval: BillingInterval;

  @ApiPropertyOptional({
    description: 'Stripe payment method ID',
    example: 'pm_1234567890',
  })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;
}

export class UpdateSubscriptionDto {
  @ApiPropertyOptional({
    description: 'Plan type',
    enum: PlanType,
    example: PlanType.INVESTOR_PLUS,
  })
  @IsOptional()
  @IsEnum(PlanType)
  planType?: PlanType;

  @ApiPropertyOptional({
    description: 'Billing interval',
    enum: BillingInterval,
    example: BillingInterval.YEARLY,
  })
  @IsOptional()
  @IsEnum(BillingInterval)
  billingInterval?: BillingInterval;
}

export class SubscriptionResponseDto {
  @ApiProperty({
    description: 'Subscription ID',
    example: 'clp1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: 'clp1234567890',
  })
  userId: string;

  @ApiProperty({
    description: 'Plan type',
    enum: PlanType,
    example: PlanType.HOME_PLUS,
  })
  planType: PlanType;

  @ApiProperty({
    description: 'Subscription status',
    enum: SubscriptionStatus,
    example: SubscriptionStatus.ACTIVE,
  })
  status: SubscriptionStatus;

  @ApiProperty({
    description: 'Billing interval',
    enum: BillingInterval,
    example: BillingInterval.MONTHLY,
  })
  billingInterval: BillingInterval;

  @ApiProperty({
    description: 'Price per month in cents',
    example: 1900,
  })
  pricePerMonth: number;

  @ApiProperty({
    description: 'Currency',
    example: 'AUD',
  })
  currency: string;

  @ApiProperty({
    description: 'Current period start date',
    example: '2024-01-01T00:00:00.000Z',
  })
  currentPeriodStart: Date;

  @ApiProperty({
    description: 'Current period end date',
    example: '2024-02-01T00:00:00.000Z',
  })
  currentPeriodEnd: Date;

  @ApiPropertyOptional({
    description: 'Trial end date',
    example: '2024-01-15T00:00:00.000Z',
  })
  trialEnd?: Date;

  @ApiProperty({
    description: 'Monthly search limit',
    example: 10,
  })
  monthlySearchLimit?: number;

  @ApiProperty({
    description: 'Yearly search limit',
    example: 4,
  })
  yearlySearchLimit?: number;

  @ApiProperty({
    description: 'Property tracking limit',
    example: 3,
  })
  propertyTrackLimit?: number;

  @ApiProperty({
    description: 'Can export reports',
    example: false,
  })
  canExportReports: boolean;

  @ApiProperty({
    description: 'Can access CMA features',
    example: false,
  })
  canAccessCMA: boolean;

  @ApiProperty({
    description: 'Can white label reports',
    example: false,
  })
  canWhiteLabel: boolean;

  @ApiProperty({
    description: 'Can access API',
    example: false,
  })
  canAccessAPI: boolean;

  @ApiProperty({
    description: 'Has priority support',
    example: false,
  })
  hasPrioritySupport: boolean;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class PlanLimitsDto {
  @ApiProperty({
    description: 'Plan type',
    enum: PlanType,
    example: PlanType.HOME_PLUS,
  })
  planType: PlanType;

  @ApiProperty({
    description: 'Monthly search limit',
    example: 10,
  })
  monthlySearchLimit?: number;

  @ApiProperty({
    description: 'Yearly search limit',
    example: 4,
  })
  yearlySearchLimit?: number;

  @ApiProperty({
    description: 'Property tracking limit',
    example: 3,
  })
  propertyTrackLimit?: number;

  @ApiProperty({
    description: 'Team member limit',
    example: 1,
  })
  teamMemberLimit?: number;

  @ApiProperty({
    description: 'Can export reports',
    example: false,
  })
  canExportReports: boolean;

  @ApiProperty({
    description: 'Can access CMA features',
    example: false,
  })
  canAccessCMA: boolean;

  @ApiProperty({
    description: 'Can white label reports',
    example: false,
  })
  canWhiteLabel: boolean;

  @ApiProperty({
    description: 'Can access API',
    example: false,
  })
  canAccessAPI: boolean;

  @ApiProperty({
    description: 'Has priority support',
    example: false,
  })
  hasPrioritySupport: boolean;
}
