import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsNumber, IsDateString, IsObject } from 'class-validator';
import { UsageType } from '@revalu/database';

export class RecordUsageDto {
  @ApiProperty({
    description: 'Type of usage',
    enum: UsageType,
    example: UsageType.SEARCH,
  })
  @IsEnum(UsageType)
  usageType: UsageType;

  @ApiPropertyOptional({
    description: 'Usage count',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @IsNumber()
  count?: number;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { query: 'Toorak VIC', filters: { propertyType: 'HOUSE' } },
  })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

export class UsageStatsDto {
  @ApiProperty({
    description: 'User ID',
    example: 'clp1234567890',
  })
  userId: string;

  @ApiProperty({
    description: 'Current monthly search count',
    example: 5,
  })
  monthlySearchCount: number;

  @ApiProperty({
    description: 'Current yearly search count',
    example: 15,
  })
  yearlySearchCount: number;

  @ApiProperty({
    description: 'Tracked properties count',
    example: 2,
  })
  trackedPropertiesCount: number;

  @ApiProperty({
    description: 'Monthly search limit',
    example: 10,
  })
  monthlySearchLimit?: number;

  @ApiProperty({
    description: 'Yearly search limit',
    example: 4,
  })
  yearlySearchLimit?: number;

  @ApiProperty({
    description: 'Property tracking limit',
    example: 3,
  })
  propertyTrackLimit?: number;

  @ApiProperty({
    description: 'Monthly searches remaining',
    example: 5,
  })
  monthlySearchesRemaining?: number;

  @ApiProperty({
    description: 'Yearly searches remaining',
    example: -11,
  })
  yearlySearchesRemaining?: number;

  @ApiProperty({
    description: 'Property tracking slots remaining',
    example: 1,
  })
  propertyTrackingSlotsRemaining?: number;

  @ApiProperty({
    description: 'Can perform more searches this month',
    example: true,
  })
  canSearchThisMonth: boolean;

  @ApiProperty({
    description: 'Can perform more searches this year',
    example: false,
  })
  canSearchThisYear: boolean;

  @ApiProperty({
    description: 'Can track more properties',
    example: true,
  })
  canTrackMoreProperties: boolean;

  @ApiProperty({
    description: 'Last search reset date',
    example: '2024-01-01T00:00:00.000Z',
  })
  lastSearchReset: Date;

  @ApiProperty({
    description: 'Last yearly reset date',
    example: '2024-01-01T00:00:00.000Z',
  })
  lastYearlyReset: Date;
}

export class UsageHistoryDto {
  @ApiProperty({
    description: 'Usage history ID',
    example: 'clp1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: 'clp1234567890',
  })
  userId: string;

  @ApiProperty({
    description: 'Usage type',
    enum: UsageType,
    example: UsageType.SEARCH,
  })
  usageType: UsageType;

  @ApiProperty({
    description: 'Usage count',
    example: 1,
  })
  count: number;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { query: 'Toorak VIC', filters: { propertyType: 'HOUSE' } },
  })
  metadata?: any;

  @ApiProperty({
    description: 'Usage date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;
}
