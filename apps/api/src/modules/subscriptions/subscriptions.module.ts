import { Module } from '@nestjs/common';
import { UsageLimitGuard } from './guards/usage-limit.guard';
import { UsageRecordingInterceptor } from './interceptors/usage-recording.interceptor';
import { PlanService } from './plan.service';
import { SubscriptionsController } from './subscriptions.controller';
import { SubscriptionsService } from './subscriptions.service';
import { UsageService } from './usage.service';

@Module({
  controllers: [SubscriptionsController],
  providers: [
    SubscriptionsService,
    UsageService,
    PlanService,
    UsageLimitGuard,
    UsageRecordingInterceptor,
  ],
  exports: [
    SubscriptionsService,
    UsageService,
    PlanService,
    UsageLimitGuard,
    UsageRecordingInterceptor,
  ],
})
export class SubscriptionsModule {}
