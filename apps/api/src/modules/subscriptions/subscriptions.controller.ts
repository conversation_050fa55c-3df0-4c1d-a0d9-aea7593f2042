import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  UseGuards,
  Query,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { User } from '@revalu/database';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { SubscriptionsService } from './subscriptions.service';
import { UsageService } from './usage.service';
import { PlanService } from './plan.service';
import {
  CreateSubscriptionDto,
  UpdateSubscriptionDto,
  SubscriptionResponseDto,
  PlanLimitsDto,
} from './dto/subscription.dto';
import {
  RecordUsageDto,
  UsageStatsDto,
  UsageHistoryDto,
} from './dto/usage.dto';
import { UsageType, PlanType } from '@revalu/database';

@ApiTags('Subscriptions')
@Controller('subscriptions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class SubscriptionsController {
  constructor(
    private subscriptionsService: SubscriptionsService,
    private usageService: UsageService,
    private planService: PlanService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new subscription' })
  @ApiResponse({
    status: 201,
    description: 'Subscription created successfully',
    type: SubscriptionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - User already has subscription' })
  async createSubscription(
    @CurrentUser() user: User,
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    return this.subscriptionsService.createSubscription(user.id, createSubscriptionDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get current user subscription' })
  @ApiResponse({
    status: 200,
    description: 'Current subscription details',
    type: SubscriptionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async getSubscription(@CurrentUser() user: User): Promise<SubscriptionResponseDto | null> {
    return this.subscriptionsService.getSubscription(user.id);
  }

  @Put()
  @ApiOperation({ summary: 'Update subscription plan or billing' })
  @ApiResponse({
    status: 200,
    description: 'Subscription updated successfully',
    type: SubscriptionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async updateSubscription(
    @CurrentUser() user: User,
    @Body() updateSubscriptionDto: UpdateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    return this.subscriptionsService.updateSubscription(user.id, updateSubscriptionDto);
  }

  @Delete()
  @ApiOperation({ summary: 'Cancel subscription' })
  @ApiResponse({ status: 200, description: 'Subscription cancelled successfully' })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async cancelSubscription(@CurrentUser() user: User): Promise<{ message: string }> {
    await this.subscriptionsService.cancelSubscription(user.id);
    return { message: 'Subscription cancelled successfully' };
  }

  @Get('plans')
  @ApiOperation({ summary: 'Get all available plans' })
  @ApiResponse({
    status: 200,
    description: 'List of all available plans',
  })
  async getPlans() {
    return this.planService.getAllPlanConfigs();
  }

  @Get('plans/:planType')
  @ApiOperation({ summary: 'Get specific plan details' })
  @ApiResponse({
    status: 200,
    description: 'Plan details and limits',
    type: PlanLimitsDto,
  })
  async getPlanDetails(@Param('planType') planType: PlanType): Promise<PlanLimitsDto> {
    return this.planService.getPlanLimits(planType);
  }

  @Get('usage/stats')
  @ApiOperation({ summary: 'Get current user usage statistics' })
  @ApiResponse({
    status: 200,
    description: 'Current usage statistics',
    type: UsageStatsDto,
  })
  async getUsageStats(@CurrentUser() user: User): Promise<UsageStatsDto> {
    return this.usageService.getUserUsageStats(user.id);
  }

  @Get('usage/history')
  @ApiOperation({ summary: 'Get user usage history' })
  @ApiQuery({ name: 'usageType', enum: UsageType, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Number of records to return (default: 50)' })
  @ApiResponse({
    status: 200,
    description: 'Usage history records',
    type: [UsageHistoryDto],
  })
  async getUsageHistory(
    @CurrentUser() user: User,
    @Query('usageType') usageType?: UsageType,
    @Query('limit') limit?: number,
  ): Promise<UsageHistoryDto[]> {
    return this.usageService.getUserUsageHistory(user.id, usageType, limit);
  }

  @Post('usage/record')
  @ApiOperation({ summary: 'Record usage (internal use)' })
  @ApiResponse({ status: 201, description: 'Usage recorded successfully' })
  @ApiResponse({ status: 403, description: 'Usage limit exceeded' })
  async recordUsage(
    @CurrentUser() user: User,
    @Body() recordUsageDto: RecordUsageDto,
  ): Promise<{ message: string }> {
    await this.usageService.recordUsage(user.id, recordUsageDto);
    return { message: 'Usage recorded successfully' };
  }

  @Get('usage/can-perform/:usageType')
  @ApiOperation({ summary: 'Check if user can perform specific action' })
  @ApiResponse({
    status: 200,
    description: 'Whether user can perform the action',
    schema: {
      type: 'object',
      properties: {
        canPerform: { type: 'boolean' },
        reason: { type: 'string' },
      },
    },
  })
  async canPerformAction(
    @CurrentUser() user: User,
    @Param('usageType') usageType: UsageType,
    @Query('count') count?: number,
  ): Promise<{ canPerform: boolean; reason?: string }> {
    const canPerform = await this.usageService.canPerformAction(
      user.id,
      usageType,
      count ? parseInt(count.toString()) : 1,
    );

    let reason: string | undefined;
    if (!canPerform) {
      const stats = await this.usageService.getUserUsageStats(user.id);
      switch (usageType) {
        case UsageType.SEARCH:
          if (stats.monthlySearchLimit && stats.monthlySearchCount >= stats.monthlySearchLimit) {
            reason = 'Monthly search limit exceeded';
          } else if (stats.yearlySearchLimit && stats.yearlySearchCount >= stats.yearlySearchLimit) {
            reason = 'Yearly search limit exceeded';
          }
          break;
        case UsageType.PROPERTY_TRACK:
          reason = 'Property tracking limit exceeded';
          break;
        default:
          reason = 'Usage limit exceeded';
      }
    }

    return { canPerform, reason };
  }
}
