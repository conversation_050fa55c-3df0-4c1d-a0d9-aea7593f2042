import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../common/database/prisma.service';
import { PlanType, SubscriptionStatus, BillingInterval } from '@revalu/database';
import { PlanService } from './plan.service';
import {
  CreateSubscriptionDto,
  UpdateSubscriptionDto,
  SubscriptionResponseDto,
} from './dto/subscription.dto';

@Injectable()
export class SubscriptionsService {
  private readonly logger = new Logger(SubscriptionsService.name);

  constructor(
    private prisma: PrismaService,
    private planService: PlanService,
  ) {}

  async createSubscription(
    userId: string,
    createSubscriptionDto: CreateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    const { planType, billingInterval, paymentMethodId } = createSubscriptionDto;

    try {
      // Check if user already has a subscription
      const existingSubscription = await this.prisma.client.subscription.findUnique({
        where: { userId },
      });

      if (existingSubscription) {
        throw new BadRequestException('User already has an active subscription');
      }

      // Get plan configuration
      const planConfig = this.planService.getPlanConfig(planType);
      const planLimits = this.planService.getPlanLimits(planType);

      // Calculate pricing
      const pricePerMonth = planConfig.pricePerMonth;
      const currentPeriodStart = new Date();
      const currentPeriodEnd = new Date();

      if (billingInterval === BillingInterval.MONTHLY) {
        currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
      } else {
        currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1);
      }

      // Create subscription
      const subscription = await this.prisma.client.subscription.create({
        data: {
          userId,
          planType,
          status:
            planType === PlanType.REVALU_ACCESS
              ? SubscriptionStatus.ACTIVE
              : SubscriptionStatus.TRIALING,
          billingInterval,
          pricePerMonth,
          currency: 'AUD',
          currentPeriodStart,
          currentPeriodEnd,
          trialStart: planType !== PlanType.REVALU_ACCESS ? currentPeriodStart : null,
          trialEnd:
            planType !== PlanType.REVALU_ACCESS
              ? new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)
              : null, // 14 days trial

          // Cache plan limits for performance
          monthlySearchLimit: planLimits.monthlySearchLimit,
          yearlySearchLimit: planLimits.yearlySearchLimit,
          propertyTrackLimit: planLimits.propertyTrackLimit,
          teamMemberLimit: planLimits.teamMemberLimit,

          // Cache features
          canExportReports: planLimits.canExportReports,
          canAccessCMA: planLimits.canAccessCMA,
          canWhiteLabel: planLimits.canWhiteLabel,
          canAccessAPI: planLimits.canAccessAPI,
          hasPrioritySupport: planLimits.hasPrioritySupport,
        },
      });

      // Update user's current plan
      await this.prisma.client.user.update({
        where: { id: userId },
        data: {
          currentPlan: planType,
          planStartDate: currentPeriodStart,
          planEndDate: currentPeriodEnd,
          isTrialActive: planType !== PlanType.REVALU_ACCESS,
          trialEndDate: subscription.trialEnd,
        },
      });

      this.logger.log(`Created subscription for user ${userId} with plan ${planType}`);

      return this.transformSubscriptionResponse(subscription);
    } catch (error) {
      this.logger.error(`Failed to create subscription for user ${userId}:`, error);
      throw error;
    }
  }

  async getSubscription(userId: string): Promise<SubscriptionResponseDto | null> {
    try {
      const subscription = await this.prisma.client.subscription.findUnique({
        where: { userId },
      });

      if (!subscription) {
        return null;
      }

      return this.transformSubscriptionResponse(subscription);
    } catch (error) {
      this.logger.error(`Failed to get subscription for user ${userId}:`, error);
      throw error;
    }
  }

  async updateSubscription(
    userId: string,
    updateSubscriptionDto: UpdateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    const { planType, billingInterval } = updateSubscriptionDto;

    try {
      const existingSubscription = await this.prisma.client.subscription.findUnique({
        where: { userId },
      });

      if (!existingSubscription) {
        throw new NotFoundException('Subscription not found');
      }

      const updateData: any = {};

      if (planType && planType !== existingSubscription.planType) {
        // Plan change
        const planConfig = this.planService.getPlanConfig(planType);
        const planLimits = this.planService.getPlanLimits(planType);

        updateData.planType = planType;
        updateData.pricePerMonth = planConfig.pricePerMonth;

        // Update cached limits and features
        updateData.monthlySearchLimit = planLimits.monthlySearchLimit;
        updateData.yearlySearchLimit = planLimits.yearlySearchLimit;
        updateData.propertyTrackLimit = planLimits.propertyTrackLimit;
        updateData.teamMemberLimit = planLimits.teamMemberLimit;
        updateData.canExportReports = planLimits.canExportReports;
        updateData.canAccessCMA = planLimits.canAccessCMA;
        updateData.canWhiteLabel = planLimits.canWhiteLabel;
        updateData.canAccessAPI = planLimits.canAccessAPI;
        updateData.hasPrioritySupport = planLimits.hasPrioritySupport;

        // Update user's current plan
        await this.prisma.client.user.update({
          where: { id: userId },
          data: { currentPlan: planType },
        });
      }

      if (billingInterval && billingInterval !== existingSubscription.billingInterval) {
        updateData.billingInterval = billingInterval;

        // Recalculate period end based on new billing interval
        const currentPeriodEnd = new Date();
        if (billingInterval === BillingInterval.MONTHLY) {
          currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
        } else {
          currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1);
        }
        updateData.currentPeriodEnd = currentPeriodEnd;
      }

      const updatedSubscription = await this.prisma.client.subscription.update({
        where: { userId },
        data: updateData,
      });

      this.logger.log(`Updated subscription for user ${userId}`);

      return this.transformSubscriptionResponse(updatedSubscription);
    } catch (error) {
      this.logger.error(`Failed to update subscription for user ${userId}:`, error);
      throw error;
    }
  }

  async cancelSubscription(userId: string): Promise<void> {
    try {
      const subscription = await this.prisma.client.subscription.findUnique({
        where: { userId },
      });

      if (!subscription) {
        throw new NotFoundException('Subscription not found');
      }

      await this.prisma.client.subscription.update({
        where: { userId },
        data: {
          status: SubscriptionStatus.CANCELLED,
          cancelledAt: new Date(),
        },
      });

      // Downgrade user to free plan at the end of current period
      // For now, we'll keep their current plan until period ends
      this.logger.log(`Cancelled subscription for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to cancel subscription for user ${userId}:`, error);
      throw error;
    }
  }

  async checkAndUpdateExpiredSubscriptions(): Promise<void> {
    try {
      const now = new Date();

      // Find expired subscriptions
      const expiredSubscriptions = await this.prisma.client.subscription.findMany({
        where: {
          currentPeriodEnd: { lt: now },
          status: { in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING] },
        },
      });

      for (const subscription of expiredSubscriptions) {
        // Downgrade to free plan
        await this.prisma.client.subscription.update({
          where: { id: subscription.id },
          data: { status: SubscriptionStatus.EXPIRED },
        });

        await this.prisma.client.user.update({
          where: { id: subscription.userId },
          data: {
            currentPlan: PlanType.REVALU_ACCESS,
            isTrialActive: false,
          },
        });

        this.logger.log(`Expired subscription for user ${subscription.userId}`);
      }
    } catch (error) {
      this.logger.error('Failed to check and update expired subscriptions:', error);
    }
  }

  private transformSubscriptionResponse(subscription: any): SubscriptionResponseDto {
    return {
      id: subscription.id,
      userId: subscription.userId,
      planType: subscription.planType,
      status: subscription.status,
      billingInterval: subscription.billingInterval,
      pricePerMonth: subscription.pricePerMonth,
      currency: subscription.currency,
      currentPeriodStart: subscription.currentPeriodStart,
      currentPeriodEnd: subscription.currentPeriodEnd,
      trialEnd: subscription.trialEnd,
      monthlySearchLimit: subscription.monthlySearchLimit,
      yearlySearchLimit: subscription.yearlySearchLimit,
      propertyTrackLimit: subscription.propertyTrackLimit,
      canExportReports: subscription.canExportReports,
      canAccessCMA: subscription.canAccessCMA,
      canWhiteLabel: subscription.canWhiteLabel,
      canAccessAPI: subscription.canAccessAPI,
      hasPrioritySupport: subscription.hasPrioritySupport,
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt,
    };
  }
}
