import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { UsageService } from '../usage.service';

@Injectable()
export class UsageRecordingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(UsageRecordingInterceptor.name);

  constructor(private usageService: UsageService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const usageToRecord = request.usageToRecord;

    return next.handle().pipe(
      tap(async () => {
        // Record usage after successful request
        if (user && usageToRecord) {
          try {
            await this.usageService.recordUsage(user.id, {
              usageType: usageToRecord.type,
              count: usageToRecord.count,
              metadata: {
                endpoint: request.route?.path,
                method: request.method,
                timestamp: new Date().toISOString(),
              },
            });
          } catch (error) {
            this.logger.error(
              `Failed to record usage for user ${user.id}:`,
              error,
            );
            // Don't throw error here to avoid breaking the main request
          }
        }
      }),
    );
  }
}
