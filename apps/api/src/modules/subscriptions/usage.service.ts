import { Injectable, Logger, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../common/database/prisma.service';
import { UsageType, PlanType } from '@revalu/database';
import { PlanService } from './plan.service';
import { RecordUsageDto, UsageStatsDto, UsageHistoryDto } from './dto/usage.dto';

@Injectable()
export class UsageService {
  private readonly logger = new Logger(UsageService.name);

  constructor(
    private prisma: PrismaService,
    private planService: PlanService,
  ) {}

  async recordUsage(userId: string, recordUsageDto: RecordUsageDto): Promise<void> {
    const { usageType, count = 1, metadata } = recordUsageDto;

    try {
      // Get user's current plan and usage stats
      const user = await this.prisma.client.user.findUnique({
        where: { id: userId },
        include: { subscription: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Check if user can perform this action
      const canPerform = await this.canPerformAction(userId, usageType, count);
      if (!canPerform) {
        throw new ForbiddenException(`Usage limit exceeded for ${usageType}`);
      }

      // Record usage in history
      await this.prisma.client.usageHistory.create({
        data: {
          userId,
          usageType,
          count,
          metadata,
        },
      });

      // Update user's usage counters
      await this.updateUserUsageCounters(userId, usageType, count);

      this.logger.log(`Recorded ${usageType} usage for user ${userId}: ${count}`);
    } catch (error) {
      this.logger.error(`Failed to record usage for user ${userId}:`, error);
      throw error;
    }
  }

  async canPerformAction(userId: string, usageType: UsageType, count: number = 1): Promise<boolean> {
    try {
      const stats = await this.getUserUsageStats(userId);
      
      switch (usageType) {
        case UsageType.SEARCH:
          // Check both monthly and yearly limits for freemium users
          if (stats.monthlySearchLimit !== undefined && stats.monthlySearchLimit !== -1) {
            if (stats.monthlySearchCount + count > stats.monthlySearchLimit) {
              return false;
            }
          }
          if (stats.yearlySearchLimit !== undefined && stats.yearlySearchLimit !== -1) {
            if (stats.yearlySearchCount + count > stats.yearlySearchLimit) {
              return false;
            }
          }
          return true;

        case UsageType.PROPERTY_TRACK:
          if (stats.propertyTrackLimit !== undefined && stats.propertyTrackLimit !== -1) {
            return stats.trackedPropertiesCount + count <= stats.propertyTrackLimit;
          }
          return true;

        default:
          return true;
      }
    } catch (error) {
      this.logger.error(`Error checking usage permissions for user ${userId}:`, error);
      return false;
    }
  }

  async getUserUsageStats(userId: string): Promise<UsageStatsDto> {
    try {
      const user = await this.prisma.client.user.findUnique({
        where: { id: userId },
        include: { subscription: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Get plan limits
      const planLimits = this.planService.getPlanLimits(user.currentPlan);

      // Check if we need to reset counters
      await this.checkAndResetCounters(userId, user);

      // Refresh user data after potential reset
      const updatedUser = await this.prisma.client.user.findUnique({
        where: { id: userId },
      });

      const monthlySearchesRemaining = this.planService.getRemainingUsage(
        updatedUser.monthlySearchCount,
        planLimits.monthlySearchLimit,
      );

      const yearlySearchesRemaining = this.planService.getRemainingUsage(
        updatedUser.yearlySearchCount,
        planLimits.yearlySearchLimit,
      );

      const propertyTrackingSlotsRemaining = this.planService.getRemainingUsage(
        updatedUser.trackedPropertiesCount,
        planLimits.propertyTrackLimit,
      );

      return {
        userId: updatedUser.id,
        monthlySearchCount: updatedUser.monthlySearchCount,
        yearlySearchCount: updatedUser.yearlySearchCount,
        trackedPropertiesCount: updatedUser.trackedPropertiesCount,
        monthlySearchLimit: planLimits.monthlySearchLimit,
        yearlySearchLimit: planLimits.yearlySearchLimit,
        propertyTrackLimit: planLimits.propertyTrackLimit,
        monthlySearchesRemaining,
        yearlySearchesRemaining,
        propertyTrackingSlotsRemaining,
        canSearchThisMonth: this.planService.canPerformAction(
          updatedUser.monthlySearchCount,
          planLimits.monthlySearchLimit,
        ),
        canSearchThisYear: this.planService.canPerformAction(
          updatedUser.yearlySearchCount,
          planLimits.yearlySearchLimit,
        ),
        canTrackMoreProperties: this.planService.canPerformAction(
          updatedUser.trackedPropertiesCount,
          planLimits.propertyTrackLimit,
        ),
        lastSearchReset: updatedUser.lastSearchReset,
        lastYearlyReset: updatedUser.lastYearlyReset,
      };
    } catch (error) {
      this.logger.error(`Failed to get usage stats for user ${userId}:`, error);
      throw error;
    }
  }

  async getUserUsageHistory(
    userId: string,
    usageType?: UsageType,
    limit: number = 50,
  ): Promise<UsageHistoryDto[]> {
    try {
      const history = await this.prisma.client.usageHistory.findMany({
        where: {
          userId,
          ...(usageType && { usageType }),
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      return history.map(record => ({
        id: record.id,
        userId: record.userId,
        usageType: record.usageType,
        count: record.count,
        metadata: record.metadata,
        createdAt: record.createdAt,
      }));
    } catch (error) {
      this.logger.error(`Failed to get usage history for user ${userId}:`, error);
      throw error;
    }
  }

  private async updateUserUsageCounters(
    userId: string,
    usageType: UsageType,
    count: number,
  ): Promise<void> {
    switch (usageType) {
      case UsageType.SEARCH:
        await this.prisma.client.user.update({
          where: { id: userId },
          data: {
            monthlySearchCount: { increment: count },
            yearlySearchCount: { increment: count },
          },
        });
        break;

      case UsageType.PROPERTY_TRACK:
        await this.prisma.client.user.update({
          where: { id: userId },
          data: {
            trackedPropertiesCount: { increment: count },
          },
        });
        break;

      default:
        // For other usage types, we just record in history
        break;
    }
  }

  private async checkAndResetCounters(userId: string, user: any): Promise<void> {
    const now = new Date();
    const lastReset = new Date(user.lastSearchReset);
    const lastYearlyReset = new Date(user.lastYearlyReset);

    // Reset monthly counter if it's a new month
    if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
      await this.prisma.client.user.update({
        where: { id: userId },
        data: {
          monthlySearchCount: 0,
          lastSearchReset: now,
        },
      });
      this.logger.log(`Reset monthly search count for user ${userId}`);
    }

    // Reset yearly counter if it's a new year
    if (now.getFullYear() !== lastYearlyReset.getFullYear()) {
      await this.prisma.client.user.update({
        where: { id: userId },
        data: {
          yearlySearchCount: 0,
          lastYearlyReset: now,
        },
      });
      this.logger.log(`Reset yearly search count for user ${userId}`);
    }
  }

  async updateTrackedPropertiesCount(userId: string): Promise<void> {
    try {
      const count = await this.prisma.client.savedProperty.count({
        where: { userId },
      });

      await this.prisma.client.user.update({
        where: { id: userId },
        data: { trackedPropertiesCount: count },
      });

      this.logger.log(`Updated tracked properties count for user ${userId}: ${count}`);
    } catch (error) {
      this.logger.error(`Failed to update tracked properties count for user ${userId}:`, error);
      throw error;
    }
  }
}
