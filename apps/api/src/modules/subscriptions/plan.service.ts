import { Injectable } from '@nestjs/common';
import { PlanType } from '@revalu/database';
import { PlanLimitsDto } from './dto/subscription.dto';

export interface PlanConfig {
  name: string;
  description: string;
  pricePerMonth: number; // in cents
  yearlyDiscount?: number; // percentage discount for yearly billing
  limits: {
    monthlySearchLimit?: number;
    yearlySearchLimit?: number;
    propertyTrackLimit?: number;
    teamMemberLimit?: number;
  };
  features: {
    canExportReports: boolean;
    canAccessCMA: boolean;
    canWhiteLabel: boolean;
    canAccessAPI: boolean;
    hasPrioritySupport: boolean;
  };
  ctaMessage?: string;
}

@Injectable()
export class PlanService {
  private readonly planConfigs: Record<PlanType, PlanConfig> = {
    [PlanType.REVALU_ACCESS]: {
      name: 'Revalu Access',
      description: 'Freemium plan with basic property intelligence',
      pricePerMonth: 0,
      limits: {
        yearlySearchLimit: 4,
        propertyTrackLimit: 0,
        teamMemberLimit: 1,
      },
      features: {
        canExportReports: false,
        canAccessCMA: false,
        canWhiteLabel: false,
        canAccessAPI: false,
        hasPrioritySupport: false,
      },
      ctaMessage: 'Want to monitor your home more often? Unlock Home+ for $19/month',
    },
    [PlanType.HOME_PLUS]: {
      name: 'Home+',
      description: 'Perfect for homeowners tracking their property and neighborhood',
      pricePerMonth: 1900, // $19.00
      yearlyDiscount: 15,
      limits: {
        monthlySearchLimit: 10,
        propertyTrackLimit: 3,
        teamMemberLimit: 1,
      },
      features: {
        canExportReports: false,
        canAccessCMA: false,
        canWhiteLabel: false,
        canAccessAPI: false,
        hasPrioritySupport: false,
      },
    },
    [PlanType.INVESTOR_PLUS]: {
      name: 'Investor+',
      description: 'Advanced features for property investors and portfolio management',
      pricePerMonth: 4900, // $49.00
      yearlyDiscount: 15,
      limits: {
        monthlySearchLimit: 20,
        propertyTrackLimit: 5,
        teamMemberLimit: 1,
      },
      features: {
        canExportReports: true,
        canAccessCMA: false,
        canWhiteLabel: false,
        canAccessAPI: false,
        hasPrioritySupport: false,
      },
    },
    [PlanType.AGENT_PRO]: {
      name: 'Agent Pro',
      description: 'Professional tools for real estate agents and brokers',
      pricePerMonth: 14900, // $149.00
      yearlyDiscount: 20,
      limits: {
        monthlySearchLimit: 100,
        propertyTrackLimit: 20,
        teamMemberLimit: 1,
      },
      features: {
        canExportReports: true,
        canAccessCMA: true,
        canWhiteLabel: false,
        canAccessAPI: false,
        hasPrioritySupport: true,
      },
    },
    [PlanType.TEAM_PLAN]: {
      name: 'Team Plan',
      description: 'Collaboration tools for real estate teams and agencies',
      pricePerMonth: 39900, // $399.00
      yearlyDiscount: 20,
      limits: {
        monthlySearchLimit: -1, // Unlimited
        propertyTrackLimit: -1, // Unlimited
        teamMemberLimit: 5,
      },
      features: {
        canExportReports: true,
        canAccessCMA: true,
        canWhiteLabel: true,
        canAccessAPI: false,
        hasPrioritySupport: true,
      },
    },
    [PlanType.ENTERPRISE]: {
      name: 'Enterprise',
      description: 'Custom solutions for large organizations and integrations',
      pricePerMonth: 0, // Custom pricing
      limits: {
        monthlySearchLimit: -1, // Unlimited
        propertyTrackLimit: -1, // Unlimited
        teamMemberLimit: -1, // Unlimited
      },
      features: {
        canExportReports: true,
        canAccessCMA: true,
        canWhiteLabel: true,
        canAccessAPI: true,
        hasPrioritySupport: true,
      },
    },
  };

  getPlanConfig(planType: PlanType): PlanConfig {
    return this.planConfigs[planType];
  }

  getAllPlanConfigs(): Record<PlanType, PlanConfig> {
    return this.planConfigs;
  }

  getPlanLimits(planType: PlanType): PlanLimitsDto {
    const config = this.getPlanConfig(planType);
    return {
      planType,
      monthlySearchLimit: config.limits.monthlySearchLimit,
      yearlySearchLimit: config.limits.yearlySearchLimit,
      propertyTrackLimit: config.limits.propertyTrackLimit,
      teamMemberLimit: config.limits.teamMemberLimit,
      canExportReports: config.features.canExportReports,
      canAccessCMA: config.features.canAccessCMA,
      canWhiteLabel: config.features.canWhiteLabel,
      canAccessAPI: config.features.canAccessAPI,
      hasPrioritySupport: config.features.hasPrioritySupport,
    };
  }

  calculateYearlyPrice(planType: PlanType): number {
    const config = this.getPlanConfig(planType);
    const monthlyPrice = config.pricePerMonth;
    const yearlyPrice = monthlyPrice * 12;

    if (config.yearlyDiscount) {
      return Math.round(yearlyPrice * (1 - config.yearlyDiscount / 100));
    }

    return yearlyPrice;
  }

  isUnlimited(limit: number | undefined): boolean {
    return limit === -1 || limit === undefined;
  }

  canPerformAction(currentUsage: number, limit: number | undefined): boolean {
    if (this.isUnlimited(limit)) {
      return true;
    }
    return currentUsage < (limit || 0);
  }

  getRemainingUsage(currentUsage: number, limit: number | undefined): number {
    if (this.isUnlimited(limit)) {
      return -1; // Unlimited
    }
    return Math.max(0, (limit || 0) - currentUsage);
  }

  getUpgradeRecommendation(currentPlan: PlanType, usageStats: any): PlanType | null {
    // Simple upgrade logic - can be enhanced based on usage patterns
    if (currentPlan === PlanType.REVALU_ACCESS) {
      if (usageStats.yearlySearchCount >= 4) {
        return PlanType.HOME_PLUS;
      }
    }

    if (currentPlan === PlanType.HOME_PLUS) {
      if (usageStats.monthlySearchCount >= 10 || usageStats.trackedPropertiesCount >= 3) {
        return PlanType.INVESTOR_PLUS;
      }
    }

    if (currentPlan === PlanType.INVESTOR_PLUS) {
      if (usageStats.monthlySearchCount >= 20 || usageStats.trackedPropertiesCount >= 5) {
        return PlanType.AGENT_PRO;
      }
    }

    return null;
  }
}
