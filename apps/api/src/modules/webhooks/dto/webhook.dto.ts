import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsEnum, Is<PERSON>rray, IsN<PERSON>ber, IsBoolean } from 'class-validator';

export enum DataSourceType {
  PROPERTY_SALES = 'PROPERTY_SALES',
  CURRENT_LISTINGS = 'CURRENT_LISTINGS',
  PLANNING_ZONING = 'PLANNING_ZONING',
  DEVELOPMENT_APPLICATIONS = 'DEVELOPMENT_APPLICATIONS',
  ECONOMIC_INDICATORS = 'ECONOMIC_INDICATORS',
  INFRASTRUCTURE_PROJECTS = 'INFRASTRUCTURE_PROJECTS',
  SCHOOL_PERFORMANCE = 'SCHOOL_PERFORMANCE',
  CRIME_STATISTICS = 'CRIME_STATISTICS',
  RENTAL_MARKET = 'RENTAL_MARKET',
  TRANSPORT_ACCESSIBILITY = 'TRANSPORT_ACCESSIBILITY',
  CLIMATE_RISK = 'CLIMATE_RISK',
  DEMOGRAPHICS = 'DEMOGRAPHICS',
  CONSTRUCTION_COSTS = 'CONSTRUCTION_COSTS',
  MARKET_SENTIMENT = 'MARKET_SENTIMENT',
  ENERGY_EFFICIENCY = 'ENERGY_EFFICIENCY',
  MORTGAGE_STRESS = 'MORTGAGE_STRESS',
  BUSINESS_ACTIVITY = 'BUSINESS_ACTIVITY',
  SOCIAL_TRENDS = 'SOCIAL_TRENDS',
}

export class PropertyDataWebhookDto {
  @ApiProperty({ description: 'Data source identifier' })
  @IsString()
  source: string;

  @ApiProperty({ enum: DataSourceType, description: 'Type of data source' })
  @IsEnum(DataSourceType)
  sourceType: DataSourceType;

  @ApiProperty({ description: 'Event type (created, updated, deleted)' })
  @IsString()
  eventType: string;

  @ApiProperty({ description: 'Property address or identifier' })
  @IsString()
  propertyIdentifier: string;

  @ApiPropertyOptional({ description: 'Suburb identifier' })
  @IsOptional()
  @IsString()
  suburbIdentifier?: string;

  @ApiProperty({ description: 'Raw data payload' })
  @IsObject()
  data: {
    // Property details
    address?: string;
    latitude?: number;
    longitude?: number;
    propertyType?: string;
    bedrooms?: number;
    bathrooms?: number;
    landSize?: number;
    buildingSize?: number;
    yearBuilt?: number;
    
    // Valuation data
    estimatedValue?: number;
    confidence?: number;
    valuationMethod?: string;
    
    // Market data
    salePrice?: number;
    saleDate?: string;
    listingPrice?: number;
    listingDate?: string;
    
    // Event-Uplift factors
    upliftFactors?: Array<{
      type: string;
      name: string;
      impact: number;
      confidence: number;
      timeHorizon: number;
    }>;
    
    // Intelligence scores
    investmentScore?: number;
    developmentScore?: number;
    growthPotential?: number;
    riskScore?: number;
    
    // Additional metadata
    metadata?: any;
  };

  @ApiProperty({ description: 'Timestamp of the data' })
  @IsString()
  timestamp: string;

  @ApiPropertyOptional({ description: 'Data quality score (0-1)' })
  @IsOptional()
  @IsNumber()
  qualityScore?: number;
}

export class MarketDataWebhookDto {
  @ApiProperty({ description: 'Data source identifier' })
  @IsString()
  source: string;

  @ApiProperty({ enum: DataSourceType, description: 'Type of data source' })
  @IsEnum(DataSourceType)
  sourceType: DataSourceType;

  @ApiProperty({ description: 'Event type' })
  @IsString()
  eventType: string;

  @ApiProperty({ description: 'Geographic area (suburb, postcode, etc.)' })
  @IsString()
  areaIdentifier: string;

  @ApiProperty({ description: 'Market data payload' })
  @IsObject()
  data: {
    // Market trends
    medianPrice?: number;
    averagePrice?: number;
    priceChange?: number;
    priceChangePercent?: number;
    salesVolume?: number;
    daysOnMarket?: number;
    
    // Infrastructure updates
    infrastructureProjects?: Array<{
      name: string;
      type: string;
      status: string;
      budget?: number;
      startDate?: string;
      endDate?: string;
      impact?: string;
    }>;
    
    // Development applications
    developmentApplications?: Array<{
      applicationId: string;
      address: string;
      type: string;
      status: string;
      submissionDate: string;
      estimatedValue?: number;
    }>;
    
    // School updates
    schoolUpdates?: Array<{
      schoolName: string;
      oldRating?: number;
      newRating?: number;
      changeDate: string;
    }>;
    
    // Crime statistics
    crimeStats?: Array<{
      type: string;
      incidents: number;
      period: string;
      rate?: number;
    }>;
    
    // Economic indicators
    economicIndicators?: {
      unemploymentRate?: number;
      medianIncome?: number;
      populationGrowth?: number;
      businessActivity?: number;
    };
    
    // Additional data
    metadata?: any;
  };

  @ApiProperty({ description: 'Timestamp of the data' })
  @IsString()
  timestamp: string;
}

export class WebhookResponseDto {
  @ApiProperty({ description: 'Processing status' })
  status: 'success' | 'error' | 'partial';

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Processed record ID' })
  recordId?: string;

  @ApiProperty({ description: 'Processing details' })
  details?: {
    propertiesUpdated?: number;
    suburbsUpdated?: number;
    alertsTriggered?: number;
    errorsEncountered?: string[];
  };
}

export class DataSourceStatusDto {
  @ApiProperty({ description: 'Data source name' })
  name: string;

  @ApiProperty({ enum: DataSourceType, description: 'Source type' })
  type: DataSourceType;

  @ApiProperty({ description: 'Current status' })
  status: 'active' | 'inactive' | 'error' | 'maintenance';

  @ApiProperty({ description: 'Last sync timestamp' })
  lastSync?: Date;

  @ApiProperty({ description: 'Confidence score' })
  confidence?: number;

  @ApiProperty({ description: 'Records processed in last 24 hours' })
  recentRecords: number;

  @ApiProperty({ description: 'Error rate percentage' })
  errorRate: number;
}
