import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';

@WebSocketGateway({
  namespace: '/property',
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
})
export class PropertyWebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private logger = new Logger('PropertyWebSocketGateway');

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('subscribe-property')
  handleSubscribeProperty(
    @MessageBody() data: { propertyId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const room = `property-${data.propertyId}`;
    client.join(room);
    this.logger.log(`Client ${client.id} subscribed to property ${data.propertyId}`);

    client.emit('subscription-confirmed', {
      type: 'property',
      id: data.propertyId,
      room,
    });
  }

  @SubscribeMessage('unsubscribe-property')
  handleUnsubscribeProperty(
    @MessageBody() data: { propertyId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const room = `property-${data.propertyId}`;
    client.leave(room);
    this.logger.log(`Client ${client.id} unsubscribed from property ${data.propertyId}`);
  }

  // Method to broadcast property updates
  broadcastPropertyUpdate(propertyId: string, update: any) {
    const room = `property-${propertyId}`;
    this.server.to(room).emit('property-update', {
      propertyId,
      timestamp: new Date(),
      data: update,
    });
    this.logger.log(`Broadcasted update for property ${propertyId} to room ${room}`);
  }

  // Method to broadcast valuation updates
  broadcastValuationUpdate(propertyId: string, valuation: any) {
    const room = `property-${propertyId}`;
    this.server.to(room).emit('valuation-update', {
      propertyId,
      timestamp: new Date(),
      valuation,
    });
    this.logger.log(`Broadcasted valuation update for property ${propertyId}`);
  }

  // Method to broadcast intelligence score updates
  broadcastIntelligenceUpdate(propertyId: string, intelligence: any) {
    const room = `property-${propertyId}`;
    this.server.to(room).emit('intelligence-update', {
      propertyId,
      timestamp: new Date(),
      intelligence,
    });
    this.logger.log(`Broadcasted intelligence update for property ${propertyId}`);
  }

  // Method to broadcast alert notifications
  broadcastAlert(propertyId: string, alert: any) {
    const room = `property-${propertyId}`;
    this.server.to(room).emit('alert-triggered', {
      propertyId,
      timestamp: new Date(),
      alert,
    });
    this.logger.log(`Broadcasted alert for property ${propertyId}`);
  }
}
