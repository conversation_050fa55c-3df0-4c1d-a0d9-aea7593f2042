import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';

@WebSocketGateway({
  namespace: '/market',
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
})
export class MarketWebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private logger = new Logger('MarketWebSocketGateway');

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('subscribe-suburb')
  handleSubscribeSuburb(
    @MessageBody() data: { suburbId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const room = `suburb-${data.suburbId}`;
    client.join(room);
    this.logger.log(`Client ${client.id} subscribed to suburb ${data.suburbId}`);
    
    client.emit('subscription-confirmed', {
      type: 'suburb',
      id: data.suburbId,
      room,
    });
  }

  @SubscribeMessage('subscribe-market-movers')
  handleSubscribeMarketMovers(@ConnectedSocket() client: Socket) {
    const room = 'market-movers';
    client.join(room);
    this.logger.log(`Client ${client.id} subscribed to market movers`);
    
    client.emit('subscription-confirmed', {
      type: 'market-movers',
      room,
    });
  }

  @SubscribeMessage('unsubscribe-suburb')
  handleUnsubscribeSuburb(
    @MessageBody() data: { suburbId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const room = `suburb-${data.suburbId}`;
    client.leave(room);
    this.logger.log(`Client ${client.id} unsubscribed from suburb ${data.suburbId}`);
  }

  @SubscribeMessage('unsubscribe-market-movers')
  handleUnsubscribeMarketMovers(@ConnectedSocket() client: Socket) {
    const room = 'market-movers';
    client.leave(room);
    this.logger.log(`Client ${client.id} unsubscribed from market movers`);
  }

  // Method to broadcast market trend updates
  broadcastMarketTrend(suburbId: string, trend: any) {
    const room = `suburb-${suburbId}`;
    this.server.to(room).emit('market-trend-update', {
      suburbId,
      timestamp: new Date(),
      trend,
    });
    this.logger.log(`Broadcasted market trend for suburb ${suburbId}`);
  }

  // Method to broadcast infrastructure updates
  broadcastInfrastructureUpdate(suburbId: string, infrastructure: any) {
    const room = `suburb-${suburbId}`;
    this.server.to(room).emit('infrastructure-update', {
      suburbId,
      timestamp: new Date(),
      infrastructure,
    });
    this.logger.log(`Broadcasted infrastructure update for suburb ${suburbId}`);
  }

  // Method to broadcast development application updates
  broadcastDevelopmentUpdate(suburbId: string, development: any) {
    const room = `suburb-${suburbId}`;
    this.server.to(room).emit('development-update', {
      suburbId,
      timestamp: new Date(),
      development,
    });
    this.logger.log(`Broadcasted development update for suburb ${suburbId}`);
  }

  // Method to broadcast market movers updates
  broadcastMarketMovers(data: any) {
    const room = 'market-movers';
    this.server.to(room).emit('market-movers-update', {
      timestamp: new Date(),
      data,
    });
    this.logger.log(`Broadcasted market movers update`);
  }

  // Method to broadcast general market alerts
  broadcastMarketAlert(suburbId: string, alert: any) {
    const room = `suburb-${suburbId}`;
    this.server.to(room).emit('market-alert', {
      suburbId,
      timestamp: new Date(),
      alert,
    });
    this.logger.log(`Broadcasted market alert for suburb ${suburbId}`);
  }
}
