import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import {
  ApiSuccessResponse,
  ApiErrorResponse,
} from '../../common/decorators/api-response.decorator';
import { AmalgamationService } from './amalgamation.service';
import {
  CreateAmalgamationDto,
  AmalgamationAnalysisDto,
  AmalgamationResponseDto,
  AmalgamationListDto,
  UpdateAmalgamationDto,
} from './dto/amalgamation.dto';

@ApiTags('amalgamation')
@Controller('amalgamation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AmalgamationController {
  constructor(private readonly amalgamationService: AmalgamationService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new amalgamation analysis' })
  @ApiSuccessResponse(Object, 'Amalgamation created successfully')
  @ApiErrorResponse(400, 'Invalid properties or properties not in same area')
  @ApiErrorResponse(404, 'One or more properties not found')
  async create(@CurrentUser('id') userId: string, @Body() dto: CreateAmalgamationDto) {
    return this.amalgamationService.create(userId, dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all amalgamations for current user' })
  @ApiSuccessResponse(AmalgamationListDto, 'Amalgamations retrieved successfully')
  async findAll(@CurrentUser('id') userId: string): Promise<AmalgamationListDto> {
    return this.amalgamationService.findAllByUser(userId);
  }

  @Post('analyze')
  @ApiOperation({ summary: 'Perform detailed amalgamation analysis' })
  @ApiSuccessResponse(AmalgamationResponseDto, 'Analysis completed successfully')
  @ApiErrorResponse(404, 'Amalgamation not found')
  async analyze(@Body() dto: AmalgamationAnalysisDto): Promise<AmalgamationResponseDto> {
    return this.amalgamationService.analyze(dto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an amalgamation' })
  @ApiSuccessResponse(Object, 'Amalgamation updated successfully')
  @ApiErrorResponse(404, 'Amalgamation not found')
  async update(
    @Param('id') id: string,
    @CurrentUser('id') userId: string,
    @Body() dto: UpdateAmalgamationDto,
  ) {
    return this.amalgamationService.update(id, userId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an amalgamation' })
  @ApiSuccessResponse(Object, 'Amalgamation deleted successfully')
  @ApiErrorResponse(404, 'Amalgamation not found')
  async delete(@Param('id') id: string, @CurrentUser('id') userId: string) {
    return this.amalgamationService.delete(id, userId);
  }
}
