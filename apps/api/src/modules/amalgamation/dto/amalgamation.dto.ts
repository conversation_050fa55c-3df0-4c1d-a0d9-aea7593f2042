import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsArray, IsOptional, IsNumber, Min } from 'class-validator';

export class CreateAmalgamationDto {
  @ApiProperty({ description: 'Name for the amalgamation analysis' })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Array of property IDs to amalgamate',
    type: [String],
    example: ['prop1', 'prop2', 'prop3'],
  })
  @IsArray()
  @IsString({ each: true })
  propertyIds: string[];

  @ApiPropertyOptional({ description: 'Development type to analyze' })
  @IsOptional()
  @IsString()
  developmentType?: string;

  @ApiPropertyOptional({ description: 'Target number of dwellings' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  targetDwellings?: number;
}

export class AmalgamationAnalysisDto {
  @ApiProperty({ description: 'Amalgamation ID to analyze' })
  @IsString()
  amalgamationId: string;

  @ApiPropertyOptional({ description: 'Include detailed financial analysis' })
  @IsOptional()
  includeFinancials?: boolean;

  @ApiPropertyOptional({ description: 'Include 3D visualization' })
  @IsOptional()
  include3D?: boolean;
}

export class AmalgamationResponseDto {
  @ApiProperty({ description: 'Amalgamation ID' })
  id: string;

  @ApiProperty({ description: 'Amalgamation name' })
  name: string;

  @ApiProperty({ description: 'Array of property IDs' })
  propertyIds: string[];

  @ApiProperty({ description: 'Combined land analysis' })
  landAnalysis: {
    totalLandSize: number;
    averageWidth: number;
    averageDepth: number;
    totalFrontage: number;
    accessibility: string;
    topography: string;
  };

  @ApiProperty({ description: 'Current combined value' })
  currentValue: {
    totalValue: number;
    averagePerSqm: number;
    individualValues: Array<{
      propertyId: string;
      address: string;
      value: number;
      landSize: number;
    }>;
  };

  @ApiProperty({ description: 'Development potential' })
  developmentPotential: {
    maxDwellings: number;
    recommendedDwellings: number;
    developmentScore: number;
    constraints: string[];
    opportunities: string[];
  };

  @ApiProperty({ description: 'Financial analysis' })
  financialAnalysis: {
    acquisitionCost: number;
    developmentCost: number;
    totalInvestment: number;
    estimatedGDV: number; // Gross Development Value
    profit: number;
    roi: number;
    irr: number; // Internal Rate of Return
    paybackPeriod: number;
  };

  @ApiProperty({ description: 'Assembly strategy' })
  assemblyStrategy: {
    acquisitionOrder: Array<{
      propertyId: string;
      priority: number;
      reasoning: string;
      estimatedCost: number;
    }>;
    totalTimeframe: number;
    risks: Array<{
      type: string;
      probability: string;
      impact: string;
      mitigation: string;
    }>;
  };

  @ApiProperty({ description: 'Comparison with individual development' })
  comparison: {
    individualDevelopmentValue: number;
    amalgamatedDevelopmentValue: number;
    valueUplift: number;
    upliftPercentage: number;
  };
}

export class AmalgamationListDto {
  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Array of amalgamations' })
  amalgamations: Array<{
    id: string;
    name: string;
    propertyCount: number;
    totalLandSize: number;
    estimatedValue: number;
    createdAt: Date;
  }>;
}

export class UpdateAmalgamationDto {
  @ApiPropertyOptional({ description: 'Updated name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Updated property IDs' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  propertyIds?: string[];

  @ApiPropertyOptional({ description: 'Development plan updates' })
  @IsOptional()
  developmentPlan?: any;

  @ApiPropertyOptional({ description: 'Feasibility updates' })
  @IsOptional()
  feasibility?: any;
}
