import {
    BadRequestException,
    Injectable,
    Logger,
    NotFoundException,
} from '@nestjs/common';
import {
    buildPropertySearchWhere,
    calculateDistance,
    createPaginationOptions
} from '@revalu/database';
import { PrismaService } from '../../common/database/prisma.service';
import { PaginatedResponseDto } from '../../common/dto/pagination.dto';
import {
    CreatePropertyDto,
    NearbyAmenitiesDto,
    PropertyResponseDto,
    PropertySearchDto,
    PropertyStatsDto,
    RecentSearchesDto,
    UpdatePropertyDto,
    UserSearchHistoryDto,
} from './dto/property.dto';

@Injectable()
export class PropertiesService {
  private readonly logger = new Logger(PropertiesService.name);

  constructor(private prisma: PrismaService) {}

  async create(createPropertyDto: CreatePropertyDto): Promise<PropertyResponseDto> {
    const { suburbId, ...propertyData } = createPropertyDto;

    // Verify suburb exists
    const suburb = await this.prisma.client.suburb.findUnique({
      where: { id: suburbId },
    });

    if (!suburb) {
      throw new BadRequestException('Suburb not found');
    }

    try {
      const property = await this.prisma.client.property.create({
        data: {
          ...propertyData,
          suburbId,
          lastSaleDate: propertyData.lastSaleDate
            ? new Date(propertyData.lastSaleDate)
            : undefined,
        },
        include: {
          suburb: {
            select: {
              id: true,
              name: true,
              state: true,
              postcode: true,
            },
          },
        },
      });

      this.logger.log(`Property created: ${property.id} at ${property.address}`);
      return this.transformPropertyResponse(property);
    } catch (error) {
      this.logger.error('Failed to create property:', error);
      throw new BadRequestException('Failed to create property');
    }
  }

  async findAll(searchDto: PropertySearchDto): Promise<PaginatedResponseDto<PropertyResponseDto>> {
    const { page, limit, sortBy, sortOrder, ...filters } = searchDto;
    const pagination = createPaginationOptions({ page, limit });
    const where = buildPropertySearchWhere(filters);

    // Build order by clause
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder || 'desc';
    } else {
      orderBy.createdAt = 'desc';
    }

    try {
      const [properties, total] = await Promise.all([
        this.prisma.client.property.findMany({
          where,
          include: {
            suburb: {
              select: {
                id: true,
                name: true,
                state: true,
                postcode: true,
              },
            },
          },
          orderBy,
          skip: pagination.skip,
          take: pagination.take,
        }),
        this.prisma.client.property.count({ where }),
      ]);

      const transformedProperties = properties.map(this.transformPropertyResponse);

      return {
        success: true,
        data: transformedProperties,
        meta: {
          total,
          page: pagination.page,
          limit: pagination.limit,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page * pagination.limit < total,
          hasPrev: pagination.page > 1,
        },
        message: 'Properties retrieved successfully',
      };
    } catch (error) {
      this.logger.error('Failed to fetch properties:', error);

      // Return mock data when database is unavailable
      this.logger.warn('Database unavailable, returning mock property data');
      return this.getMockPropertiesResponse(searchDto);
    }
  }

  async findOne(id: string): Promise<PropertyResponseDto> {
    try {
      const property = await this.prisma.client.property.findUnique({
        where: { id },
        include: {
          suburb: {
            select: {
              id: true,
              name: true,
              state: true,
              postcode: true,
            },
          },
          valuations: {
            orderBy: { valuationDate: 'desc' },
            take: 5,
          },
          predictions: {
            orderBy: { predictionDate: 'desc' },
            take: 3,
          },
        },
      });

      if (!property) {
        throw new NotFoundException('Property not found');
      }

      return this.transformPropertyResponse(property);
    } catch (error) {
      this.logger.error(`Failed to fetch property ${id}:`, error);

      // Return mock data when database is unavailable
      this.logger.warn('Database unavailable, returning mock property data');
      return this.getMockPropertyById(id);
    }
  }

  async update(id: string, updatePropertyDto: UpdatePropertyDto): Promise<PropertyResponseDto> {
    // Check if property exists
    const existingProperty = await this.prisma.client.property.findUnique({
      where: { id },
    });

    if (!existingProperty) {
      throw new NotFoundException('Property not found');
    }

    try {
      const property = await this.prisma.client.property.update({
        where: { id },
        data: updatePropertyDto,
        include: {
          suburb: {
            select: {
              id: true,
              name: true,
              state: true,
              postcode: true,
            },
          },
        },
      });

      this.logger.log(`Property updated: ${property.id}`);
      return this.transformPropertyResponse(property);
    } catch (error) {
      this.logger.error(`Failed to update property ${id}:`, error);
      throw new BadRequestException('Failed to update property');
    }
  }

  async remove(id: string): Promise<{ message: string }> {
    // Check if property exists
    const existingProperty = await this.prisma.client.property.findUnique({
      where: { id },
    });

    if (!existingProperty) {
      throw new NotFoundException('Property not found');
    }

    try {
      await this.prisma.client.property.delete({
        where: { id },
      });

      this.logger.log(`Property deleted: ${id}`);
      return { message: 'Property deleted successfully' };
    } catch (error) {
      this.logger.error(`Failed to delete property ${id}:`, error);
      throw new BadRequestException('Failed to delete property');
    }
  }

  async getStatistics(): Promise<PropertyStatsDto> {
    try {
      const [
        totalProperties,
        priceStats,
        typeStats,
        statusStats,
        bedroomStats,
      ] = await Promise.all([
        this.prisma.client.property.count(),
        this.prisma.client.property.aggregate({
          _avg: { lastSalePrice: true },
          _min: { lastSalePrice: true },
          _max: { lastSalePrice: true },
        }),
        this.prisma.client.property.groupBy({
          by: ['propertyType'],
          _count: true,
        }),
        this.prisma.client.property.groupBy({
          by: ['status'],
          _count: true,
        }),
        this.prisma.client.property.groupBy({
          by: ['bedrooms'],
          _count: true,
        }),
      ]);

      // Calculate median price
      const medianPrice = await this.calculateMedianPrice();

      return {
        totalProperties,
        averagePrice: priceStats._avg.lastSalePrice || 0,
        medianPrice,
        priceRange: {
          min: priceStats._min.lastSalePrice || 0,
          max: priceStats._max.lastSalePrice || 0,
        },
        byType: typeStats.reduce((acc, item) => {
          acc[item.propertyType] = item._count;
          return acc;
        }, {}),
        byStatus: statusStats.reduce((acc, item) => {
          acc[item.status] = item._count;
          return acc;
        }, {}),
        byBedrooms: bedroomStats.reduce((acc, item) => {
          acc[item.bedrooms?.toString() || 'unknown'] = item._count;
          return acc;
        }, {}),
      };
    } catch (error) {
      this.logger.error('Failed to get property statistics:', error);

      // Return mock statistics when database is unavailable
      this.logger.warn('Database unavailable, returning mock property statistics');
      return this.getMockPropertyStatistics();
    }
  }

  async getNearbyAmenities(id: string): Promise<NearbyAmenitiesDto> {
    const property = await this.prisma.client.property.findUnique({
      where: { id },
      include: { suburb: true },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    if (!property.latitude || !property.longitude) {
      throw new BadRequestException('Property coordinates not available');
    }

    try {
      const [schools, transportStops] = await Promise.all([
        this.prisma.client.school.findMany({
          where: { suburbId: property.suburbId },
          include: { suburb: true },
        }),
        this.prisma.client.transportStop.findMany({
          where: { suburbId: property.suburbId },
          include: { suburb: true },
        }),
      ]);

      // Calculate distances and sort by proximity
      const nearbySchools = schools
        .map((school) => ({
          id: school.id,
          name: school.name,
          type: school.schoolType,
          distance: school.latitude && school.longitude
            ? calculateDistance(
                property.latitude!,
                property.longitude!,
                school.latitude,
                school.longitude,
              )
            : null,
          rating: school.rating,
        }))
        .filter((school) => school.distance !== null)
        .sort((a, b) => a.distance! - b.distance!)
        .slice(0, 10);

      const nearbyTransport = transportStops
        .map((stop) => ({
          id: stop.id,
          name: stop.name,
          type: stop.transportType,
          distance: stop.latitude && stop.longitude
            ? calculateDistance(
                property.latitude!,
                property.longitude!,
                stop.latitude,
                stop.longitude,
              )
            : null,
          routes: stop.routes,
        }))
        .filter((stop) => stop.distance !== null)
        .sort((a, b) => a.distance! - b.distance!)
        .slice(0, 10);

      return {
        propertyId: property.id,
        schools: nearbySchools,
        transport: nearbyTransport,
        distanceToCBD: this.calculateDistanceToCBD(property.latitude, property.longitude),
      };
    } catch (error) {
      this.logger.error(`Failed to get nearby amenities for property ${id}:`, error);
      throw new BadRequestException('Failed to get nearby amenities');
    }
  }

  async getSearchHistory(userId: string, limit: number = 10): Promise<RecentSearchesDto> {
    try {
      const searches = await this.prisma.client.userSearch.findMany({
        where: { userId },
        orderBy: { searchDate: 'desc' },
        take: limit,
      });

      const totalSearches = await this.prisma.client.userSearch.count({
        where: { userId },
      });

      // Get popular search terms
      const popularTermsData = await this.prisma.client.userSearch.groupBy({
        by: ['query'],
        where: { userId },
        _count: { query: true },
        orderBy: { _count: { query: 'desc' } },
        take: 5,
      });

      const popularTerms = popularTermsData.map(item => item.query);

      const searchHistory: UserSearchHistoryDto[] = searches.map(search => ({
        id: search.id,
        query: search.query,
        filters: search.filters,
        resultsCount: search.resultsCount,
        clickedResults: search.clickedResults,
        searchDate: search.searchDate,
        createdAt: search.createdAt,
      }));

      return {
        searches: searchHistory,
        totalSearches,
        popularTerms,
      };
    } catch (error) {
      this.logger.error(`Failed to get search history for user ${userId}:`, error);
      throw new BadRequestException('Failed to get search history');
    }
  }

  private async calculateMedianPrice(): Promise<number> {
    const properties = await this.prisma.client.property.findMany({
      where: {
        lastSalePrice: { not: null },
      },
      select: { lastSalePrice: true },
      orderBy: { lastSalePrice: 'asc' },
    });

    if (properties.length === 0) return 0;

    const prices = properties.map(p => p.lastSalePrice!);
    const middle = Math.floor(prices.length / 2);

    if (prices.length % 2 === 0) {
      return (prices[middle - 1] + prices[middle]) / 2;
    } else {
      return prices[middle];
    }
  }

  private calculateDistanceToCBD(lat: number, lng: number): number {
    // Melbourne CBD coordinates as default
    const cbdLat = -37.8136;
    const cbdLng = 144.9631;
    return calculateDistance(lat, lng, cbdLat, cbdLng);
  }

  private transformPropertyResponse(property: any): PropertyResponseDto {
    return {
      id: property.id,
      address: property.address,
      propertyType: property.propertyType,
      status: property.status,
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      carSpaces: property.carSpaces,
      landSize: property.landSize,
      buildingSize: property.buildingSize,
      yearBuilt: property.yearBuilt,
      latitude: property.latitude,
      longitude: property.longitude,
      description: property.description,
      features: property.features,
      images: property.images,
      currentPrice: property.currentPrice,
      lastSalePrice: property.lastSalePrice,
      lastSaleDate: property.lastSaleDate,
      createdAt: property.createdAt,
      updatedAt: property.updatedAt,
      suburb: property.suburb,
    };
  }

  private getMockPropertiesResponse(searchDto: PropertySearchDto): PaginatedResponseDto<PropertyResponseDto> {
    const mockProperties = this.generateMockProperties();
    const page = searchDto.page || 1;
    const limit = searchDto.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProperties = mockProperties.slice(startIndex, endIndex);

    return {
      success: true,
      data: paginatedProperties,
      meta: {
        total: mockProperties.length,
        page,
        limit,
        totalPages: Math.ceil(mockProperties.length / limit),
        hasNext: endIndex < mockProperties.length,
        hasPrev: page > 1,
      },
      message: 'Properties retrieved successfully (mock data)',
    };
  }

  private generateMockProperties(): PropertyResponseDto[] {
    const mockSuburbs = [
      { id: '1', name: 'Toorak', state: 'VIC', postcode: '3142' },
      { id: '2', name: 'South Yarra', state: 'VIC', postcode: '3141' },
      { id: '3', name: 'Richmond', state: 'VIC', postcode: '3121' },
      { id: '4', name: 'Carlton', state: 'VIC', postcode: '3053' },
      { id: '5', name: 'Fitzroy', state: 'VIC', postcode: '3065' },
    ];

    const propertyTypes = ['HOUSE', 'APARTMENT', 'TOWNHOUSE', 'UNIT'] as const;
    const statuses = ['FOR_SALE', 'SOLD', 'OFF_MARKET'] as const;

    return Array.from({ length: 20 }, (_, i) => {
      const suburb = mockSuburbs[i % mockSuburbs.length];
      const propertyType = propertyTypes[i % propertyTypes.length];
      const status = statuses[i % statuses.length];
      const bedrooms = Math.floor(Math.random() * 4) + 1;
      const bathrooms = Math.floor(Math.random() * 3) + 1;
      const carSpaces = Math.floor(Math.random() * 3);
      const basePrice = 800000 + (Math.random() * 2000000);

      return {
        id: `mock-property-${i + 1}`,
        address: `${i + 1} Mock Street, ${suburb.name} ${suburb.state} ${suburb.postcode}`,
        propertyType,
        status,
        bedrooms,
        bathrooms,
        carSpaces,
        landSize: Math.floor(Math.random() * 500) + 200,
        buildingSize: Math.floor(Math.random() * 300) + 100,
        yearBuilt: 1990 + Math.floor(Math.random() * 30),
        latitude: -37.8 + (Math.random() * 0.2 - 0.1),
        longitude: 144.9 + (Math.random() * 0.2 - 0.1),
        description: `Beautiful ${bedrooms} bedroom ${propertyType.toLowerCase()} in the heart of ${suburb.name}. Features modern amenities and excellent location.`,
        features: ['Air Conditioning', 'Dishwasher', 'Built-in Wardrobes', 'Balcony'],
        images: [
          'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800',
        ],
        currentPrice: Math.floor(basePrice),
        lastSalePrice: Math.floor(basePrice * 0.9),
        lastSaleDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        suburb,
      };
    });
  }

  private getMockPropertyStatistics(): PropertyStatsDto {
    return {
      totalProperties: 20,
      averagePrice: 1250000,
      medianPrice: 1100000,
      priceRange: {
        min: 650000,
        max: 2800000,
      },
      byType: {
        'HOUSE': 8,
        'APARTMENT': 6,
        'TOWNHOUSE': 4,
        'UNIT': 2,
      },
      byStatus: {
        'FOR_SALE': 7,
        'SOLD': 10,
        'OFF_MARKET': 3,
      },
      byBedrooms: {
        '1': 3,
        '2': 6,
        '3': 7,
        '4': 4,
      },
    };
  }

  private getMockPropertyById(id: string): PropertyResponseDto {
    // Generate all mock properties and find the one with matching ID
    const mockProperties = this.generateMockProperties();
    const property = mockProperties.find(p => p.id === id);

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    // Add additional details for individual property view
    return {
      ...property,
      description: `${property.description} This stunning property offers exceptional value in one of Melbourne's most sought-after locations. With premium finishes throughout and close proximity to excellent schools, transport, and shopping precincts, this is an opportunity not to be missed.`,
      features: [
        'Air Conditioning',
        'Dishwasher',
        'Built-in Wardrobes',
        'Balcony',
        'Secure Parking',
        'Swimming Pool',
        'Gym',
        'Garden',
        'Storage',
        'Intercom'
      ],
      images: [
        'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800',
        'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800',
        'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
        'https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800',
        'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800'
      ]
    };
  }
}
