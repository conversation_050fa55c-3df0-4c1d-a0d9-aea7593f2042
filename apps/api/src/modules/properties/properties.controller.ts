import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UsageType, User } from '@revalu/database';
import {
  ApiErrorResponse,
  ApiPaginatedResponse,
  ApiSuccessResponse,
} from '../../common/decorators/api-response.decorator';
import { PaginatedResponseDto } from '../../common/dto/pagination.dto';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UsageLimit, UsageLimitGuard } from '../subscriptions/guards/usage-limit.guard';
import {
  CreatePropertyDto,
  NearbyAmenitiesDto,
  PropertyResponseDto,
  PropertySearchDto,
  PropertyStatsDto,
  RecentSearchesDto,
  UpdatePropertyDto,
} from './dto/property.dto';
import { PropertiesService } from './properties.service';

@ApiTags('properties')
@Controller('properties')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class PropertiesController {
  constructor(private readonly propertiesService: PropertiesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new property' })
  @ApiSuccessResponse(PropertyResponseDto, 'Property created successfully')
  @ApiErrorResponse(400, 'Bad Request - Invalid property data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  async create(@Body() createPropertyDto: CreatePropertyDto): Promise<PropertyResponseDto> {
    return this.propertiesService.create(createPropertyDto);
  }

  @Get()
  @Public()
  @UseGuards(UsageLimitGuard)
  @UsageLimit({ type: UsageType.SEARCH, autoRecord: true })
  @ApiOperation({ summary: 'Search and filter properties' })
  @ApiPaginatedResponse(PropertyResponseDto, 'Properties retrieved successfully')
  @ApiErrorResponse(400, 'Bad Request - Invalid search parameters')
  @ApiErrorResponse(403, 'Forbidden - Search limit exceeded')
  async findAll(
    @Query() searchDto: PropertySearchDto,
  ): Promise<PaginatedResponseDto<PropertyResponseDto>> {
    return this.propertiesService.findAll(searchDto);
  }

  @Get('statistics')
  @Public()
  @ApiOperation({ summary: 'Get property market statistics' })
  @ApiSuccessResponse(PropertyStatsDto, 'Property statistics retrieved successfully')
  async getStatistics(): Promise<PropertyStatsDto> {
    return this.propertiesService.getStatistics();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get property by ID' })
  @ApiParam({ name: 'id', description: 'Property ID', example: 'clp1234567890' })
  @ApiSuccessResponse(PropertyResponseDto, 'Property retrieved successfully')
  @ApiErrorResponse(404, 'Not Found - Property not found')
  async findOne(@Param('id') id: string): Promise<PropertyResponseDto> {
    return this.propertiesService.findOne(id);
  }

  @Get(':id/amenities')
  @Public()
  @ApiOperation({ summary: 'Get nearby amenities for a property' })
  @ApiParam({ name: 'id', description: 'Property ID', example: 'clp1234567890' })
  @ApiSuccessResponse(NearbyAmenitiesDto, 'Nearby amenities retrieved successfully')
  @ApiErrorResponse(404, 'Not Found - Property not found')
  @ApiErrorResponse(400, 'Bad Request - Property coordinates not available')
  async getNearbyAmenities(@Param('id') id: string): Promise<NearbyAmenitiesDto> {
    return this.propertiesService.getNearbyAmenities(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update property by ID' })
  @ApiParam({ name: 'id', description: 'Property ID', example: 'clp1234567890' })
  @ApiSuccessResponse(PropertyResponseDto, 'Property updated successfully')
  @ApiErrorResponse(400, 'Bad Request - Invalid update data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(404, 'Not Found - Property not found')
  async update(
    @Param('id') id: string,
    @Body() updatePropertyDto: UpdatePropertyDto,
  ): Promise<PropertyResponseDto> {
    return this.propertiesService.update(id, updatePropertyDto);
  }

  @Get('search/history')
  @ApiOperation({ summary: 'Get user search history' })
  @ApiSuccessResponse(RecentSearchesDto, 'Search history retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  async getSearchHistory(
    @CurrentUser() user: User,
    @Query('limit') limit?: number,
  ): Promise<RecentSearchesDto> {
    return this.propertiesService.getSearchHistory(user.id, limit);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete property by ID' })
  @ApiParam({ name: 'id', description: 'Property ID', example: 'clp1234567890' })
  @ApiResponse({
    status: 200,
    description: 'Property deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Property deleted successfully' },
      },
    },
  })
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(404, 'Not Found - Property not found')
  async remove(@Param('id') id: string): Promise<{ message: string }> {
    return this.propertiesService.remove(id);
  }
}
