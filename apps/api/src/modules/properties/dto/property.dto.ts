import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PropertyStatus, PropertyType } from '@revalu/database';
import { Type } from 'class-transformer';
import {
    IsArray,
    IsDateString,
    IsEnum,
    IsLatitude,
    IsLongitude,
    IsNumber,
    IsOptional,
    IsString,
    Max,
    Min,
} from 'class-validator';
import { PaginationDto } from '../../../common/dto/pagination.dto';

export class CreatePropertyDto {
  @ApiProperty({
    description: 'Property address',
    example: '123 Main Street, Toorak VIC 3142',
  })
  @IsString()
  address: string;

  @ApiProperty({
    description: 'Suburb ID',
    example: 'clp1234567890',
  })
  @IsString()
  suburbId: string;

  @ApiProperty({
    description: 'Property type',
    enum: PropertyType,
    example: PropertyType.HOUSE,
  })
  @IsEnum(PropertyType)
  propertyType: PropertyType;

  @ApiPropertyOptional({
    description: 'Property status',
    enum: PropertyStatus,
    example: PropertyStatus.FOR_SALE,
  })
  @IsOptional()
  @IsEnum(PropertyStatus)
  status?: PropertyStatus;

  @ApiPropertyOptional({
    description: 'Number of bedrooms',
    example: 3,
    minimum: 0,
    maximum: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(20)
  bedrooms?: number;

  @ApiPropertyOptional({
    description: 'Number of bathrooms',
    example: 2,
    minimum: 0,
    maximum: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(20)
  bathrooms?: number;

  @ApiPropertyOptional({
    description: 'Number of car spaces',
    example: 2,
    minimum: 0,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  carSpaces?: number;

  @ApiPropertyOptional({
    description: 'Land size in square meters',
    example: 650,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  landSize?: number;

  @ApiPropertyOptional({
    description: 'Building size in square meters',
    example: 280,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  buildingSize?: number;

  @ApiPropertyOptional({
    description: 'Year built',
    example: 1920,
    minimum: 1800,
    maximum: 2030,
  })
  @IsOptional()
  @IsNumber()
  @Min(1800)
  @Max(2030)
  yearBuilt?: number;

  @ApiPropertyOptional({
    description: 'Property latitude',
    example: -37.8403,
  })
  @IsOptional()
  @IsLatitude()
  latitude?: number;

  @ApiPropertyOptional({
    description: 'Property longitude',
    example: 145.0075,
  })
  @IsOptional()
  @IsLongitude()
  longitude?: number;

  @ApiPropertyOptional({
    description: 'Property description',
    example: 'Beautiful family home with modern amenities',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Property features',
    example: ['Pool', 'Garden', 'Garage', 'Fireplace'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];

  @ApiPropertyOptional({
    description: 'Property images',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @ApiPropertyOptional({
    description: 'Current asking price',
    example: 2800000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  currentPrice?: number;

  @ApiPropertyOptional({
    description: 'Last sale price',
    example: 2200000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  lastSalePrice?: number;

  @ApiPropertyOptional({
    description: 'Last sale date',
    example: '2020-03-15T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  lastSaleDate?: string;
}

export class UpdatePropertyDto {
  @ApiPropertyOptional({
    description: 'Property address',
    example: '123 Main Street, Toorak VIC 3142',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: 'Property status',
    enum: PropertyStatus,
    example: PropertyStatus.SOLD,
  })
  @IsOptional()
  @IsEnum(PropertyStatus)
  status?: PropertyStatus;

  @ApiPropertyOptional({
    description: 'Number of bedrooms',
    example: 4,
    minimum: 0,
    maximum: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(20)
  bedrooms?: number;

  @ApiPropertyOptional({
    description: 'Number of bathrooms',
    example: 3,
    minimum: 0,
    maximum: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(20)
  bathrooms?: number;

  @ApiPropertyOptional({
    description: 'Number of car spaces',
    example: 2,
    minimum: 0,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  carSpaces?: number;

  @ApiPropertyOptional({
    description: 'Property description',
    example: 'Updated description with new features',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Property features',
    example: ['Pool', 'Garden', 'Garage', 'Fireplace', 'Solar Panels'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];

  @ApiPropertyOptional({
    description: 'Property images',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @ApiPropertyOptional({
    description: 'Current asking price',
    example: 2900000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  currentPrice?: number;
}

export class PropertySearchDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Property types to filter by',
    enum: PropertyType,
    isArray: true,
    example: [PropertyType.HOUSE, PropertyType.APARTMENT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(PropertyType, { each: true })
  propertyType?: PropertyType[];

  @ApiPropertyOptional({
    description: 'Property status to filter by',
    enum: PropertyStatus,
    isArray: true,
    example: [PropertyStatus.FOR_SALE],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(PropertyStatus, { each: true })
  status?: PropertyStatus[];

  @ApiPropertyOptional({
    description: 'Minimum price',
    example: 500000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minPrice?: number;

  @ApiPropertyOptional({
    description: 'Maximum price',
    example: 2000000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxPrice?: number;

  @ApiPropertyOptional({
    description: 'Minimum bedrooms',
    example: 2,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minBedrooms?: number;

  @ApiPropertyOptional({
    description: 'Maximum bedrooms',
    example: 5,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxBedrooms?: number;

  @ApiPropertyOptional({
    description: 'Minimum bathrooms',
    example: 1,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minBathrooms?: number;

  @ApiPropertyOptional({
    description: 'Maximum bathrooms',
    example: 4,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxBathrooms?: number;

  @ApiPropertyOptional({
    description: 'Suburb IDs to filter by',
    example: ['clp1234567890', 'clp0987654321'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  suburbs?: string[];

  @ApiPropertyOptional({
    description: 'Property features to filter by',
    example: ['Pool', 'Garden'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];

  @ApiPropertyOptional({
    description: 'Year built from',
    example: 2000,
    minimum: 1800,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1800)
  yearBuiltFrom?: number;

  @ApiPropertyOptional({
    description: 'Year built to',
    example: 2020,
    minimum: 1800,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1800)
  yearBuiltTo?: number;
}

export class PropertyResponseDto {
  @ApiProperty({ description: 'Property ID', example: 'clp1234567890' })
  id: string;

  @ApiProperty({ description: 'Property address' })
  address: string;

  @ApiProperty({ description: 'Property type', enum: PropertyType })
  propertyType: PropertyType;

  @ApiProperty({ description: 'Property status', enum: PropertyStatus })
  status: PropertyStatus;

  @ApiPropertyOptional({ description: 'Number of bedrooms' })
  bedrooms?: number;

  @ApiPropertyOptional({ description: 'Number of bathrooms' })
  bathrooms?: number;

  @ApiPropertyOptional({ description: 'Number of car spaces' })
  carSpaces?: number;

  @ApiPropertyOptional({ description: 'Land size in square meters' })
  landSize?: number;

  @ApiPropertyOptional({ description: 'Building size in square meters' })
  buildingSize?: number;

  @ApiPropertyOptional({ description: 'Year built' })
  yearBuilt?: number;

  @ApiPropertyOptional({ description: 'Property latitude' })
  latitude?: number;

  @ApiPropertyOptional({ description: 'Property longitude' })
  longitude?: number;

  @ApiPropertyOptional({ description: 'Property description' })
  description?: string;

  @ApiPropertyOptional({ description: 'Property features', type: [String] })
  features?: string[];

  @ApiPropertyOptional({ description: 'Property images', type: [String] })
  images?: string[];

  @ApiPropertyOptional({ description: 'Current asking price' })
  currentPrice?: number;

  @ApiPropertyOptional({ description: 'Last sale price' })
  lastSalePrice?: number;

  @ApiPropertyOptional({ description: 'Last sale date' })
  lastSaleDate?: Date;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Suburb information' })
  suburb: {
    id: string;
    name: string;
    state: string;
    postcode: string;
  };
}

export class PropertyStatsDto {
  @ApiProperty({ description: 'Total number of properties', example: 1250 })
  totalProperties: number;

  @ApiProperty({ description: 'Average price', example: 1500000 })
  averagePrice: number;

  @ApiProperty({ description: 'Median price', example: 1200000 })
  medianPrice: number;

  @ApiProperty({ description: 'Price range' })
  priceRange: {
    min: number;
    max: number;
  };

  @ApiProperty({ description: 'Properties by type' })
  byType: Record<string, number>;

  @ApiProperty({ description: 'Properties by status' })
  byStatus: Record<string, number>;

  @ApiProperty({ description: 'Properties by bedroom count' })
  byBedrooms: Record<string, number>;
}

export class NearbyAmenitiesDto {
  @ApiProperty({ description: 'Property ID', example: 'clp1234567890' })
  propertyId: string;

  @ApiProperty({ description: 'Nearby schools' })
  schools: Array<{
    id: string;
    name: string;
    type: string;
    distance: number;
    rating?: number;
  }>;

  @ApiProperty({ description: 'Nearby transport stops' })
  transport: Array<{
    id: string;
    name: string;
    type: string;
    distance: number;
    routes: string[];
  }>;

  @ApiProperty({ description: 'Distance to CBD in kilometers' })
  distanceToCBD?: number;
}

export class UserSearchHistoryDto {
  @ApiProperty({ description: 'Search ID', example: 'clp1234567890' })
  id: string;

  @ApiProperty({ description: 'Search query', example: 'Toorak house 3 bedrooms' })
  query: string;

  @ApiPropertyOptional({ description: 'Search filters applied' })
  filters?: any;

  @ApiPropertyOptional({ description: 'Number of results returned', example: 25 })
  resultsCount?: number;

  @ApiPropertyOptional({ description: 'Property IDs clicked from results', type: [String] })
  clickedResults?: string[];

  @ApiProperty({ description: 'Search date', example: '2024-12-15T10:30:00.000Z' })
  searchDate: Date;

  @ApiProperty({ description: 'Creation date', example: '2024-12-15T10:30:00.000Z' })
  createdAt: Date;
}

export class RecentSearchesDto {
  @ApiProperty({ description: 'Recent searches', type: [UserSearchHistoryDto] })
  searches: UserSearchHistoryDto[];

  @ApiProperty({ description: 'Total number of searches', example: 45 })
  totalSearches: number;

  @ApiProperty({ description: 'Most searched terms', type: [String] })
  popularTerms: string[];
}
