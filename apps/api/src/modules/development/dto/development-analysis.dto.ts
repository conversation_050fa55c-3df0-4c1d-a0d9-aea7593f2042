import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsArray, IsBoolean, Min, Max } from 'class-validator';

export class DevelopmentAnalysisDto {
  @ApiProperty({ description: 'Property ID to analyze' })
  @IsString()
  propertyId: string;

  @ApiPropertyOptional({ description: 'Development type to analyze', example: 'duplex' })
  @IsOptional()
  @IsString()
  developmentType?: string;

  @ApiPropertyOptional({ description: 'Number of dwellings', minimum: 1, maximum: 50 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  dwellings?: number;

  @ApiPropertyOptional({ description: 'Number of floors', minimum: 1, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  floors?: number;
}

export class DevelopmentPotentialResponseDto {
  @ApiProperty({ description: 'Development potential score (0-100)' })
  score: number;

  @ApiProperty({ description: 'Current zoning information' })
  zoning: {
    current: string;
    allowedUses: string[];
    heightLimit: number;
    floorSpaceRatio: number;
    siteCoverage: number;
  };

  @ApiProperty({ description: 'Building envelope data' })
  envelope: {
    maxHeight: number;
    setbacks: {
      front: number;
      rear: number;
      side: number;
    };
    buildableArea: number;
  };

  @ApiProperty({ description: 'Development scenarios' })
  scenarios: Array<{
    type: string;
    dwellings: number;
    estimatedValue: number;
    constructionCost: number;
    profit: number;
    roi: number;
    timeline: string;
  }>;

  @ApiProperty({ description: 'Approval precedents' })
  precedents: {
    successRate: number;
    averageTimeframe: number;
    similarApprovals: number;
    commonConditions: string[];
  };

  @ApiProperty({ description: 'Risk factors' })
  risks: Array<{
    type: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
    mitigation: string;
  }>;
}

export class DevelopmentSimulatorDto {
  @ApiProperty({ description: 'Property ID' })
  @IsString()
  propertyId: string;

  @ApiProperty({ description: 'Number of dwellings to simulate' })
  @IsNumber()
  @Min(1)
  @Max(50)
  dwellings: number;

  @ApiProperty({ description: 'Number of floors' })
  @IsNumber()
  @Min(1)
  @Max(10)
  floors: number;

  @ApiPropertyOptional({ description: 'Development type' })
  @IsOptional()
  @IsString()
  developmentType?: string;

  @ApiPropertyOptional({ description: 'Include 3D visualization' })
  @IsOptional()
  @IsBoolean()
  include3D?: boolean;
}

export class DevelopmentSimulatorResponseDto {
  @ApiProperty({ description: 'Simulation results' })
  simulation: {
    feasible: boolean;
    totalFloorArea: number;
    totalValue: number;
    constructionCost: number;
    profit: number;
    roi: number;
    timeline: {
      planning: number;
      construction: number;
      total: number;
    };
  };

  @ApiProperty({ description: '3D visualization data' })
  visualization: {
    buildingEnvelope: any;
    shadowAnalysis: any;
    floorPlans: any[];
  };

  @ApiProperty({ description: 'Financial breakdown' })
  financials: {
    landValue: number;
    constructionCost: number;
    professionalFees: number;
    councilFees: number;
    contingency: number;
    totalCost: number;
    estimatedSaleValue: number;
    profit: number;
    margin: number;
  };
}
