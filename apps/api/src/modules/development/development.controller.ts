import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  ApiSuccessResponse,
  ApiErrorResponse,
} from '../../common/decorators/api-response.decorator';
import { DevelopmentService } from './development.service';
import {
  DevelopmentAnalysisDto,
  DevelopmentPotentialResponseDto,
  DevelopmentSimulatorDto,
  DevelopmentSimulatorResponseDto,
} from './dto/development-analysis.dto';

@ApiTags('development')
@Controller('development')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class DevelopmentController {
  constructor(private readonly developmentService: DevelopmentService) {}

  @Post('analyze')
  @ApiOperation({ summary: 'Analyze development potential for a property' })
  @ApiSuccessResponse(
    DevelopmentPotentialResponseDto,
    'Development analysis completed successfully',
  )
  @ApiErrorResponse(404, 'Property not found')
  @ApiErrorResponse(400, 'Invalid analysis parameters')
  async analyzeDevelopmentPotential(
    @Body() dto: DevelopmentAnalysisDto,
  ): Promise<DevelopmentPotentialResponseDto> {
    return this.developmentService.analyzeDevelopmentPotential(dto);
  }

  @Post('simulate')
  @ApiOperation({ summary: 'Simulate development scenarios with 3D visualization' })
  @ApiSuccessResponse(
    DevelopmentSimulatorResponseDto,
    'Development simulation completed successfully',
  )
  @ApiErrorResponse(404, 'Property not found')
  @ApiErrorResponse(400, 'Invalid simulation parameters')
  async simulateDevelopment(
    @Body() dto: DevelopmentSimulatorDto,
  ): Promise<DevelopmentSimulatorResponseDto> {
    return this.developmentService.simulateDevelopment(dto);
  }
}
