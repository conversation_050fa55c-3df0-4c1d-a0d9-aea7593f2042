import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common/database/prisma.service';
import { DevelopmentAnalysisDto, DevelopmentPotentialResponseDto, DevelopmentSimulatorDto, DevelopmentSimulatorResponseDto } from './dto/development-analysis.dto';

@Injectable()
export class DevelopmentService {
  constructor(private readonly prisma: PrismaService) {}

  async analyzeDevelopmentPotential(dto: DevelopmentAnalysisDto): Promise<DevelopmentPotentialResponseDto> {
    // Get property details
    const property = await this.prisma.client.property.findUnique({
      where: { id: dto.propertyId },
      include: {
        suburb: true,
        intelligence: true,
      },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    // Get development applications in the area for precedent analysis
    const precedents = await this.prisma.client.developmentApplication.findMany({
      where: {
        suburbId: property.suburbId,
        status: 'APPROVED',
      },
      take: 50,
    });

    // Calculate development potential score
    const score = this.calculateDevelopmentScore(property, precedents);

    // Mock zoning data (in real implementation, this would come from planning data)
    const zoning = {
      current: 'R2 - Low Density Residential',
      allowedUses: ['Single dwelling', 'Dual occupancy', 'Secondary dwelling'],
      heightLimit: 8.5,
      floorSpaceRatio: 0.5,
      siteCoverage: 50,
    };

    // Calculate building envelope
    const envelope = this.calculateBuildingEnvelope(property, zoning);

    // Generate development scenarios
    const scenarios = this.generateDevelopmentScenarios(property, zoning, envelope);

    // Analyze precedents
    const precedentAnalysis = this.analyzePrecedents(precedents);

    // Identify risks
    const risks = this.identifyDevelopmentRisks(property, zoning);

    return {
      score,
      zoning,
      envelope,
      scenarios,
      precedents: precedentAnalysis,
      risks,
    };
  }

  async simulateDevelopment(dto: DevelopmentSimulatorDto): Promise<DevelopmentSimulatorResponseDto> {
    const property = await this.prisma.client.property.findUnique({
      where: { id: dto.propertyId },
      include: { suburb: true },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    // Calculate simulation results
    const simulation = this.calculateSimulation(property, dto);

    // Generate 3D visualization data (mock)
    const visualization = dto.include3D ? this.generate3DVisualization(property, dto) : null;

    // Calculate detailed financials
    const financials = this.calculateDetailedFinancials(property, dto);

    return {
      simulation,
      visualization,
      financials,
    };
  }

  private calculateDevelopmentScore(property: any, precedents: any[]): number {
    let score = 50; // Base score

    // Land size factor
    if (property.landSize > 800) score += 20;
    else if (property.landSize > 600) score += 10;

    // Precedent factor
    if (precedents.length > 10) score += 15;
    else if (precedents.length > 5) score += 10;

    // Location factor (mock)
    if (property.suburb.medianPrice > 1000000) score += 10;

    // Property age factor
    const currentYear = new Date().getFullYear();
    const age = property.yearBuilt ? currentYear - property.yearBuilt : 50;
    if (age > 30) score += 5;

    return Math.min(100, Math.max(0, score));
  }

  private calculateBuildingEnvelope(property: any, zoning: any) {
    const landSize = property.landSize || 600;
    const maxHeight = zoning.heightLimit;
    
    return {
      maxHeight,
      setbacks: {
        front: 6,
        rear: 6,
        side: 1.5,
      },
      buildableArea: landSize * (zoning.siteCoverage / 100),
    };
  }

  private generateDevelopmentScenarios(property: any, zoning: any, envelope: any) {
    const baseValue = property.suburb.medianPrice || 800000;
    
    return [
      {
        type: 'Duplex',
        dwellings: 2,
        estimatedValue: baseValue * 1.8,
        constructionCost: 400000,
        profit: baseValue * 1.8 - 400000 - (property.lastSalePrice || baseValue * 0.8),
        roi: 25,
        timeline: '18-24 months',
      },
      {
        type: 'Townhouses',
        dwellings: 3,
        estimatedValue: baseValue * 2.4,
        constructionCost: 600000,
        profit: baseValue * 2.4 - 600000 - (property.lastSalePrice || baseValue * 0.8),
        roi: 35,
        timeline: '24-30 months',
      },
    ];
  }

  private analyzePrecedents(precedents: any[]) {
    const total = precedents.length;
    const approved = precedents.filter(p => p.status === 'APPROVED').length;
    
    return {
      successRate: total > 0 ? (approved / total) * 100 : 0,
      averageTimeframe: 6, // months (mock)
      similarApprovals: approved,
      commonConditions: [
        'Landscaping requirements',
        'Parking provisions',
        'Privacy screening',
        'Stormwater management',
      ],
    };
  }

  private identifyDevelopmentRisks(property: any, zoning: any) {
    return [
      {
        type: 'Planning Risk',
        severity: 'medium' as const,
        description: 'Council may impose additional conditions',
        mitigation: 'Engage experienced town planner early',
      },
      {
        type: 'Market Risk',
        severity: 'low' as const,
        description: 'Property values may fluctuate during development',
        mitigation: 'Monitor market conditions and consider pre-sales',
      },
      {
        type: 'Construction Risk',
        severity: 'medium' as const,
        description: 'Cost overruns and delays possible',
        mitigation: 'Fixed-price contract with experienced builder',
      },
    ];
  }

  private calculateSimulation(property: any, dto: DevelopmentSimulatorDto) {
    const landValue = property.lastSalePrice || property.suburb.medianPrice * 0.8;
    const constructionCostPerSqm = 2500;
    const avgDwellingSize = 120;
    const totalFloorArea = dto.dwellings * avgDwellingSize;
    const constructionCost = totalFloorArea * constructionCostPerSqm;
    const estimatedValue = dto.dwellings * property.suburb.medianPrice * 0.9;
    const profit = estimatedValue - constructionCost - landValue;
    const roi = (profit / (constructionCost + landValue)) * 100;

    return {
      feasible: profit > 0,
      totalFloorArea,
      totalValue: estimatedValue,
      constructionCost,
      profit,
      roi,
      timeline: {
        planning: 6,
        construction: 12,
        total: 18,
      },
    };
  }

  private generate3DVisualization(property: any, dto: DevelopmentSimulatorDto) {
    // Mock 3D visualization data
    return {
      buildingEnvelope: {
        vertices: [],
        faces: [],
        materials: [],
      },
      shadowAnalysis: {
        winterSolstice: [],
        summerSolstice: [],
        equinox: [],
      },
      floorPlans: Array(dto.floors).fill(null).map((_, index) => ({
        floor: index + 1,
        area: 120,
        rooms: ['Living', 'Kitchen', 'Bedrooms', 'Bathrooms'],
      })),
    };
  }

  private calculateDetailedFinancials(property: any, dto: DevelopmentSimulatorDto) {
    const landValue = property.lastSalePrice || property.suburb.medianPrice * 0.8;
    const constructionCost = dto.dwellings * 120 * 2500;
    const professionalFees = constructionCost * 0.15;
    const councilFees = 25000;
    const contingency = constructionCost * 0.1;
    const totalCost = landValue + constructionCost + professionalFees + councilFees + contingency;
    const estimatedSaleValue = dto.dwellings * property.suburb.medianPrice * 0.9;
    const profit = estimatedSaleValue - totalCost;
    const margin = (profit / estimatedSaleValue) * 100;

    return {
      landValue,
      constructionCost,
      professionalFees,
      councilFees,
      contingency,
      totalCost,
      estimatedSaleValue,
      profit,
      margin,
    };
  }
}
