import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common/database/prisma.service';
import {
    EmergingSuburbsDto,
    EmergingSuburbsResponseDto,
    HeatMapType,
    MarketHeatMapDto,
    MarketHeatMapResponseDto,
    MarketMoversResponseDto,
    MarketTrendsDto,
    MarketTrendsResponseDto,
    TrendPeriod
} from './dto/market-intelligence.dto';

@Injectable()
export class MarketService {
  constructor(private readonly prisma: PrismaService) {}

  async getHeatMap(dto: MarketHeatMapDto): Promise<MarketHeatMapResponseDto> {
    // Get suburbs with market data
    const suburbs = await this.prisma.client.suburb.findMany({
      where: dto.state ? { state: dto.state } : {},
      include: {
        marketTrends: {
          orderBy: { startDate: 'desc' },
          take: 1,
        },
        properties: {
          select: {
            id: true,
            lastSalePrice: true,
          },
        },
        developmentApps: {
          where: {
            status: 'APPROVED',
            submissionDate: {
              gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // Last year
            },
          },
        },
      },
      take: 500, // Limit for performance
    });

    const data = suburbs
      .filter(s => s.latitude && s.longitude)
      .map(suburb => {
        let value = 0;
        let color = '#cccccc';
        let intensity = 0;

        switch (dto.type) {
          case HeatMapType.PRICE_GROWTH:
            value = suburb.marketTrends[0]?.percentageChange || 0;
            intensity = Math.min(Math.abs(value) / 20, 1); // Normalize to 0-1
            color = value > 0 ? `rgba(0, 255, 0, ${intensity})` : `rgba(255, 0, 0, ${intensity})`;
            break;
          
          case HeatMapType.DEVELOPMENT_ACTIVITY:
            value = suburb.developmentApps.length;
            intensity = Math.min(value / 10, 1);
            color = `rgba(255, 165, 0, ${intensity})`;
            break;
          
          case HeatMapType.YIELDS:
            // Mock yield calculation
            value = 3.5 + Math.random() * 3; // 3.5-6.5%
            intensity = (value - 3.5) / 3;
            color = `rgba(0, 0, 255, ${intensity})`;
            break;
          
          case HeatMapType.INVESTMENT_SCORE:
            // Mock investment score
            value = 50 + Math.random() * 50; // 50-100
            intensity = (value - 50) / 50;
            color = `rgba(128, 0, 128, ${intensity})`;
            break;
        }

        return {
          suburbId: suburb.id,
          name: suburb.name,
          state: suburb.state,
          latitude: suburb.latitude!,
          longitude: suburb.longitude!,
          value,
          color,
          intensity,
        };
      });

    // Calculate legend
    const values = data.map(d => d.value);
    const min = Math.min(...values);
    const max = Math.max(...values);

    const legend = {
      min,
      max,
      unit: this.getUnitForHeatMapType(dto.type),
      colorScale: this.generateColorScale(dto.type),
    };

    return {
      type: dto.type,
      data,
      legend,
      lastUpdated: new Date(),
    };
  }

  async getTrends(dto: MarketTrendsDto): Promise<MarketTrendsResponseDto> {
    const whereClause = dto.suburbId ? { suburbId: dto.suburbId } : {};
    
    const trends = await this.prisma.client.marketTrend.findMany({
      where: {
        ...whereClause,
        period: dto.period.toUpperCase() as any,
      },
      orderBy: { startDate: 'desc' },
      take: dto.periods || 12,
      include: {
        suburb: true,
      },
    });

    if (trends.length === 0) {
      throw new NotFoundException('No trend data found for the specified criteria');
    }

    // Process trends data
    const trendData = trends.reverse().map((trend, index) => ({
      period: this.formatPeriod(trend.startDate, dto.period),
      date: trend.startDate,
      value: trend.medianPrice || 0,
      change: index > 0 ? (trend.medianPrice || 0) - (trends[index - 1]?.medianPrice || 0) : 0,
      changePercent: trend.percentageChange,
    }));

    // Calculate summary
    const currentValue = trendData[trendData.length - 1]?.value || 0;
    const previousValue = trendData[trendData.length - 2]?.value || 0;
    const periodChange = currentValue - previousValue;
    const periodChangePercent = previousValue > 0 ? (periodChange / previousValue) * 100 : 0;

    // Calculate volatility
    const changes = trendData.slice(1).map(t => t.changePercent);
    const volatility = this.calculateStandardDeviation(changes);

    // Determine trend direction
    const recentChanges = changes.slice(-3);
    const avgRecentChange = recentChanges.reduce((sum, c) => sum + c, 0) / recentChanges.length;
    const trend = avgRecentChange > 2 ? 'up' : avgRecentChange < -2 ? 'down' : 'stable';

    // Generate forecast (simplified)
    const forecast = this.generateForecast(trendData, dto.period);

    return {
      trends: trendData,
      summary: {
        currentValue,
        periodChange,
        periodChangePercent,
        volatility,
        trend,
      },
      forecast,
    };
  }

  async getEmergingSuburbs(dto: EmergingSuburbsDto): Promise<EmergingSuburbsResponseDto> {
    const suburbs = await this.prisma.client.suburb.findMany({
      where: {
        ...(dto.state && { state: dto.state }),
        ...(dto.maxPrice && { medianPrice: { lte: dto.maxPrice } }),
      },
      include: {
        marketTrends: {
          orderBy: { startDate: 'desc' },
          take: 4, // Last 4 quarters
        },
        infrastructureProjects: {
          where: {
            status: { in: ['APPROVED', 'UNDER_CONSTRUCTION'] },
          },
        },
        properties: {
          select: { id: true },
        },
      },
      take: dto.limit || 20,
    });

    const emergingSuburbs = suburbs
      .map(suburb => {
        // Calculate growth rate from recent trends
        const recentTrends = suburb.marketTrends.slice(0, 4);
        const growthRate = recentTrends.length > 0 
          ? recentTrends.reduce((sum, t) => sum + t.percentageChange, 0) / recentTrends.length
          : 0;

        // Calculate emerging score
        const emergingScore = this.calculateEmergingScore(suburb, growthRate);

        return {
          id: suburb.id,
          name: suburb.name,
          state: suburb.state,
          postcode: suburb.postcode,
          medianPrice: suburb.medianPrice || 0,
          growthRate,
          growthRank: 0, // Will be set after sorting
          emergingScore,
          keyFactors: this.identifyKeyFactors(suburb),
          infrastructure: suburb.infrastructureProjects.map(infra => ({
            type: infra.type,
            name: infra.name,
            status: infra.status,
            impact: 'Positive', // Simplified
          })),
          demographics: {
            population: suburb.population || 0,
            medianAge: suburb.medianAge || 0,
            medianIncome: 75000, // Mock data
          },
        };
      })
      .filter(s => !dto.minGrowth || s.growthRate >= dto.minGrowth)
      .sort((a, b) => b.emergingScore - a.emergingScore)
      .map((suburb, index) => ({ ...suburb, growthRank: index + 1 }));

    return {
      suburbs: emergingSuburbs,
      criteria: {
        state: dto.state,
        minGrowth: dto.minGrowth,
        maxPrice: dto.maxPrice,
        limit: dto.limit || 20,
      },
    };
  }

  async getMarketMovers(): Promise<MarketMoversResponseDto> {
    // Get recent market trends
    const recentTrends = await this.prisma.client.marketTrend.findMany({
      where: {
        startDate: {
          gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Last 3 months
        },
      },
      include: {
        suburb: true,
      },
      orderBy: { percentageChange: 'desc' },
    });

    const topGrowers = recentTrends
      .filter(t => t.percentageChange > 0)
      .slice(0, 5)
      .map(trend => ({
        suburbId: trend.suburbId,
        name: trend.suburb.name,
        state: trend.suburb.state,
        growthPercent: trend.percentageChange,
        medianPrice: trend.medianPrice || 0,
        salesVolume: trend.salesVolume || 0,
      }));

    const topDecliners = recentTrends
      .filter(t => t.percentageChange < 0)
      .sort((a, b) => a.percentageChange - b.percentageChange)
      .slice(0, 5)
      .map(trend => ({
        suburbId: trend.suburbId,
        name: trend.suburb.name,
        state: trend.suburb.state,
        declinePercent: Math.abs(trend.percentageChange),
        medianPrice: trend.medianPrice || 0,
        salesVolume: trend.salesVolume || 0,
      }));

    const mostActive = recentTrends
      .sort((a, b) => (b.salesVolume || 0) - (a.salesVolume || 0))
      .slice(0, 5)
      .map(trend => ({
        suburbId: trend.suburbId,
        name: trend.suburb.name,
        state: trend.suburb.state,
        salesVolume: trend.salesVolume || 0,
        averagePrice: trend.averagePrice || 0,
        daysOnMarket: trend.daysOnMarket || 0,
      }));

    return {
      topGrowers,
      topDecliners,
      mostActive,
    };
  }

  private getUnitForHeatMapType(type: HeatMapType): string {
    switch (type) {
      case HeatMapType.PRICE_GROWTH:
        return '%';
      case HeatMapType.DEVELOPMENT_ACTIVITY:
        return 'applications';
      case HeatMapType.YIELDS:
        return '%';
      case HeatMapType.INVESTMENT_SCORE:
        return 'score';
      default:
        return '';
    }
  }

  private generateColorScale(type: HeatMapType) {
    // Simplified color scale generation
    return [
      { value: 0, color: '#cccccc' },
      { value: 0.5, color: '#ffff00' },
      { value: 1, color: '#ff0000' },
    ];
  }

  private formatPeriod(date: Date, period: TrendPeriod): string {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const quarter = Math.ceil(month / 3);

    switch (period) {
      case TrendPeriod.WEEKLY:
        return `Week ${Math.ceil(date.getDate() / 7)}, ${year}`;
      case TrendPeriod.MONTHLY:
        return `${year}-${month.toString().padStart(2, '0')}`;
      case TrendPeriod.QUARTERLY:
        return `${year}-Q${quarter}`;
      case TrendPeriod.YEARLY:
        return year.toString();
      default:
        return date.toISOString().split('T')[0];
    }
  }

  private calculateStandardDeviation(values: number[]): number {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  private generateForecast(trendData: any[], period: TrendPeriod) {
    // Simplified linear forecast
    const recentTrends = trendData.slice(-6);
    const avgChange = recentTrends.reduce((sum, t) => sum + t.changePercent, 0) / recentTrends.length;
    const lastValue = trendData[trendData.length - 1]?.value || 0;

    return Array.from({ length: 3 }, (_, i) => ({
      period: `Forecast ${i + 1}`,
      predictedValue: lastValue * Math.pow(1 + avgChange / 100, i + 1),
      confidence: Math.max(0.5 - i * 0.1, 0.3), // Decreasing confidence
    }));
  }

  private calculateEmergingScore(suburb: any, growthRate: number): number {
    let score = 50;

    // Growth rate factor
    score += Math.min(growthRate * 2, 30);

    // Infrastructure factor
    score += Math.min(suburb.infrastructureProjects.length * 5, 20);

    // Price accessibility factor
    if (suburb.medianPrice < 600000) score += 15;
    else if (suburb.medianPrice < 800000) score += 10;

    // Population growth potential
    if (suburb.population && suburb.population < 50000) score += 10;

    return Math.min(100, Math.max(0, score));
  }

  private identifyKeyFactors(suburb: any): string[] {
    const factors = [];

    if (suburb.infrastructureProjects.length > 0) {
      factors.push('Major infrastructure development');
    }

    if (suburb.medianPrice < 600000) {
      factors.push('Affordable entry point');
    }

    if (suburb.marketTrends.some((t: any) => t.percentageChange > 5)) {
      factors.push('Strong price growth');
    }

    factors.push('Growing population');
    factors.push('Improving amenities');

    return factors;
  }
}
