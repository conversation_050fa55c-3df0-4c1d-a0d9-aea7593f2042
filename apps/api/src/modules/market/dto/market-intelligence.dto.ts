import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';

export enum HeatMapType {
  PRICE_GROWTH = 'price_growth',
  DEVELOPMENT_ACTIVITY = 'development_activity',
  YIELDS = 'yields',
  INVESTMENT_SCORE = 'investment_score',
}

export enum TrendPeriod {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

export class MarketHeatMapDto {
  @ApiProperty({ enum: HeatMapType, description: 'Type of heat map data' })
  @IsEnum(HeatMapType)
  type: HeatMapType;

  @ApiPropertyOptional({ description: 'State filter', example: 'QLD' })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({ description: 'Zoom level for data granularity', minimum: 1, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  zoomLevel?: number;
}

export class MarketTrendsDto {
  @ApiPropertyOptional({ description: 'Suburb ID for specific trends' })
  @IsOptional()
  @IsString()
  suburbId?: string;

  @ApiProperty({ enum: TrendPeriod, description: 'Trend period' })
  @IsEnum(TrendPeriod)
  period: TrendPeriod;

  @ApiPropertyOptional({ description: 'Number of periods to include', minimum: 1, maximum: 52 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(52)
  periods?: number;
}

export class EmergingSuburbsDto {
  @ApiPropertyOptional({ description: 'State filter' })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({ description: 'Minimum growth threshold', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minGrowth?: number;

  @ApiPropertyOptional({ description: 'Maximum median price' })
  @IsOptional()
  @IsNumber()
  maxPrice?: number;

  @ApiPropertyOptional({ description: 'Number of results to return', minimum: 1, maximum: 50 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number;
}

export class MarketHeatMapResponseDto {
  @ApiProperty({ description: 'Heat map type' })
  type: HeatMapType;

  @ApiProperty({ description: 'Geographic data points' })
  data: Array<{
    suburbId: string;
    name: string;
    state: string;
    latitude: number;
    longitude: number;
    value: number;
    color: string;
    intensity: number;
  }>;

  @ApiProperty({ description: 'Legend information' })
  legend: {
    min: number;
    max: number;
    unit: string;
    colorScale: Array<{
      value: number;
      color: string;
    }>;
  };

  @ApiProperty({ description: 'Last updated timestamp' })
  lastUpdated: Date;
}

export class MarketTrendsResponseDto {
  @ApiProperty({ description: 'Trend data points' })
  trends: Array<{
    period: string;
    date: Date;
    value: number;
    change: number;
    changePercent: number;
  }>;

  @ApiProperty({ description: 'Summary statistics' })
  summary: {
    currentValue: number;
    periodChange: number;
    periodChangePercent: number;
    volatility: number;
    trend: 'up' | 'down' | 'stable';
  };

  @ApiProperty({ description: 'Forecast data' })
  forecast: Array<{
    period: string;
    predictedValue: number;
    confidence: number;
  }>;
}

export class EmergingSuburbsResponseDto {
  @ApiProperty({ description: 'Emerging suburbs list' })
  suburbs: Array<{
    id: string;
    name: string;
    state: string;
    postcode: string;
    medianPrice: number;
    growthRate: number;
    growthRank: number;
    emergingScore: number;
    keyFactors: string[];
    infrastructure: Array<{
      type: string;
      name: string;
      status: string;
      impact: string;
    }>;
    demographics: {
      population: number;
      medianAge: number;
      medianIncome: number;
    };
  }>;

  @ApiProperty({ description: 'Search criteria used' })
  criteria: {
    state?: string;
    minGrowth?: number;
    maxPrice?: number;
    limit: number;
  };
}

export class MarketMoversResponseDto {
  @ApiProperty({ description: 'Top growing suburbs' })
  topGrowers: Array<{
    suburbId: string;
    name: string;
    state: string;
    growthPercent: number;
    medianPrice: number;
    salesVolume: number;
  }>;

  @ApiProperty({ description: 'Top declining suburbs' })
  topDecliners: Array<{
    suburbId: string;
    name: string;
    state: string;
    declinePercent: number;
    medianPrice: number;
    salesVolume: number;
  }>;

  @ApiProperty({ description: 'Most active suburbs by volume' })
  mostActive: Array<{
    suburbId: string;
    name: string;
    state: string;
    salesVolume: number;
    averagePrice: number;
    daysOnMarket: number;
  }>;
}

export class PriceCycleAnalysisDto {
  @ApiProperty({ description: 'Suburb ID for analysis' })
  @IsString()
  suburbId: string;

  @ApiPropertyOptional({ description: 'Analysis period in years', minimum: 1, maximum: 20 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  years?: number;
}

export class PriceCycleResponseDto {
  @ApiProperty({ description: 'Current cycle position' })
  currentCycle: {
    phase: 'recovery' | 'growth' | 'peak' | 'decline';
    position: number; // 0-100 within phase
    timeInPhase: number; // months
    expectedDuration: number; // months
  };

  @ApiProperty({ description: 'Historical cycles' })
  historicalCycles: Array<{
    startDate: Date;
    endDate: Date;
    phase: string;
    duration: number;
    priceChange: number;
    peakPrice: number;
    troughPrice: number;
  }>;

  @ApiProperty({ description: 'Cycle predictions' })
  predictions: {
    nextPhase: string;
    estimatedTransition: Date;
    confidence: number;
    priceTarget: number;
  };
}
