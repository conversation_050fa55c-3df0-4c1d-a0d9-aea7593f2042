import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { Public } from '../auth/decorators/public.decorator';
import { ApiSuccessResponse, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { MarketService } from './market.service';
import { 
  MarketHeatMapDto, 
  MarketHeatMapResponseDto,
  MarketTrendsDto,
  MarketTrendsResponseDto,
  EmergingSuburbsDto,
  EmergingSuburbsResponseDto,
  MarketMoversResponseDto,
  PriceCycleAnalysisDto,
  PriceCycleResponseDto,
  HeatMapType,
  TrendPeriod 
} from './dto/market-intelligence.dto';

@ApiTags('market')
@Controller('market')
export class MarketController {
  constructor(private readonly marketService: MarketService) {}

  @Post('heat-map')
  @Public()
  @ApiOperation({ summary: 'Get market heat map data for visualization' })
  @ApiSuccessResponse(MarketHeatMapResponseDto, 'Heat map data retrieved successfully')
  @ApiErrorResponse(400, 'Invalid heat map parameters')
  async getHeatMap(@Body() dto: MarketHeatMapDto): Promise<MarketHeatMapResponseDto> {
    return this.marketService.getHeatMap(dto);
  }

  @Post('trends')
  @Public()
  @ApiOperation({ summary: 'Get market trends and forecasts' })
  @ApiSuccessResponse(MarketTrendsResponseDto, 'Market trends retrieved successfully')
  @ApiErrorResponse(404, 'No trend data found')
  async getTrends(@Body() dto: MarketTrendsDto): Promise<MarketTrendsResponseDto> {
    return this.marketService.getTrends(dto);
  }

  @Post('emerging-suburbs')
  @Public()
  @ApiOperation({ summary: 'Find emerging suburbs with growth potential' })
  @ApiSuccessResponse(EmergingSuburbsResponseDto, 'Emerging suburbs identified successfully')
  async getEmergingSuburbs(@Body() dto: EmergingSuburbsDto): Promise<EmergingSuburbsResponseDto> {
    return this.marketService.getEmergingSuburbs(dto);
  }

  @Get('movers')
  @Public()
  @ApiOperation({ summary: 'Get top market movers (gainers, losers, most active)' })
  @ApiSuccessResponse(MarketMoversResponseDto, 'Market movers retrieved successfully')
  async getMarketMovers(): Promise<MarketMoversResponseDto> {
    return this.marketService.getMarketMovers();
  }

  @Get('heat-map-quick')
  @Public()
  @ApiOperation({ summary: 'Quick heat map with query parameters' })
  @ApiQuery({ name: 'type', enum: HeatMapType, description: 'Heat map type' })
  @ApiQuery({ name: 'state', required: false, description: 'State filter' })
  @ApiQuery({ name: 'zoomLevel', required: false, type: Number, description: 'Zoom level' })
  @ApiSuccessResponse(MarketHeatMapResponseDto, 'Heat map data retrieved successfully')
  async getHeatMapQuick(
    @Query('type') type: HeatMapType,
    @Query('state') state?: string,
    @Query('zoomLevel') zoomLevel?: number,
  ): Promise<MarketHeatMapResponseDto> {
    return this.marketService.getHeatMap({
      type,
      state,
      zoomLevel: zoomLevel ? Number(zoomLevel) : undefined,
    });
  }

  @Get('trends-quick')
  @Public()
  @ApiOperation({ summary: 'Quick trends with query parameters' })
  @ApiQuery({ name: 'period', enum: TrendPeriod, description: 'Trend period' })
  @ApiQuery({ name: 'suburbId', required: false, description: 'Suburb ID' })
  @ApiQuery({ name: 'periods', required: false, type: Number, description: 'Number of periods' })
  @ApiSuccessResponse(MarketTrendsResponseDto, 'Market trends retrieved successfully')
  async getTrendsQuick(
    @Query('period') period: TrendPeriod,
    @Query('suburbId') suburbId?: string,
    @Query('periods') periods?: number,
  ): Promise<MarketTrendsResponseDto> {
    return this.marketService.getTrends({
      period,
      suburbId,
      periods: periods ? Number(periods) : undefined,
    });
  }
}
