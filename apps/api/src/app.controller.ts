import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('health')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'API Health Check' })
  @ApiResponse({
    status: 200,
    description: 'API is healthy',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Revalu API is running' },
        version: { type: 'string', example: '1.0.0' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
        environment: { type: 'string', example: 'development' },
      },
    },
  })
  getHealth() {
    return this.appService.getHealth();
  }

  @Get('health')
  @ApiOperation({ summary: 'Detailed Health Check' })
  @ApiResponse({
    status: 200,
    description: 'Detailed health information',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            api: { type: 'string', example: 'healthy' },
            database: { type: 'string', example: 'healthy' },
            uptime: { type: 'number', example: 12345 },
            memory: {
              type: 'object',
              properties: {
                used: { type: 'number', example: 50000000 },
                total: { type: 'number', example: 100000000 },
              },
            },
          },
        },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
      },
    },
  })
  async getDetailedHealth() {
    return this.appService.getDetailedHealth();
  }

  @Get('metrics')
  @ApiOperation({ summary: 'API Metrics' })
  @ApiResponse({
    status: 200,
    description: 'API and database metrics',
  })
  async getMetrics() {
    return this.appService.getMetrics();
  }
}
