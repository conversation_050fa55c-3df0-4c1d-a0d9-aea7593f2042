import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';

// Configuration
import configuration from './config/configuration';

// Common modules
import { DatabaseModule } from './common/database/database.module';

// Feature modules

// Feature modules

import { AuthModule } from './modules/auth/auth.module';
import { PropertiesModule } from './modules/properties/properties.module';
import { SuburbsModule } from './modules/suburbs/suburbs.module';
import { ValuationsModule } from './modules/valuations/valuations.module';

// New modules
import { AlertsModule } from './modules/alerts/alerts.module';
import { AmalgamationModule } from './modules/amalgamation/amalgamation.module';
import { DevelopmentModule } from './modules/development/development.module';
import { MarketModule } from './modules/market/market.module';
import { SubscriptionsModule } from './modules/subscriptions/subscriptions.module';
import { WebhooksModule } from './modules/webhooks/webhooks.module';
import { WebSocketsModule } from './modules/websockets/websockets.module';

import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // Configuration module
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      envFilePath: [
        '.env.local',
        '.env',
        '../../.env', // Root .env file
      ],
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      useFactory: () => ({
        throttlers: [
          {
            ttl: 60000, // 1 minute
            limit: 100, // 100 requests per minute
          },
        ],
      }),
    }),

    // Caching
    CacheModule.register({
      isGlobal: true,
      ttl: 300, // 5 minutes default TTL
      max: 1000, // Maximum number of items in cache
    }),

    // Database
    DatabaseModule,

    // Feature modules
    AuthModule,
    PropertiesModule,
    ValuationsModule,
    SuburbsModule,
    DevelopmentModule,
    AmalgamationModule,
    MarketModule,
    AlertsModule,
    SubscriptionsModule,
    WebhooksModule,
    WebSocketsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    // Global guards
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
