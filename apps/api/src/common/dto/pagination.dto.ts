import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsPositive, IsInt, Min, Max } from 'class-validator';

export class PaginationDto {
  @ApiPropertyOptional({
    description: 'Page number (1-based)',
    minimum: 1,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
    example: 'desc',
  })
  @IsOptional()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class PaginationMetaDto {
  @ApiPropertyOptional({ description: 'Total number of items', example: 100 })
  total: number;

  @ApiPropertyOptional({ description: 'Current page number', example: 1 })
  page: number;

  @ApiPropertyOptional({ description: 'Number of items per page', example: 20 })
  limit: number;

  @ApiPropertyOptional({ description: 'Total number of pages', example: 5 })
  totalPages: number;

  @ApiPropertyOptional({ description: 'Whether there is a next page', example: true })
  hasNext: boolean;

  @ApiPropertyOptional({ description: 'Whether there is a previous page', example: false })
  hasPrev: boolean;
}

export class PaginatedResponseDto<T> {
  @ApiPropertyOptional({ description: 'Success status', example: true })
  success: boolean;

  @ApiPropertyOptional({ description: 'Response data' })
  data: T[];

  @ApiPropertyOptional({ description: 'Pagination metadata' })
  meta: PaginationMetaDto;

  @ApiPropertyOptional({ description: 'Response message', example: 'Data retrieved successfully' })
  message: string;
}
