import { Injectable, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { PrismaClient } from '@revalu/database';
import { prisma } from '@revalu/database';

@Injectable()
export class PrismaService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  // Expose the Prisma client from the database package
  public readonly client: PrismaClient = prisma;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      await this.client.$connect();
      this.logger.log('✅ Connected to database successfully');

      // Log database connection info (without sensitive data)
      const dbUrl = this.configService.get('DATABASE_URL', '');
      const dbHost = dbUrl.match(/@([^:]+)/)?.[1] || 'unknown';
      this.logger.log(`🗄️  Database host: ${dbHost}`);
    } catch (error) {
      this.logger.error('❌ Failed to connect to database:', error);
      this.logger.warn('⚠️  API will start without database connection');
      // Don't throw error - allow API to start without database
    }
  }

  async onModuleDestroy() {
    try {
      await this.client.$disconnect();
      this.logger.log('🔌 Disconnected from database');
    } catch (error) {
      this.logger.error('❌ Error disconnecting from database:', error);
    }
  }

  // Health check method
  async isHealthy(): Promise<boolean> {
    try {
      await this.client.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('❌ Database health check failed:', error);
      return false;
    }
  }

  // Get database metrics
  async getMetrics() {
    try {
      const [userCount, propertyCount, suburbCount, valuationCount] = await Promise.all([
        this.client.user.count(),
        this.client.property.count(),
        this.client.suburb.count(),
        this.client.valuation.count(),
      ]);

      return {
        users: userCount,
        properties: propertyCount,
        suburbs: suburbCount,
        valuations: valuationCount,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('❌ Failed to get database metrics:', error);
      throw error;
    }
  }

  // Transaction helper
  async transaction<T>(fn: (prisma: PrismaClient) => Promise<T>): Promise<T> {
    return this.client.$transaction(fn);
  }

  // Batch operations helper
  async batch(operations: any[]) {
    return this.client.$transaction(operations);
  }

  // Raw query helper with logging
  async queryRaw<T = unknown>(query: string, ...values: any[]): Promise<T> {
    this.logger.debug(`Executing raw query: ${query}`);
    return this.client.$queryRawUnsafe(query, ...values);
  }

  // Execute raw SQL helper
  async executeRaw(query: string, ...values: any[]): Promise<number> {
    this.logger.debug(`Executing raw SQL: ${query}`);
    return this.client.$executeRawUnsafe(query, ...values);
  }
}
