import { applyDecorators, Type } from '@nestjs/common';
import { ApiResponse, getSchemaPath } from '@nestjs/swagger';

export const ApiSuccessResponse = <TModel extends Type<any>>(
  model: TModel,
  description?: string,
) => {
  return applyDecorators(
    ApiResponse({
      status: 200,
      description: description || 'Success',
      schema: {
        properties: {
          success: {
            type: 'boolean',
            example: true,
          },
          data: {
            $ref: getSchemaPath(model),
          },
          message: {
            type: 'string',
            example: 'Operation completed successfully',
          },
        },
      },
    }),
  );
};

export const ApiPaginatedResponse = <TModel extends Type<any>>(
  model: TModel,
  description?: string,
) => {
  return applyDecorators(
    ApiResponse({
      status: 200,
      description: description || 'Paginated results',
      schema: {
        properties: {
          success: {
            type: 'boolean',
            example: true,
          },
          data: {
            type: 'array',
            items: { $ref: getSchemaPath(model) },
          },
          meta: {
            type: 'object',
            properties: {
              total: { type: 'number', example: 100 },
              page: { type: 'number', example: 1 },
              limit: { type: 'number', example: 20 },
              totalPages: { type: 'number', example: 5 },
            },
          },
          message: {
            type: 'string',
            example: 'Data retrieved successfully',
          },
        },
      },
    }),
  );
};

export const ApiErrorResponse = (status: number, description: string) => {
  return applyDecorators(
    ApiResponse({
      status,
      description,
      schema: {
        properties: {
          success: {
            type: 'boolean',
            example: false,
          },
          error: {
            type: 'object',
            properties: {
              code: { type: 'string', example: 'ERROR_CODE' },
              message: { type: 'string', example: 'Error message' },
              details: { type: 'object' },
            },
          },
          timestamp: {
            type: 'string',
            example: '2024-01-01T00:00:00.000Z',
          },
        },
      },
    }),
  );
};
