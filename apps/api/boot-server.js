require('dotenv').config({ path: '../../.env' });

const { spawn } = require('child_process');

console.log('🚀 Booting Revalu API Server...');
console.log('===============================');

// Start the server with proper stdio handling
const server = spawn('node', ['dist/main.js'], {
  stdio: ['inherit', 'pipe', 'pipe'],
  env: { ...process.env }
});

let serverStarted = false;

server.stdout.on('data', (data) => {
  const output = data.toString();
  process.stdout.write(output);
  
  if (output.includes('API is running on')) {
    serverStarted = true;
    console.log('\n✅ Server is ready!');
    console.log('📚 Swagger docs: http://localhost:3001/api/docs');
    console.log('🔐 Login endpoint: http://localhost:3001/api/v1/auth/login');
    console.log('\nPress Ctrl+C to stop the server\n');
  }
});

server.stderr.on('data', (data) => {
  const output = data.toString();
  // Only show non-database errors
  if (!output.includes('Database not available') && !output.includes('PrismaService')) {
    process.stderr.write(output);
  }
});

server.on('error', (error) => {
  console.error('❌ Failed to start server:', error.message);
  process.exit(1);
});

server.on('close', (code) => {
  if (code !== 0 && code !== null) {
    console.log(`\n❌ Server exited with code ${code}`);
    process.exit(code);
  } else {
    console.log('\n🛑 Server stopped');
  }
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  server.kill('SIGTERM');
});

// Keep the process alive
process.stdin.resume();
