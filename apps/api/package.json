{"name": "@revalu/api", "version": "1.0.0", "description": "NestJS API for Revalu property intelligence platform", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "dev": "nest start --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:generate": "cd ../../packages/database && pnpm db:generate", "db:push": "cd ../../packages/database && pnpm db:push", "db:migrate": "cd ../../packages/database && pnpm db:migrate", "db:seed": "cd ../../packages/database && pnpm db:seed"}, "dependencies": {"@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.3.0", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.1.1", "@nestjs/websockets": "^11.1.3", "@revalu/database": "workspace:*", "bcryptjs": "^2.4.3", "cache-manager": "^5.4.0", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "helmet": "^7.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "redis": "^4.6.12", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.8.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/node": "^20.11.5", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.4", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}