require('dotenv').config({ path: '../../.env' });

console.log('🔍 Debugging API Startup Issues');
console.log('================================');

// Check environment variables
console.log('\n📋 Environment Check:');
console.log('NODE_ENV:', process.env.NODE_ENV || 'undefined');
console.log('DATABASE_URL:', process.env.DATABASE_URL ? '✅ Set' : '❌ Not set');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '✅ Set' : '❌ Not set');
console.log('API_PORT:', process.env.API_PORT || 'undefined');

// Test basic Node.js modules
console.log('\n📦 Module Loading Test:');
try {
  console.log('✅ dotenv loaded');

  const { NestFactory } = require('@nestjs/core');
  console.log('✅ @nestjs/core loaded');

  // Check if we have the bundled main.js or separate modules
  const fs = require('fs');
  if (fs.existsSync('./dist/app.module.js')) {
    const { AppModule } = require('./dist/app.module');
    console.log('✅ AppModule loaded (separate file)');
  } else if (fs.existsSync('./dist/main.js')) {
    console.log('✅ Found bundled main.js file');
  } else {
    throw new Error('No compiled files found in dist/');
  }

} catch (error) {
  console.error('❌ Module loading failed:', error.message);
  process.exit(1);
}

// Test database connection
console.log('\n🗄️  Database Connection Test:');
async function testDatabase() {
  try {
    const { prisma } = require('@revalu/database');
    console.log('✅ Database package loaded');
    
    await prisma.$connect();
    console.log('✅ Database connected');
    
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database query successful:', result);
    
    await prisma.$disconnect();
    console.log('✅ Database disconnected');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.log('⚠️  Will continue without database (mock mode)');
  }
}

// Test NestJS application creation by running the bundled main.js
console.log('\n🚀 NestJS Application Test:');
async function testNestApp() {
  try {
    console.log('Starting bundled NestJS application...');

    // Since we have a bundled main.js, let's run it directly
    const { spawn } = require('child_process');

    return new Promise((resolve, reject) => {
      const child = spawn('node', ['dist/main.js'], {
        stdio: 'pipe',
        env: { ...process.env }
      });

      let output = '';
      let errorOutput = '';

      child.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        console.log(text.trim());

        // Check if server started successfully
        if (text.includes('API is running on')) {
          console.log('✅ NestJS application started successfully');
          setTimeout(() => {
            child.kill();
            resolve();
          }, 2000);
        }
      });

      child.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        console.error('STDERR:', text.trim());
      });

      child.on('close', (code) => {
        if (code !== 0 && code !== null) {
          console.error(`❌ Process exited with code ${code}`);
          console.error('Output:', output);
          console.error('Error output:', errorOutput);
          reject(new Error(`Process failed with code ${code}`));
        } else {
          resolve();
        }
      });

      child.on('error', (error) => {
        console.error('❌ Failed to start process:', error.message);
        reject(error);
      });

      // Timeout after 10 seconds
      setTimeout(() => {
        child.kill();
        reject(new Error('Timeout waiting for server to start'));
      }, 10000);
    });
  } catch (error) {
    console.error('❌ NestJS application test failed:', error.message);
    console.error('Stack trace:', error.stack);
    throw error;
  }
}

// Run all tests
async function runDiagnostics() {
  await testDatabase();
  await testNestApp();
}

runDiagnostics();
