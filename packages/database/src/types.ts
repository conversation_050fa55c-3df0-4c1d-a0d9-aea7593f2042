import type {
    ApplicationStatus,
    ApplicationType,
    CrimeStatistic,
    CrimeType,
    DevelopmentApplication,
    MarketTrend,
    Prediction,
    PredictionModel,
    Property,
    PropertyStatus,
    PropertyType,
    Report,
    ReportStatus,
    ReportType,
    SavedProperty,
    School,
    SchoolType,
    Suburb,
    TransportStop,
    TransportType,
    TrendDirection,
    TrendPeriod,
    User,
    UserSearch,
    Valuation,
    ValuationMethod,
    ValuationStatus
} from '@prisma/client';

// Import additional types

// Extended types with relationships
export type PropertyWithRelations = Property & {
  suburb: Suburb;
  valuations: Valuation[];
  predictions: Prediction[];
  savedByUsers: SavedProperty[];
  reports: Report[];
};

export type SuburbWithRelations = Suburb & {
  properties: Property[];
  marketTrends: MarketTrend[];
  developmentApps: DevelopmentApplication[];
  schools: School[];
  transportStops: TransportStop[];
  crimeStatistics: CrimeStatistic[];
};

export type UserWithRelations = User & {
  searches: UserSearch[];
  savedProperties: (SavedProperty & { property: Property })[];
  reports: Report[];
};

export type ReportWithRelations = Report & {
  user: User;
  property?: Property;
};

// Search and filter types
export interface PropertySearchFilters {
  propertyType?: PropertyType[];
  status?: PropertyStatus[];
  minPrice?: number;
  maxPrice?: number;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  minLandSize?: number;
  maxLandSize?: number;
  suburbs?: string[];
  features?: string[];
  yearBuiltFrom?: number;
  yearBuiltTo?: number;
}

export interface SuburbSearchFilters {
  state?: string;
  postcode?: string;
  minPopulation?: number;
  maxPopulation?: number;
  minMedianPrice?: number;
  maxMedianPrice?: number;
}

// Valuation types
export interface ValuationRequest {
  propertyId: string;
  method: ValuationMethod;
  requestedBy?: string;
  notes?: string;
}

export interface ValuationResult {
  estimatedValue: number;
  confidence: number;
  method: ValuationMethod;
  factors: Record<string, any>;
  comparables?: Property[];
}

// Prediction types
export interface PredictionRequest {
  propertyId: string;
  model: PredictionModel;
  targetDate?: Date;
  features: Record<string, any>;
}

export interface PredictionResult {
  predictedValue: number;
  confidence: number;
  model: PredictionModel;
  features: Record<string, any>;
  targetDate?: Date;
}

// Market analysis types
export interface MarketAnalysis {
  suburb: Suburb;
  trends: MarketTrend[];
  averagePrice: number;
  medianPrice: number;
  priceGrowth: number;
  salesVolume: number;
  daysOnMarket: number;
  priceDistribution: {
    range: string;
    count: number;
    percentage: number;
  }[];
}

// Report generation types
export interface ReportParameters {
  propertyIds?: string[];
  suburbIds?: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
  includeComparables?: boolean;
  includeMarketTrends?: boolean;
  includeAmenities?: boolean;
  customSections?: string[];
}

// Database operation types
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Extended types for new entities (using any for now until Prisma generates the types)
export type PropertyIntelligenceWithProperty = any & {
  property: Property;
};

export type AlertWithRelations = any & {
  user: User;
  property?: Property;
  suburb?: Suburb;
  history: any[];
};

export type ComparisonWithUser = any & {
  user: User;
};

export type AmalgamationWithUser = any & {
  user: User;
};

export type DataSourceWithWebhooks = any & {
  webhookData: any[];
};

export type InfrastructureProjectWithSuburb = any & {
  suburb: Suburb;
};

// Export all Prisma types
export type {
    ApplicationStatus,
    ApplicationType, CrimeStatistic, CrimeType, DevelopmentApplication, MarketTrend, Prediction, PredictionModel, Property, PropertyStatus, PropertyType, Report, ReportStatus, ReportType, SavedProperty, School, SchoolType, Suburb, TransportStop, TransportType, TrendDirection,
    TrendPeriod, User, UserSearch, Valuation, ValuationMethod,
    ValuationStatus
};

