import { Prisma } from '@prisma/client';
import type { PaginationOptions, PropertySearchFilters, SearchResult, SuburbSearchFilters } from './types';

/**
 * Create pagination options for Prisma queries
 */
export function createPaginationOptions(options: PaginationOptions = {}) {
  const page = Math.max(1, options.page || 1);
  const limit = Math.min(100, Math.max(1, options.limit || 20));
  const skip = (page - 1) * limit;

  return {
    skip,
    take: limit,
    page,
    limit,
  };
}

/**
 * Create a paginated search result
 */
export function createSearchResult<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): SearchResult<T> {
  return {
    data,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
}

/**
 * Build property search where clause from filters
 */
export function buildPropertySearchWhere(filters: PropertySearchFilters): Prisma.PropertyWhereInput {
  const where: Prisma.PropertyWhereInput = {};

  if (filters.propertyType?.length) {
    where.propertyType = { in: filters.propertyType };
  }

  if (filters.status?.length) {
    where.status = { in: filters.status };
  }

  if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
    where.lastSalePrice = {};
    if (filters.minPrice !== undefined) {
      where.lastSalePrice.gte = filters.minPrice;
    }
    if (filters.maxPrice !== undefined) {
      where.lastSalePrice.lte = filters.maxPrice;
    }
  }

  if (filters.minBedrooms !== undefined || filters.maxBedrooms !== undefined) {
    where.bedrooms = {};
    if (filters.minBedrooms !== undefined) {
      where.bedrooms.gte = filters.minBedrooms;
    }
    if (filters.maxBedrooms !== undefined) {
      where.bedrooms.lte = filters.maxBedrooms;
    }
  }

  if (filters.minBathrooms !== undefined || filters.maxBathrooms !== undefined) {
    where.bathrooms = {};
    if (filters.minBathrooms !== undefined) {
      where.bathrooms.gte = filters.minBathrooms;
    }
    if (filters.maxBathrooms !== undefined) {
      where.bathrooms.lte = filters.maxBathrooms;
    }
  }

  if (filters.minLandSize !== undefined || filters.maxLandSize !== undefined) {
    where.landSize = {};
    if (filters.minLandSize !== undefined) {
      where.landSize.gte = filters.minLandSize;
    }
    if (filters.maxLandSize !== undefined) {
      where.landSize.lte = filters.maxLandSize;
    }
  }

  if (filters.suburbs?.length) {
    where.suburb = {
      id: { in: filters.suburbs },
    };
  }

  if (filters.features?.length) {
    where.features = {
      hasEvery: filters.features,
    };
  }

  if (filters.yearBuiltFrom !== undefined || filters.yearBuiltTo !== undefined) {
    where.yearBuilt = {};
    if (filters.yearBuiltFrom !== undefined) {
      where.yearBuilt.gte = filters.yearBuiltFrom;
    }
    if (filters.yearBuiltTo !== undefined) {
      where.yearBuilt.lte = filters.yearBuiltTo;
    }
  }

  return where;
}

/**
 * Build suburb search where clause from filters
 */
export function buildSuburbSearchWhere(filters: SuburbSearchFilters): Prisma.SuburbWhereInput {
  const where: Prisma.SuburbWhereInput = {};

  if (filters.state) {
    where.state = { equals: filters.state, mode: 'insensitive' };
  }

  if (filters.postcode) {
    where.postcode = filters.postcode;
  }

  if (filters.minPopulation !== undefined || filters.maxPopulation !== undefined) {
    where.population = {};
    if (filters.minPopulation !== undefined) {
      where.population.gte = filters.minPopulation;
    }
    if (filters.maxPopulation !== undefined) {
      where.population.lte = filters.maxPopulation;
    }
  }

  if (filters.minMedianPrice !== undefined || filters.maxMedianPrice !== undefined) {
    where.medianPrice = {};
    if (filters.minMedianPrice !== undefined) {
      where.medianPrice.gte = filters.minMedianPrice;
    }
    if (filters.maxMedianPrice !== undefined) {
      where.medianPrice.lte = filters.maxMedianPrice;
    }
  }

  return where;
}

/**
 * Calculate distance between two coordinates using Haversine formula
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Generate a unique property reference
 */
export function generatePropertyReference(suburb: string, address: string): string {
  const cleanSuburb = suburb.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  const cleanAddress = address.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  const timestamp = Date.now().toString(36);
  return `${cleanSuburb}-${cleanAddress.slice(0, 8)}-${timestamp}`;
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number, currency = 'AUD'): string {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Format percentage for display
 */
export function formatPercentage(value: number, decimals = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Validate Australian postcode
 */
export function isValidAustralianPostcode(postcode: string): boolean {
  return /^[0-9]{4}$/.test(postcode);
}

/**
 * Get property type display name
 */
export function getPropertyTypeDisplayName(type: string): string {
  const displayNames: Record<string, string> = {
    HOUSE: 'House',
    APARTMENT: 'Apartment',
    TOWNHOUSE: 'Townhouse',
    UNIT: 'Unit',
    VILLA: 'Villa',
    DUPLEX: 'Duplex',
    STUDIO: 'Studio',
    LAND: 'Land',
    COMMERCIAL: 'Commercial',
    INDUSTRIAL: 'Industrial',
    OTHER: 'Other',
  };
  return displayNames[type] || type;
}

/**
 * Create database connection URL
 */
export function createDatabaseUrl(config: {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}): string {
  const { host, port, database, username, password, ssl = false } = config;
  const sslParam = ssl ? '?sslmode=require' : '';
  return `postgresql://${username}:${password}@${host}:${port}/${database}${sslParam}`;
}
