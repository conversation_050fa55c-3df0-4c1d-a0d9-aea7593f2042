# @revalu/database

Database schemas, migrations, and ORM configuration for the Revalu property intelligence platform.

## Overview

This package contains the Prisma schema and database utilities for managing property data, valuations, market trends, and user interactions.

## Database Schema

The schema includes the following main entities:

### Core Entities
- **Properties** - Property listings with details, location, and features
- **Valuations** - Property valuations with different methods and confidence scores
- **Predictions** - ML-based property value predictions
- **Market Trends** - Historical and current market data by suburb
- **Suburbs** - Geographic areas with demographic and market data

### Supporting Entities
- **Development Applications** - Planning and development applications
- **Schools** - Educational institutions and ratings
- **Transport Stops** - Public transport accessibility
- **Crime Statistics** - Safety and crime data by area

### User Interaction
- **Users** - User accounts and profiles
- **User Searches** - Search history and analytics
- **Saved Properties** - User bookmarks and notes
- **Reports** - Generated property and market reports

## Setup

1. **Install dependencies**
   ```bash
   pnpm install
   ```

2. **Set up environment variables**
   ```bash
   # Add to your .env file
   DATABASE_URL="postgresql://username:password@localhost:5432/revalu_db"
   ```

3. **Generate Prisma client**
   ```bash
   pnpm db:generate
   ```

4. **Push schema to database**
   ```bash
   pnpm db:push
   ```

5. **Run migrations (production)**
   ```bash
   pnpm db:migrate
   ```

## Development

### Available Scripts

```bash
# Generate Prisma client
pnpm db:generate

# Push schema changes to database (development)
pnpm db:push

# Create and run migrations
pnpm db:migrate

# Deploy migrations (production)
pnpm db:migrate:deploy

# Reset database and run migrations
pnpm db:migrate:reset

# Open Prisma Studio
pnpm db:studio

# Seed database with sample data
pnpm db:seed

# Format Prisma schema
pnpm db:format

# Build TypeScript
pnpm build

# Watch mode for development
pnpm dev
```

### Database Migrations

For production deployments, always use migrations:

```bash
# Create a new migration
npx prisma migrate dev --name add_new_feature

# Deploy migrations to production
npx prisma migrate deploy
```

### Seeding

The database includes a seed script with sample data:

```bash
pnpm db:seed
```

This creates:
- Sample suburbs (Toorak, Bondi, Paddington)
- Sample properties with realistic data
- Schools and transport stops
- Crime statistics
- Demo user and saved properties

## Usage

### Basic Usage

```typescript
import { prisma } from '@revalu/database';

// Find properties in a suburb
const properties = await prisma.property.findMany({
  where: {
    suburb: {
      name: 'Toorak',
      state: 'VIC'
    }
  },
  include: {
    suburb: true,
    valuations: true
  }
});

// Create a new valuation
const valuation = await prisma.valuation.create({
  data: {
    propertyId: 'property-id',
    estimatedValue: 2500000,
    confidence: 0.85,
    method: 'AUTOMATED',
    status: 'COMPLETED'
  }
});
```

### Using Utility Functions

```typescript
import { 
  buildPropertySearchWhere, 
  createPaginationOptions,
  formatCurrency 
} from '@revalu/database';

// Build search filters
const where = buildPropertySearchWhere({
  propertyType: ['HOUSE', 'APARTMENT'],
  minPrice: 500000,
  maxPrice: 2000000,
  minBedrooms: 2
});

// Create pagination
const pagination = createPaginationOptions({ page: 1, limit: 20 });

// Search properties
const properties = await prisma.property.findMany({
  where,
  ...pagination,
  include: { suburb: true }
});

// Format price for display
const formattedPrice = formatCurrency(1500000); // "$1,500,000"
```

### Type Safety

The package exports all Prisma types and custom extended types:

```typescript
import type { 
  Property, 
  PropertyWithRelations,
  PropertySearchFilters,
  ValuationResult 
} from '@revalu/database';
```

## Schema Design

### Indexes

The schema includes comprehensive indexes for:
- Geographic queries (latitude/longitude)
- Property searches (type, price, bedrooms)
- Time-based queries (dates, trends)
- User interactions (searches, saved properties)

### Relationships

- Properties belong to suburbs
- Valuations and predictions belong to properties
- Market trends are tracked by suburb
- Users can save properties and generate reports
- All entities include proper foreign key relationships

### Data Types

- **Geographic**: Latitude/longitude as Float
- **Prices**: Float for monetary values
- **Features**: String arrays for property features
- **Metadata**: JSON fields for flexible data storage
- **Enums**: Strongly typed status and category fields

## Performance Considerations

- Use appropriate indexes for your query patterns
- Consider connection pooling for high-traffic applications
- Use `select` and `include` to limit data fetching
- Implement pagination for large result sets
- Use database-level constraints for data integrity

## Contributing

When modifying the schema:

1. Update the Prisma schema file
2. Create a migration: `pnpm db:migrate dev --name description`
3. Update TypeScript types if needed
4. Update seed data if applicable
5. Test with `pnpm db:seed`
