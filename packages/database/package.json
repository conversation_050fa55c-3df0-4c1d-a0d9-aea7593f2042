{"name": "@revalu/database", "version": "1.0.0", "description": "Database schemas, migrations, and ORM configuration for Revalu", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:studio": "prisma studio", "db:seed": "tsx src/seed.ts", "db:format": "prisma format"}, "devDependencies": {"@prisma/client": "^6.8.2", "@types/node": "^20.11.5", "prisma": "^6.8.2", "tsx": "^4.7.0", "typescript": "^5.3.3"}, "prisma": {"schema": "prisma/schema.prisma"}}